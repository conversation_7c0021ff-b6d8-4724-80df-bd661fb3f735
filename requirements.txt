# Core dependencies
tkinter-tooltip==2.2.0
Pillow==10.0.0
opencv-python==********
numpy==1.24.3
requests==2.31.0
python-dotenv==1.0.0

# Video processing
moviepy==1.0.3
imageio==2.31.1
imageio-ffmpeg==0.4.8

# Audio processing
pydub==0.25.1
SpeechRecognition==3.10.0
pyaudio==0.2.11
librosa==0.10.1

# AI and ML APIs
transformers==4.33.2
torch==2.0.1
google-cloud-videointelligence==2.11.1
google-cloud-speech==2.21.0
google-cloud-translate==3.11.3

# Advanced AI libraries (for enhanced features)
mediapipe>=0.10.0    # Advanced pose and face detection
deepface>=0.0.79     # Advanced emotion analysis
py-feat>=0.5.0       # Action Units analysis
librosa>=0.10.1      # Advanced audio analysis

# Object Detection and Computer Vision
ultralytics>=8.0.0   # YOLOv8 for real-time object detection
tensorflow>=2.13.0   # TensorFlow for advanced ML models
tensorflow-object-detection-api>=0.1.1  # TensorFlow Object Detection API
detectron2>=0.6      # Facebook's advanced detection framework
pytorchvideo>=0.1.5  # PyTorch video understanding

# YouTube downloading
yt-dlp>=2023.7.6     # YouTube downloader

# HTTP and API clients
httpx==0.24.1
aiohttp==3.8.5

# GUI enhancements
customtkinter==5.2.0
tkinter-dnd2==0.3.0

# File handling
pathlib2==2.3.7
send2trash==1.8.2

# Configuration and logging
pyyaml==6.0.1
colorlog==6.7.0

# Progress bars and UI
tqdm==4.66.1
progressbar2==4.2.0

# Image processing
scikit-image==0.21.0

# Utilities
python-magic==0.4.27
psutil==5.9.5
packaging==23.1

# Development and testing (optional)
pytest==7.4.0
pytest-asyncio==0.21.1
black==23.7.0
flake8==6.0.0

# Social media APIs (for publishing features)
tweepy==4.14.0
facebook-sdk==3.1.0

# Subtitle and caption handling
pysrt==1.1.2
webvtt-py==0.4.6

# Video metadata
mutagen==1.47.0
pymediainfo==6.0.1
