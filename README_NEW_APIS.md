# 🆕 دليل الخدمات الجديدة - New AI Services Guide

## 📋 نظرة عامة

تم إضافة دعم لخدمات ذكاء اصطناعي جديدة ومجانية لتحسين قدرات تحليل الفيديو:

### 🆓 الخدمات المجانية الجديدة

1. **Amazon Rekognition Video** - تحليل الفيديو والوجوه
2. **Microsoft Azure Computer Vision** - تحليل الصور والنصوص  
3. **Microsoft Azure Video Indexer** - فهرسة وتحليل الفيديو
4. **Clarifai AI Platform** - تحليل شامل للمحتوى

---

## 🚀 التثبيت السريع

### 1. تثبيت المكتبات الجديدة

```bash
# تثبيت تلقائي لجميع المكتبات
python install_new_apis.py

# أو تثبيت يدوي
pip install -r requirements_new_apis.txt
```

### 2. تكوين مفاتيح API

```bash
# فحص حالة المفاتيح الحالية
python api_keys_manager.py status

# عرض دليل الإعداد
python api_keys_manager.py setup

# عرض الخدمات المجانية
python api_keys_manager.py free
```

### 3. اختبار النظام

```bash
# فحص شامل للنظام
python api_keys_manager.py check

# اختبار المكتبات الجديدة
python test_new_apis.py

# فحص مفاتيح Gemini المحسن
python check_gemini_enhanced.py
```

---

## 🔑 إعداد مفاتيح API

### 🤗 Hugging Face (مجاني - 30,000 طلب/شهر)

1. اذهب إلى: https://huggingface.co/settings/tokens
2. أنشئ مفتاح API جديد
3. أضف في ملف `.env`:

```env
HUGGINGFACE_API_KEY=hf_your_token_here
```

### 🔶 Amazon Rekognition (مجاني للسنة الأولى)

1. أنشئ حساب AWS: https://aws.amazon.com/
2. احصل على Access Key و Secret Key
3. أضف في ملف `.env`:

```env
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1
AWS_REKOGNITION_ENABLED=true
```

**الحدود المجانية:**
- 5,000 صورة شهرياً للسنة الأولى
- تحليل الفيديو بالدفع حسب الاستخدام

### 🔷 Microsoft Azure Computer Vision (مجاني مدى الحياة)

1. أنشئ حساب Azure: https://azure.microsoft.com/
2. أنشئ Computer Vision resource
3. أضف في ملف `.env`:

```env
AZURE_COMPUTER_VISION_ENDPOINT=https://your-resource.cognitiveservices.azure.com/
AZURE_COMPUTER_VISION_KEY=your_key_here
AZURE_ENABLED=true
```

**الحدود المجانية:**
- 5,000 طلب شهرياً مدى الحياة
- تحليل الصور والنصوص

### 🔷 Microsoft Azure Video Indexer

```env
AZURE_VIDEO_INDEXER_ACCOUNT_ID=your_account_id
AZURE_VIDEO_INDEXER_API_KEY=your_api_key
AZURE_VIDEO_INDEXER_LOCATION=trial
```

### 🎯 Clarifai (مجاني - 5,000 عملية/شهر)

1. أنشئ حساب: https://clarifai.com/
2. أنشئ تطبيق جديد
3. أضف في ملف `.env`:

```env
CLARIFAI_API_KEY=your_api_key_here
CLARIFAI_USER_ID=your_user_id_here
CLARIFAI_APP_ID=your_app_id_here
CLARIFAI_ENABLED=true
```

---

## 🛠️ الاستخدام

### 📊 فحص حالة النظام

```python
from src.config.api_keys import api_keys_manager

# عرض الخدمات المتاحة
available = api_keys_manager.get_available_services()
print(f"الخدمات المتاحة: {len(available)}")

# عرض الخدمات المجانية
free_services = api_keys_manager.get_free_services()
print(f"الخدمات المجانية: {len(free_services)}")

# الحصول على توصيات الاستخدام
recommendations = api_keys_manager.get_smart_usage_recommendations()
```

### 🔍 استخدام AWS Rekognition

```python
from src.ai.aws_rekognition_client import AWSRekognitionClient

client = AWSRekognitionClient()

if client.is_available():
    # كشف الوجوه
    faces = client.detect_faces_in_image("image.jpg")
    
    # كشف الكائنات
    labels = client.detect_labels_in_image("image.jpg")
    
    # كشف النصوص
    texts = client.detect_text_in_image("image.jpg")
    
    # فحص المحتوى غير اللائق
    moderation = client.detect_moderation_labels("image.jpg")
```

### 🔍 استخدام Azure Computer Vision

```python
from src.ai.azure_client import AzureComputerVisionClient

client = AzureComputerVisionClient()

if client.is_available():
    # تحليل شامل للصورة
    analysis = client.analyze_image("image.jpg")
    
    # استخراج النص (OCR)
    text = client.extract_text_from_image("image.jpg")
```

### 🔍 استخدام Clarifai

```python
from src.ai.clarifai_client import ClarifaiClient

client = ClarifaiClient()

if client.is_available():
    # التنبؤ العام
    concepts = client.predict_image_general("image.jpg")
    
    # كشف المحتوى غير اللائق
    nsfw = client.detect_nsfw_content("image.jpg")
    
    # كشف الوجوه
    faces = client.detect_faces("image.jpg")
    
    # تحليل الديموغرافيا
    demographics = client.analyze_demographics("image.jpg")
```

---

## 🧠 الاستخدام الذكي والمجاني

### 💡 نصائح لتوفير التكلفة

1. **استخدم الخدمات المجانية أولاً**
   - Hugging Face للنصوص والترجمة
   - Azure Computer Vision للصور
   - Clarifai للتحليل الشامل

2. **وزع الاستخدام**
   - استخدم خدمات مختلفة لمهام مختلفة
   - لا تعتمد على خدمة واحدة فقط

3. **راقب الحدود**
   - استخدم أداة المراقبة المدمجة
   - تحقق من الاستخدام الشهري

### 📊 مراقبة الاستخدام

```bash
# فحص دوري للحالة
python api_keys_manager.py status

# عرض التوصيات
python api_keys_manager.py recommend

# فحص شامل
python api_keys_manager.py check
```

---

## 🔧 استكشاف الأخطاء

### ❌ مشاكل شائعة

1. **مفاتيح API غير صالحة**
   ```bash
   python check_gemini_enhanced.py
   python api_keys_manager.py status
   ```

2. **مكتبات غير مثبتة**
   ```bash
   python install_new_apis.py
   pip install -r requirements_new_apis.txt
   ```

3. **مشاكل الشبكة**
   - تحقق من الاتصال بالإنترنت
   - تحقق من إعدادات الجدار الناري

### 🔍 أدوات التشخيص

```bash
# فحص شامل للنظام
python api_keys_manager.py check

# اختبار المكتبات
python test_new_apis.py

# فحص مفاتيح Gemini
python check_gemini_enhanced.py --save
```

---

## 📈 الميزات الجديدة

### 🔄 التبديل التلقائي بين الخدمات
- النظام يختار أفضل خدمة متاحة تلقائياً
- يوزع الحمل بين الخدمات المختلفة
- يتجنب الخدمات التي وصلت لحدودها

### 📊 مراقبة الاستخدام
- تتبع عدد الطلبات لكل خدمة
- تحذيرات عند الاقتراب من الحدود
- تقارير شهرية مفصلة

### 🛡️ الأمان المحسن
- تشفير مفاتيح API
- إخفاء المفاتيح في السجلات
- فحص دوري لصحة المفاتيح

---

## 📞 الدعم

إذا واجهت أي مشاكل:

1. شغّل أدوات التشخيص
2. تحقق من ملف السجلات: `logs/app.log`
3. راجع دليل استكشاف الأخطاء
4. تأكد من تحديث المكتبات

---

## 🎉 الخلاصة

الآن لديك وصول إلى:
- ✅ 4 خدمات ذكاء اصطناعي جديدة
- ✅ أدوات إدارة ومراقبة متقدمة  
- ✅ استخدام ذكي ومجاني
- ✅ فحص وتشخيص شامل

**ابدأ الآن:** `python api_keys_manager.py setup`
