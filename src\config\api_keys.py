"""
إدارة مفاتيح APIs الجديدة
New API Keys Management
"""

import os
from typing import Dict, Optional, Any
from pathlib import Path
from dotenv import load_dotenv
import logging

logger = logging.getLogger(__name__)

# تحميل متغيرات البيئة
load_env_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(load_env_path)

class APIKeysManager:
    """مدير مفاتيح APIs الجديدة"""
    
    def __init__(self):
        self.api_keys = self._load_api_keys()
        self.validation_results = {}
    
    def _load_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """تحميل جميع مفاتيح API من متغيرات البيئة"""
        return {
            'huggingface': {
                'api_key': os.getenv('HUGGINGFACE_API_KEY'),
                'enabled': bool(os.getenv('HUGGINGFACE_API_KEY')),
                'free_tier': True,
                'monthly_limit': 30000,
                'description': 'Hugging Face Inference API'
            },
            'aws_rekognition': {
                'access_key': os.getenv('AWS_ACCESS_KEY_ID'),
                'secret_key': os.getenv('AWS_SECRET_ACCESS_KEY'),
                'region': os.getenv('AWS_REGION', 'us-east-1'),
                'enabled': os.getenv('AWS_REKOGNITION_ENABLED', 'false').lower() == 'true',
                'free_tier': True,
                'monthly_limit': 5000,  # صور
                'description': 'Amazon Rekognition Video'
            },
            'azure_cv': {
                'endpoint': os.getenv('AZURE_COMPUTER_VISION_ENDPOINT'),
                'api_key': os.getenv('AZURE_COMPUTER_VISION_KEY'),
                'enabled': os.getenv('AZURE_ENABLED', 'false').lower() == 'true',
                'free_tier': True,
                'monthly_limit': 5000,
                'description': 'Microsoft Azure Computer Vision'
            },
            'azure_video_indexer': {
                'account_id': os.getenv('AZURE_VIDEO_INDEXER_ACCOUNT_ID'),
                'api_key': os.getenv('AZURE_VIDEO_INDEXER_API_KEY'),
                'location': os.getenv('AZURE_VIDEO_INDEXER_LOCATION', 'trial'),
                'enabled': os.getenv('AZURE_ENABLED', 'false').lower() == 'true',
                'free_tier': True,
                'description': 'Microsoft Azure Video Indexer'
            },
            'clarifai': {
                'api_key': os.getenv('CLARIFAI_API_KEY'),
                'user_id': os.getenv('CLARIFAI_USER_ID'),
                'app_id': os.getenv('CLARIFAI_APP_ID'),
                'enabled': os.getenv('CLARIFAI_ENABLED', 'false').lower() == 'true',
                'free_tier': True,
                'monthly_limit': 5000,
                'description': 'Clarifai AI Platform'
            },
            'google_cloud': {
                'project_id': os.getenv('GOOGLE_CLOUD_PROJECT_ID'),
                'credentials_path': os.getenv('GOOGLE_CLOUD_CREDENTIALS_PATH'),
                'video_api_key': os.getenv('GOOGLE_CLOUD_VIDEO_INTELLIGENCE_API_KEY'),
                'speech_api_key': os.getenv('GOOGLE_CLOUD_SPEECH_API_KEY'),
                'enabled': bool(os.getenv('GOOGLE_CLOUD_PROJECT_ID')),
                'free_tier': False,  # يحتاج بطاقة ائتمان
                'description': 'Google Cloud Video Intelligence'
            },
            'gemini': {
                'api_key': os.getenv('GOOGLE_GEMINI_API_KEY'),
                'enabled': bool(os.getenv('GOOGLE_GEMINI_API_KEY')),
                'free_tier': True,
                'description': 'Google Gemini API'
            }
        }
    
    def get_available_services(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على الخدمات المتاحة"""
        available = {}
        for service_name, config in self.api_keys.items():
            if self._is_service_configured(service_name):
                available[service_name] = config
        return available
    
    def get_free_services(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على الخدمات المجانية المتاحة"""
        free_services = {}
        for service_name, config in self.api_keys.items():
            if config.get('free_tier', False) and self._is_service_configured(service_name):
                free_services[service_name] = config
        return free_services
    
    def _is_service_configured(self, service_name: str) -> bool:
        """التحقق من تكوين الخدمة"""
        config = self.api_keys.get(service_name, {})
        
        if service_name == 'huggingface':
            return bool(config.get('api_key'))
        
        elif service_name == 'aws_rekognition':
            return bool(config.get('access_key') and config.get('secret_key'))
        
        elif service_name == 'azure_cv':
            return bool(config.get('endpoint') and config.get('api_key'))
        
        elif service_name == 'azure_video_indexer':
            return bool(config.get('account_id') and config.get('api_key'))
        
        elif service_name == 'clarifai':
            return bool(config.get('api_key') and config.get('user_id') and config.get('app_id'))
        
        elif service_name == 'google_cloud':
            return bool(config.get('project_id') and 
                       (config.get('credentials_path') or config.get('video_api_key')))
        
        elif service_name == 'gemini':
            return bool(config.get('api_key'))
        
        return False
    
    def validate_api_keys(self) -> Dict[str, Any]:
        """التحقق من صحة جميع مفاتيح API"""
        results = {
            'total_services': len(self.api_keys),
            'configured_services': 0,
            'free_services': 0,
            'enabled_services': 0,
            'services_status': {}
        }
        
        for service_name, config in self.api_keys.items():
            is_configured = self._is_service_configured(service_name)
            is_enabled = config.get('enabled', False)
            is_free = config.get('free_tier', False)
            
            if is_configured:
                results['configured_services'] += 1
            if is_free and is_configured:
                results['free_services'] += 1
            if is_enabled and is_configured:
                results['enabled_services'] += 1
            
            results['services_status'][service_name] = {
                'configured': is_configured,
                'enabled': is_enabled,
                'free_tier': is_free,
                'description': config.get('description', ''),
                'monthly_limit': config.get('monthly_limit', 'غير محدود')
            }
        
        return results
    
    def get_service_config(self, service_name: str) -> Optional[Dict[str, Any]]:
        """الحصول على تكوين خدمة معينة"""
        return self.api_keys.get(service_name)
    
    def is_service_available(self, service_name: str) -> bool:
        """التحقق من توفر خدمة معينة"""
        config = self.api_keys.get(service_name, {})
        return (self._is_service_configured(service_name) and 
                config.get('enabled', False))
    
    def get_smart_usage_recommendations(self) -> Dict[str, str]:
        """توصيات للاستخدام الذكي والمجاني"""
        recommendations = {}
        
        # Hugging Face
        if self.is_service_available('huggingface'):
            recommendations['huggingface'] = (
                "استخدم للنصوص والترجمة والتحليل العاطفي. "
                "30,000 طلب مجاني شهرياً."
            )
        
        # AWS Rekognition
        if self.is_service_available('aws_rekognition'):
            recommendations['aws_rekognition'] = (
                "مثالي لتحليل الوجوه والأشياء في الفيديو. "
                "5,000 صورة مجانية شهرياً في السنة الأولى."
            )
        
        # Azure Computer Vision
        if self.is_service_available('azure_cv'):
            recommendations['azure_cv'] = (
                "ممتاز لتحليل المشاهد والنصوص في الفيديو. "
                "5,000 طلب مجاني شهرياً مدى الحياة."
            )
        
        # Clarifai
        if self.is_service_available('clarifai'):
            recommendations['clarifai'] = (
                "شامل لتحليل المحتوى وكشف NSFW. "
                "5,000 عملية مجانية شهرياً."
            )
        
        return recommendations

# إنشاء مثيل عام
api_keys_manager = APIKeysManager()
