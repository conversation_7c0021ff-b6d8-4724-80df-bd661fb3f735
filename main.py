#!/usr/bin/env python3
"""
Video Editor Pro - أداة تحرير الفيديو المتكاملة والمجانية
الملف الرئيسي لتشغيل التطبيق

Main entry point for Video Editor Pro application
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    import tkinter as tk
    from tkinter import messagebox
except ImportError:
    print("خطأ: لا يمكن استيراد tkinter. تأكد من تثبيت Python مع دعم tkinter.")
    print("Error: Cannot import tkinter. Make sure Python is installed with tkinter support.")
    sys.exit(1)

# استيراد الإعدادات
try:
    from config.settings import AppSettings, LoggingSettings
except ImportError as e:
    print(f"خطأ في استيراد الإعدادات: {e}")
    print(f"Settings import error: {e}")
    sys.exit(1)

def setup_logging():
    """إعداد نظام السجلات"""
    try:
        logging.basicConfig(
            level=getattr(logging, LoggingSettings.LOG_LEVEL),
            format=LoggingSettings.LOG_FORMAT,
            handlers=[
                logging.FileHandler(LoggingSettings.LOG_FILE, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        logger = logging.getLogger(__name__)
        logger.info("تم تشغيل نظام السجلات بنجاح")
        logger.info("Logging system initialized successfully")
        return logger
    except Exception as e:
        print(f"خطأ في إعداد السجلات: {e}")
        print(f"Logging setup error: {e}")
        return None

def check_dependencies():
    """فحص المتطلبات الأساسية"""
    missing_deps = []
    
    # فحص المكتبات المطلوبة
    required_modules = [
        'PIL', 'cv2', 'numpy', 'requests', 
        'moviepy', 'pydub', 'transformers'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_deps.append(module)
    
    # فحص FFmpeg
    import subprocess
    try:
        subprocess.run([AppSettings.FFMPEG_PATH, '-version'], 
                      capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        missing_deps.append('FFmpeg')
    
    return missing_deps

def check_gemini_keys():
    """فحص حالة مفاتيح Gemini API"""
    try:
        from ai.gemini_key_manager import gemini_key_manager

        # الحصول على ملخص حالة المفاتيح
        status_summary = gemini_key_manager.get_keys_status_summary()

        total_keys = status_summary['total_keys']
        available_keys = status_summary['available_keys']
        blocked_keys = status_summary['blocked_keys']

        # إذا لم تكن هناك مفاتيح متاحة
        if available_keys == 0 and total_keys > 0:
            # محاولة إعادة تفعيل المفاتيح القديمة
            reset_count = gemini_key_manager.reset_old_keys(hours_threshold=23.5)

            if reset_count > 0:
                # تحديث الحالة بعد إعادة التفعيل
                status_summary = gemini_key_manager.get_keys_status_summary()
                available_keys = status_summary['available_keys']

                return {
                    'status': 'recovered',
                    'message': f"تم إعادة تفعيل {reset_count} مفتاح Gemini تلقائياً",
                    'available_keys': available_keys,
                    'total_keys': total_keys
                }
            else:
                # حساب وقت توفر المفتاح التالي
                next_available = gemini_key_manager._get_next_key_available_time()

                return {
                    'status': 'blocked',
                    'message': f"جميع مفاتيح Gemini محظورة مؤقتاً ({blocked_keys}/{total_keys})",
                    'next_available': next_available,
                    'available_keys': 0,
                    'total_keys': total_keys
                }

        elif available_keys > 0:
            return {
                'status': 'ok',
                'message': f"مفاتيح Gemini تعمل بشكل طبيعي ({available_keys}/{total_keys} متاح)",
                'available_keys': available_keys,
                'total_keys': total_keys
            }

        else:
            return {
                'status': 'no_keys',
                'message': "لا توجد مفاتيح Gemini مُعرفة في النظام",
                'available_keys': 0,
                'total_keys': 0
            }

    except ImportError:
        return {
            'status': 'import_error',
            'message': "خطأ في استيراد مدير مفاتيح Gemini",
            'available_keys': 0,
            'total_keys': 0
        }
    except Exception as e:
        error_msg = str(e)
        # تقصير رسالة الخطأ إذا كانت طويلة
        if len(error_msg) > 100:
            error_msg = error_msg[:100] + "..."

        return {
            'status': 'error',
            'message': f"خطأ في فحص مفاتيح Gemini: {error_msg}",
            'available_keys': 0,
            'total_keys': 0
        }

def show_gemini_status_message(gemini_status, logger=None):
    """عرض رسالة حالة مفاتيح Gemini"""
    status = gemini_status['status']
    message = gemini_status['message']

    if status == 'ok':
        # مفاتيح تعمل بشكل طبيعي - رسالة معلوماتية بسيطة
        if logger:
            logger.info(f"✅ Gemini: {message}")

        # لا نعرض رسالة منبثقة للحالة العادية لتجنب الإزعاج
        print(f"✅ Gemini: {message}")

    elif status == 'recovered':
        # تم إعادة تفعيل المفاتيح - رسالة مهمة
        if logger:
            logger.info(f"🔄 Gemini: {message}")

        messagebox.showinfo(
            "تم إصلاح مفاتيح Gemini - Gemini Keys Recovered",
            f"🔄 {message}\n\nالنظام جاهز للاستخدام مع جميع الميزات المتقدمة."
        )

    elif status == 'blocked':
        # جميع المفاتيح محظورة - تحذير مهم
        if logger:
            logger.warning(f"⚠️ Gemini: {message}")

        warning_msg = f"⚠️ {message}"

        if gemini_status.get('next_available'):
            warning_msg += f"\nأقرب مفتاح سيتوفر خلال: {gemini_status['next_available']}"

        warning_msg += "\n\n🛡️ سيتم استخدام النظام الاحتياطي للتحليل."
        warning_msg += "\n💡 لإدارة المفاتيح: python manage_gemini_keys.py"
        warning_msg += "\n\nهل تريد المتابعة؟"

        result = messagebox.askyesno(
            "مفاتيح Gemini محظورة - Gemini Keys Blocked",
            warning_msg
        )

        return result  # إرجاع اختيار المستخدم

    elif status == 'no_keys':
        # لا توجد مفاتيح - تحذير
        if logger:
            logger.warning(f"⚠️ Gemini: {message}")

        warning_msg = f"⚠️ {message}"
        warning_msg += "\n\n🛡️ سيتم استخدام النظام الاحتياطي للتحليل."
        warning_msg += "\n💡 يمكنك إضافة مفاتيح في ملف .env"
        warning_msg += "\n\nهل تريد المتابعة؟"

        result = messagebox.askyesno(
            "مفاتيح Gemini غير موجودة - No Gemini Keys",
            warning_msg
        )

        return result  # إرجاع اختيار المستخدم

    else:
        # خطأ في الفحص - خطأ
        if logger:
            logger.error(f"❌ Gemini: {message}")

        error_msg = f"❌ {message}"
        error_msg += "\n\n🛡️ سيتم استخدام النظام الاحتياطي للتحليل."
        error_msg += "\nهل تريد المتابعة؟"

        result = messagebox.askyesno(
            "خطأ في فحص Gemini - Gemini Check Error",
            error_msg
        )

        return result  # إرجاع اختيار المستخدم

    return True  # المتابعة افتراضياً

def show_dependency_error(missing_deps):
    """عرض رسالة خطأ للمتطلبات المفقودة"""
    error_msg = "المتطلبات التالية مفقودة:\nMissing dependencies:\n\n"
    error_msg += "\n".join(f"- {dep}" for dep in missing_deps)
    error_msg += "\n\nيرجى تثبيتها باستخدام:\nPlease install them using:\n"
    error_msg += "pip install -r requirements.txt"
    
    if 'FFmpeg' in missing_deps:
        error_msg += "\n\nلتثبيت FFmpeg:\nTo install FFmpeg:\n"
        error_msg += "Windows: https://ffmpeg.org/download.html\n"
        error_msg += "macOS: brew install ffmpeg\n"
        error_msg += "Linux: sudo apt install ffmpeg"
    
    messagebox.showerror("خطأ في المتطلبات - Dependency Error", error_msg)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    
    # إعداد السجلات
    logger = setup_logging()
    if logger:
        logger.info(f"بدء تشغيل {AppSettings.APP_NAME} v{AppSettings.APP_VERSION}")
        logger.info(f"Starting {AppSettings.APP_NAME} v{AppSettings.APP_VERSION}")
    
    # فحص المتطلبات
    missing_deps = check_dependencies()
    if missing_deps:
        show_dependency_error(missing_deps)
        return 1

    # فحص حالة مفاتيح Gemini
    if logger:
        logger.info("فحص حالة مفاتيح Gemini...")

    gemini_status = check_gemini_keys()
    continue_app = show_gemini_status_message(gemini_status, logger)

    # إذا اختار المستخدم عدم المتابعة
    if not continue_app:
        if logger:
            logger.info("تم إلغاء تشغيل التطبيق بناءً على اختيار المستخدم")
        return 0

    try:
        # استيراد واجهة المستخدم الرئيسية
        from gui.main_window import VideoEditorApp
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        app = VideoEditorApp(root)
        
        if logger:
            logger.info("تم تشغيل التطبيق بنجاح")
            logger.info("Application started successfully")
        
        # تشغيل التطبيق
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"خطأ في استيراد واجهة المستخدم: {e}\nGUI import error: {e}"
        if logger:
            logger.error(error_msg)
        messagebox.showerror("خطأ - Error", error_msg)
        return 1
        
    except Exception as e:
        error_msg = f"خطأ غير متوقع: {e}\nUnexpected error: {e}"
        if logger:
            logger.error(error_msg, exc_info=True)
        messagebox.showerror("خطأ - Error", error_msg)
        return 1
    
    if logger:
        logger.info("تم إغلاق التطبيق")
        logger.info("Application closed")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
