"""
عميل Hugging Face للذكاء الاصطناعي
Hugging Face AI client for various AI tasks
"""

import requests
import json
import logging
import time
from typing import Optional, Dict, List, Any, Union
import base64
from pathlib import Path

from config.settings import AISettings

logger = logging.getLogger(__name__)

class HuggingFaceClient:
    """عميل للتفاعل مع Hugging Face Inference API"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or AISettings.HUGGINGFACE_API_KEY
        self.base_url = AISettings.HUGGINGFACE_API_URL
        self.session = requests.Session()
        
        if self.api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            })
        
        # عدادات للحدود
        self.daily_requests = 0
        self.monthly_requests = 0

        logger.info("تم تهيئة عميل Hugging Face")

        # التحقق من صحة المفتاح عند بدء التشغيل
        self._validate_api_key_on_startup()
    
    def _make_request(self, model_name: str, payload: Dict[str, Any], 
                     timeout: int = 30, retries: int = 3) -> Optional[Dict]:
        """إرسال طلب إلى API"""
        if not self.api_key:
            logger.error("مفتاح Hugging Face API غير متوفر")
            return None
        
        # التحقق من الحدود
        if self.daily_requests >= AISettings.DAILY_API_LIMIT:
            logger.warning("تم تجاوز الحد اليومي لطلبات API")
            return None
        
        url = f"{self.base_url}/{model_name}"
        
        for attempt in range(retries):
            try:
                response = self.session.post(url, json=payload, timeout=timeout)
                
                if response.status_code == 200:
                    self.daily_requests += 1
                    return response.json()
                elif response.status_code == 503:
                    # النموذج يتم تحميله
                    logger.info(f"النموذج {model_name} يتم تحميله، انتظار...")
                    time.sleep(10)
                    continue
                elif response.status_code == 429:
                    # تجاوز الحد المسموح
                    logger.warning("تم تجاوز حد الطلبات، انتظار...")
                    time.sleep(60)
                    continue
                else:
                    logger.error(f"خطأ في API: {response.status_code} - {response.text}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"خطأ في الشبكة (محاولة {attempt + 1}): {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)  # انتظار متزايد
                    
        return None

    def _validate_api_key_on_startup(self):
        """التحقق من صحة مفتاح API عند بدء التشغيل"""
        if not self.api_key:
            logger.warning("⚠️ مفتاح Hugging Face API غير متوفر")
            return

        try:
            # اختبار بسيط للتحقق من صحة المفتاح
            test_url = f"{self.base_url}/bert-base-uncased"
            test_payload = {"inputs": "test"}

            response = self.session.post(test_url, json=test_payload, timeout=10)

            if response.status_code == 200:
                logger.info("✅ مفتاح Hugging Face API صالح ويعمل بشكل صحيح")
            elif response.status_code == 401:
                logger.error("❌ مفتاح Hugging Face API غير صالح")
                self.api_key = None
                # إزالة المفتاح من headers
                if "Authorization" in self.session.headers:
                    del self.session.headers["Authorization"]
            elif response.status_code == 429:
                logger.warning("⚠️ مفتاح Hugging Face API وصل للحد الأقصى")
            else:
                logger.warning(f"⚠️ استجابة غير متوقعة من Hugging Face API: {response.status_code}")

        except Exception as e:
            logger.warning(f"⚠️ لا يمكن التحقق من صحة مفتاح Hugging Face API: {e}")

    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return bool(self.api_key)

    def get_api_status(self) -> Dict[str, Any]:
        """الحصول على حالة API"""
        return {
            "api_key_available": bool(self.api_key),
            "daily_requests": self.daily_requests,
            "monthly_requests": self.monthly_requests,
            "daily_limit": AISettings.DAILY_API_LIMIT,
            "monthly_limit": AISettings.MONTHLY_API_LIMIT,
            "requests_remaining_daily": max(0, AISettings.DAILY_API_LIMIT - self.daily_requests),
            "requests_remaining_monthly": max(0, AISettings.MONTHLY_API_LIMIT - self.monthly_requests)
        }
    
    def speech_to_text(self, audio_file_path: str, language: str = "ar") -> Optional[str]:
        """تحويل الكلام إلى نص"""
        try:
            audio_path = Path(audio_file_path)
            if not audio_path.exists():
                logger.error(f"ملف الصوت غير موجود: {audio_file_path}")
                return None
            
            # قراءة الملف الصوتي
            with open(audio_path, "rb") as f:
                audio_data = f.read()
            
            # ترميز base64
            audio_b64 = base64.b64encode(audio_data).decode()
            
            # إزالة معلمة task لأنها تسبب خطأ في API
            payload = {
                "inputs": audio_b64
                # تم إزالة parameters.task لأنها تسبب خطأ:
                # "AutomaticSpeechRecognitionPipeline._sanitize_parameters() got an unexpected keyword argument 'task'"
            }
            
            result = self._make_request(AISettings.SPEECH_TO_TEXT_MODEL, payload)
            
            if result and "text" in result:
                logger.info("تم تحويل الكلام إلى نص بنجاح")
                return result["text"]
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في تحويل الكلام إلى نص: {e}")
            return None
    
    def translate_text(self, text: str, source_lang: str = "en", 
                      target_lang: str = "ar") -> Optional[str]:
        """ترجمة النص"""
        try:
            if not text.strip():
                return ""
            
            # اختيار النموذج المناسب حسب اللغات
            if source_lang == "en" and target_lang == "ar":
                model = "Helsinki-NLP/opus-mt-en-ar"
            elif source_lang == "ar" and target_lang == "en":
                model = "Helsinki-NLP/opus-mt-ar-en"
            else:
                # استخدام نموذج عام
                model = f"Helsinki-NLP/opus-mt-{source_lang}-{target_lang}"
            
            payload = {
                "inputs": text,
                "parameters": {
                    "src_lang": source_lang,
                    "tgt_lang": target_lang
                }
            }
            
            result = self._make_request(model, payload)
            
            if result and isinstance(result, list) and len(result) > 0:
                translated = result[0].get("translation_text", "")
                logger.info("تم ترجمة النص بنجاح")
                return translated
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في ترجمة النص: {e}")
            return None
    
    def analyze_sentiment(self, text: str) -> Optional[Dict[str, Any]]:
        """تحليل المشاعر في النص"""
        try:
            if not text.strip():
                return None
            
            payload = {"inputs": text}
            
            result = self._make_request(AISettings.SENTIMENT_ANALYSIS_MODEL, payload)
            
            if result and isinstance(result, list) and len(result) > 0:
                sentiment_data = result[0]
                
                # تحويل النتيجة إلى تنسيق مفهوم
                sentiment_map = {
                    "LABEL_0": "سلبي",
                    "LABEL_1": "محايد", 
                    "LABEL_2": "إيجابي",
                    "NEGATIVE": "سلبي",
                    "NEUTRAL": "محايد",
                    "POSITIVE": "إيجابي"
                }
                
                if isinstance(sentiment_data, list):
                    # إذا كانت النتيجة قائمة من التصنيفات
                    best_sentiment = max(sentiment_data, key=lambda x: x["score"])
                    label = sentiment_map.get(best_sentiment["label"], best_sentiment["label"])
                    score = best_sentiment["score"]
                else:
                    # إذا كانت النتيجة تصنيف واحد
                    label = sentiment_map.get(sentiment_data["label"], sentiment_data["label"])
                    score = sentiment_data["score"]
                
                logger.info(f"تم تحليل المشاعر: {label} ({score:.2f})")
                return {
                    "sentiment": label,
                    "confidence": score,
                    "raw_result": result
                }
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر: {e}")
            return None
    
    def generate_text(self, prompt: str, max_length: int = 100, 
                     temperature: float = 0.7) -> Optional[str]:
        """توليد النص"""
        try:
            if not prompt.strip():
                return None
            
            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_length": max_length,
                    "temperature": temperature,
                    "do_sample": True,
                    "top_p": 0.9
                }
            }
            
            result = self._make_request(AISettings.TEXT_GENERATION_MODEL, payload)
            
            if result and isinstance(result, list) and len(result) > 0:
                generated_text = result[0].get("generated_text", "")
                
                # إزالة النص الأصلي من النتيجة
                if generated_text.startswith(prompt):
                    generated_text = generated_text[len(prompt):].strip()
                
                logger.info("تم توليد النص بنجاح")
                return generated_text
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في توليد النص: {e}")
            return None
    
    def generate_video_title(self, video_description: str) -> Optional[str]:
        """توليد عنوان جذاب للفيديو"""
        try:
            prompt = f"""اكتب عنوان جذاب ومثير للاهتمام لفيديو يوتيوب بناءً على الوصف التالي:
الوصف: {video_description}

العنوان:"""
            
            title = self.generate_text(prompt, max_length=50, temperature=0.8)
            
            if title:
                # تنظيف العنوان
                title = title.strip().split('\n')[0]  # أخذ السطر الأول فقط
                title = title.replace('"', '').replace("'", "")  # إزالة علامات الاقتباس
                
                logger.info(f"تم توليد العنوان: {title}")
                return title
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في توليد العنوان: {e}")
            return None
    
    def generate_cta(self, video_topic: str) -> Optional[str]:
        """توليد عبارة حث على اتخاذ إجراء"""
        try:
            prompt = f"""اكتب عبارة قصيرة وجذابة للحث على اتخاذ إجراء (Call to Action) لفيديو عن:
الموضوع: {video_topic}

العبارة:"""
            
            cta = self.generate_text(prompt, max_length=30, temperature=0.7)
            
            if cta:
                cta = cta.strip().split('\n')[0]
                logger.info(f"تم توليد عبارة الحث: {cta}")
                return cta
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في توليد عبارة الحث: {e}")
            return None
    
    def get_usage_stats(self) -> Dict[str, int]:
        """الحصول على إحصائيات الاستخدام"""
        return {
            "daily_requests": self.daily_requests,
            "monthly_requests": self.monthly_requests,
            "daily_limit": AISettings.DAILY_API_LIMIT,
            "monthly_limit": AISettings.MONTHLY_API_LIMIT
        }
    
    def reset_daily_counter(self):
        """إعادة تعيين العداد اليومي"""
        self.daily_requests = 0
        logger.info("تم إعادة تعيين العداد اليومي")
