"""
عميل Microsoft Azure Computer Vision & Video Indexer
Microsoft Azure Computer Vision & Video Indexer Client
"""

import logging
import time
import requests
from typing import Dict, List, Optional, Any
from pathlib import Path
import json
import base64

try:
    from azure.cognitiveservices.vision.computervision import ComputerVisionClient
    from azure.cognitiveservices.vision.computervision.models import OperationStatusCodes
    from msrest.authentication import CognitiveServicesCredentials
    AZURE_CV_AVAILABLE = True
except ImportError:
    AZURE_CV_AVAILABLE = False
    ComputerVisionClient = None
    CognitiveServicesCredentials = None

from ..config.api_keys import api_keys_manager

logger = logging.getLogger(__name__)

class AzureComputerVisionClient:
    """عميل للتفاعل مع Azure Computer Vision"""
    
    def __init__(self):
        self.config = api_keys_manager.get_service_config('azure_cv')
        self.client = None
        
        if not AZURE_CV_AVAILABLE:
            logger.error("مكتبة Azure Computer Vision غير متوفرة. قم بتثبيتها: pip install azure-cognitiveservices-vision-computervision")
            return
        
        if not self.config or not self.config.get('enabled'):
            logger.warning("Azure Computer Vision غير مُفعل في الإعدادات")
            return
        
        self._initialize_client()
    
    def _initialize_client(self):
        """تهيئة عميل Azure Computer Vision"""
        try:
            if not self.config.get('api_key') or not self.config.get('endpoint'):
                logger.error("مفاتيح Azure Computer Vision غير متوفرة")
                return
            
            credentials = CognitiveServicesCredentials(self.config['api_key'])
            self.client = ComputerVisionClient(
                endpoint=self.config['endpoint'],
                credentials=credentials
            )
            
            logger.info("تم تهيئة عميل Azure Computer Vision بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة عميل Azure Computer Vision: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return (AZURE_CV_AVAILABLE and 
                self.client is not None and 
                self.config and 
                self.config.get('enabled', False))
    
    def analyze_image(self, image_path: str) -> Optional[Dict[str, Any]]:
        """تحليل شامل للصورة"""
        if not self.is_available():
            logger.error("خدمة Azure Computer Vision غير متوفرة")
            return None
        
        try:
            # قائمة الميزات المطلوبة
            visual_features = [
                'Categories', 'Description', 'Faces', 'ImageType',
                'Objects', 'Tags', 'Adult', 'Color', 'Brands'
            ]
            
            with open(image_path, 'rb') as image_stream:
                analysis = self.client.analyze_image_in_stream(
                    image_stream,
                    visual_features=visual_features
                )
            
            result = {
                'description': {
                    'captions': [
                        {
                            'text': caption.text,
                            'confidence': caption.confidence
                        }
                        for caption in analysis.description.captions
                    ] if analysis.description else [],
                    'tags': analysis.description.tags if analysis.description else []
                },
                'categories': [
                    {
                        'name': category.name,
                        'score': category.score
                    }
                    for category in analysis.categories
                ] if analysis.categories else [],
                'tags': [
                    {
                        'name': tag.name,
                        'confidence': tag.confidence
                    }
                    for tag in analysis.tags
                ] if analysis.tags else [],
                'objects': [
                    {
                        'object': obj.object_property,
                        'confidence': obj.confidence,
                        'rectangle': {
                            'x': obj.rectangle.x,
                            'y': obj.rectangle.y,
                            'w': obj.rectangle.w,
                            'h': obj.rectangle.h
                        }
                    }
                    for obj in analysis.objects
                ] if analysis.objects else [],
                'faces': [
                    {
                        'age': face.age,
                        'gender': face.gender.value if face.gender else None,
                        'rectangle': {
                            'left': face.face_rectangle.left,
                            'top': face.face_rectangle.top,
                            'width': face.face_rectangle.width,
                            'height': face.face_rectangle.height
                        }
                    }
                    for face in analysis.faces
                ] if analysis.faces else [],
                'adult_content': {
                    'is_adult_content': analysis.adult.is_adult_content,
                    'adult_score': analysis.adult.adult_score,
                    'is_racy_content': analysis.adult.is_racy_content,
                    'racy_score': analysis.adult.racy_score
                } if analysis.adult else {},
                'color_info': {
                    'dominant_color_foreground': analysis.color.dominant_color_foreground,
                    'dominant_color_background': analysis.color.dominant_color_background,
                    'dominant_colors': analysis.color.dominant_colors,
                    'accent_color': analysis.color.accent_color,
                    'is_bw_img': analysis.color.is_bw_img
                } if analysis.color else {},
                'brands': [
                    {
                        'name': brand.name,
                        'confidence': brand.confidence,
                        'rectangle': {
                            'x': brand.rectangle.x,
                            'y': brand.rectangle.y,
                            'w': brand.rectangle.w,
                            'h': brand.rectangle.h
                        }
                    }
                    for brand in analysis.brands
                ] if analysis.brands else []
            }
            
            logger.info("تم تحليل الصورة بنجاح باستخدام Azure Computer Vision")
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الصورة: {e}")
            return None
    
    def extract_text_from_image(self, image_path: str) -> Optional[str]:
        """استخراج النص من الصورة (OCR)"""
        if not self.is_available():
            logger.error("خدمة Azure Computer Vision غير متوفرة")
            return None
        
        try:
            with open(image_path, 'rb') as image_stream:
                # بدء عملية قراءة النص
                read_operation = self.client.read_in_stream(
                    image_stream,
                    raw=True
                )
            
            # الحصول على معرف العملية
            operation_id = read_operation.headers['Operation-Location'].split('/')[-1]
            
            # انتظار اكتمال العملية
            while True:
                result = self.client.get_read_result(operation_id)
                if result.status not in [OperationStatusCodes.running, OperationStatusCodes.not_started]:
                    break
                time.sleep(1)
            
            # استخراج النص
            extracted_text = ""
            if result.status == OperationStatusCodes.succeeded:
                for page in result.analyze_result.read_results:
                    for line in page.lines:
                        extracted_text += line.text + "\n"
            
            logger.info("تم استخراج النص من الصورة بنجاح")
            return extracted_text.strip()
            
        except Exception as e:
            logger.error(f"خطأ في استخراج النص: {e}")
            return None

class AzureVideoIndexerClient:
    """عميل للتفاعل مع Azure Video Indexer"""
    
    def __init__(self):
        self.config = api_keys_manager.get_service_config('azure_video_indexer')
        self.base_url = "https://api.videoindexer.ai"
        self.session = requests.Session()
        
        if not self.config or not self.config.get('enabled'):
            logger.warning("Azure Video Indexer غير مُفعل في الإعدادات")
            return
        
        self._setup_session()
    
    def _setup_session(self):
        """إعداد جلسة الطلبات"""
        if self.config.get('api_key'):
            self.session.headers.update({
                'Ocp-Apim-Subscription-Key': self.config['api_key'],
                'Content-Type': 'application/json'
            })
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return (self.config and 
                self.config.get('enabled', False) and
                self.config.get('api_key') and
                self.config.get('account_id'))
    
    def upload_video(self, video_path: str, video_name: str = None) -> Optional[str]:
        """رفع فيديو للتحليل"""
        if not self.is_available():
            logger.error("خدمة Azure Video Indexer غير متوفرة")
            return None
        
        try:
            if not video_name:
                video_name = Path(video_path).stem
            
            # رفع الفيديو
            url = f"{self.base_url}/{self.config['location']}/Accounts/{self.config['account_id']}/Videos"
            
            with open(video_path, 'rb') as video_file:
                files = {'file': (video_name, video_file, 'video/mp4')}
                params = {
                    'name': video_name,
                    'privacy': 'Private',
                    'language': 'auto'
                }
                
                response = self.session.post(url, files=files, params=params)
                
                if response.status_code == 200:
                    result = response.json()
                    video_id = result.get('id')
                    logger.info(f"تم رفع الفيديو بنجاح. معرف الفيديو: {video_id}")
                    return video_id
                else:
                    logger.error(f"خطأ في رفع الفيديو: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"خطأ في رفع الفيديو: {e}")
            return None
    
    def get_video_insights(self, video_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على تحليل الفيديو"""
        if not self.is_available():
            logger.error("خدمة Azure Video Indexer غير متوفرة")
            return None
        
        try:
            url = f"{self.base_url}/{self.config['location']}/Accounts/{self.config['account_id']}/Videos/{video_id}/Index"
            
            response = self.session.get(url)
            
            if response.status_code == 200:
                insights = response.json()
                logger.info("تم الحصول على تحليل الفيديو بنجاح")
                return insights
            else:
                logger.error(f"خطأ في الحصول على التحليل: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على تحليل الفيديو: {e}")
            return None
    
    def get_service_status(self) -> Dict[str, Any]:
        """الحصول على حالة الخدمة"""
        return {
            'service_name': 'Azure Video Indexer',
            'available': self.is_available(),
            'config_enabled': self.config.get('enabled', False) if self.config else False,
            'account_id': self.config.get('account_id') if self.config else None,
            'location': self.config.get('location') if self.config else None
        }
