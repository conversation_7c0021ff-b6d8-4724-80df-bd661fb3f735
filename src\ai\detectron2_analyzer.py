"""
محلل Detectron2 للتحليل المتقدم
Detectron2 Advanced Analysis System

يستخدم Detectron2 من Facebook لكشف الأجسام والتقسيم والعلامات المفتاحية المتقدمة
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import time

logger = logging.getLogger(__name__)

# محاولة استيراد Detectron2
try:
    import detectron2
    from detectron2 import model_zoo
    from detectron2.engine import DefaultPredictor
    from detectron2.config import get_cfg
    from detectron2.utils.visualizer import Visualizer
    from detectron2.data import MetadataCatalog
    DETECTRON2_AVAILABLE = True
    logger.info("✅ Detectron2 متوفر")
except ImportError:
    DETECTRON2_AVAILABLE = False
    logger.warning("⚠️ Detectron2 غير متوفر - pip install detectron2")

@dataclass
class InstanceSegmentation:
    """تقسيم المثيل"""
    class_name: str
    confidence: float
    bbox: Tuple[int, int, int, int]
    mask: np.ndarray
    area: float
    keypoints: Optional[List[Tuple[int, int]]] = None

@dataclass
class PoseAnalysis:
    """تحليل الوضعية"""
    person_id: int
    keypoints: List[Tuple[int, int, float]]  # x, y, confidence
    pose_type: str
    confidence: float
    action_detected: str

@dataclass
class AdvancedAnalysis:
    """التحليل المتقدم"""
    timestamp: float
    instances: List[InstanceSegmentation]
    poses: List[PoseAnalysis]
    scene_understanding: Dict[str, Any]
    interaction_analysis: Dict[str, Any]
    movement_analysis: Dict[str, Any]

class Detectron2Analyzer:
    """محلل Detectron2 للتحليل المتقدم"""
    
    def __init__(self):
        """تهيئة محلل Detectron2"""
        self.available = DETECTRON2_AVAILABLE
        self.instance_predictor = None
        self.keypoint_predictor = None
        
        # أنواع الوضعيات
        self.pose_types = {
            'standing': 'واقف',
            'sitting': 'جالس',
            'lying': 'مستلقي',
            'jumping': 'يقفز',
            'running': 'يجري',
            'dancing': 'يرقص',
            'pointing': 'يشير',
            'waving': 'يلوح',
            'clapping': 'يصفق'
        }
        
        # أنواع الأفعال
        self.action_types = {
            'excited': 'متحمس',
            'calm': 'هادئ',
            'active': 'نشط',
            'surprised': 'مندهش',
            'celebrating': 'يحتفل',
            'concentrating': 'يركز'
        }
        
        if self.available:
            self._load_models()
    
    def _load_models(self):
        """تحميل نماذج Detectron2"""
        try:
            logger.info("تحميل نماذج Detectron2...")
            
            # إعداد نموذج Instance Segmentation
            cfg_instance = get_cfg()
            cfg_instance.merge_from_file(
                model_zoo.get_config_file("COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml")
            )
            cfg_instance.MODEL.ROI_HEADS.SCORE_THRESH_TEST = 0.5
            cfg_instance.MODEL.WEIGHTS = model_zoo.get_checkpoint_url(
                "COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"
            )
            self.instance_predictor = DefaultPredictor(cfg_instance)
            
            # إعداد نموذج Keypoint Detection
            cfg_keypoint = get_cfg()
            cfg_keypoint.merge_from_file(
                model_zoo.get_config_file("COCO-Keypoints/keypoint_rcnn_R_50_FPN_3x.yaml")
            )
            cfg_keypoint.MODEL.ROI_HEADS.SCORE_THRESH_TEST = 0.7
            cfg_keypoint.MODEL.WEIGHTS = model_zoo.get_checkpoint_url(
                "COCO-Keypoints/keypoint_rcnn_R_50_FPN_3x.yaml"
            )
            self.keypoint_predictor = DefaultPredictor(cfg_keypoint)
            
            logger.info("✅ تم تحميل نماذج Detectron2 بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل نماذج Detectron2: {e}")
            self.available = False
    
    def is_available(self) -> bool:
        """فحص توفر Detectron2"""
        return (self.available and 
                self.instance_predictor is not None and 
                self.keypoint_predictor is not None)
    
    def analyze_frame(self, frame: np.ndarray, timestamp: float = 0.0) -> AdvancedAnalysis:
        """تحليل متقدم للإطار
        
        Args:
            frame: الإطار المراد تحليله
            timestamp: الوقت بالثواني
            
        Returns:
            التحليل المتقدم
        """
        if not self.is_available():
            return AdvancedAnalysis(
                timestamp=timestamp,
                instances=[],
                poses=[],
                scene_understanding={},
                interaction_analysis={},
                movement_analysis={}
            )
        
        try:
            # تحليل Instance Segmentation
            instances = self._analyze_instances(frame)
            
            # تحليل الوضعيات
            poses = self._analyze_poses(frame)
            
            # فهم المشهد
            scene_understanding = self._understand_scene(instances, poses)
            
            # تحليل التفاعلات
            interaction_analysis = self._analyze_interactions(instances, poses)
            
            # تحليل الحركة
            movement_analysis = self._analyze_movement(poses)
            
            return AdvancedAnalysis(
                timestamp=timestamp,
                instances=instances,
                poses=poses,
                scene_understanding=scene_understanding,
                interaction_analysis=interaction_analysis,
                movement_analysis=movement_analysis
            )
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم: {e}")
            return AdvancedAnalysis(
                timestamp=timestamp,
                instances=[],
                poses=[],
                scene_understanding={'error': str(e)},
                interaction_analysis={},
                movement_analysis={}
            )
    
    def _analyze_instances(self, frame: np.ndarray) -> List[InstanceSegmentation]:
        """تحليل تقسيم المثيلات"""
        try:
            outputs = self.instance_predictor(frame)
            instances = outputs["instances"].to("cpu")
            
            results = []
            
            if len(instances) > 0:
                boxes = instances.pred_boxes.tensor.numpy()
                classes = instances.pred_classes.numpy()
                scores = instances.scores.numpy()
                masks = instances.pred_masks.numpy()
                
                # الحصول على أسماء الفئات
                metadata = MetadataCatalog.get(self.instance_predictor.cfg.DATASETS.TRAIN[0])
                class_names = metadata.thing_classes
                
                for i in range(len(instances)):
                    x1, y1, x2, y2 = boxes[i].astype(int)
                    class_id = classes[i]
                    confidence = scores[i]
                    mask = masks[i]
                    
                    class_name = class_names[class_id] if class_id < len(class_names) else f"class_{class_id}"
                    area = np.sum(mask)
                    
                    instance = InstanceSegmentation(
                        class_name=class_name,
                        confidence=float(confidence),
                        bbox=(x1, y1, x2, y2),
                        mask=mask,
                        area=float(area)
                    )
                    results.append(instance)
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في تحليل تقسيم المثيلات: {e}")
            return []
    
    def _analyze_poses(self, frame: np.ndarray) -> List[PoseAnalysis]:
        """تحليل الوضعيات"""
        try:
            outputs = self.keypoint_predictor(frame)
            instances = outputs["instances"].to("cpu")
            
            results = []
            
            if len(instances) > 0 and instances.has("pred_keypoints"):
                keypoints = instances.pred_keypoints.numpy()
                scores = instances.scores.numpy()
                
                for i, (kpts, score) in enumerate(zip(keypoints, scores)):
                    # تحويل النقاط المفتاحية
                    pose_keypoints = []
                    for j in range(len(kpts)):
                        x, y, conf = kpts[j]
                        pose_keypoints.append((int(x), int(y), float(conf)))
                    
                    # تحليل نوع الوضعية
                    pose_type = self._classify_pose(pose_keypoints)
                    
                    # كشف الفعل
                    action = self._detect_action(pose_keypoints)
                    
                    pose_analysis = PoseAnalysis(
                        person_id=i,
                        keypoints=pose_keypoints,
                        pose_type=pose_type,
                        confidence=float(score),
                        action_detected=action
                    )
                    results.append(pose_analysis)
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الوضعيات: {e}")
            return []
    
    def _classify_pose(self, keypoints: List[Tuple[int, int, float]]) -> str:
        """تصنيف نوع الوضعية"""
        try:
            # فحص النقاط المفتاحية المهمة
            # 0: nose, 5: left_shoulder, 6: right_shoulder
            # 11: left_hip, 12: right_hip, 15: left_ankle, 16: right_ankle
            
            if len(keypoints) < 17:
                return 'unknown'
            
            nose = keypoints[0]
            left_shoulder = keypoints[5]
            right_shoulder = keypoints[6]
            left_hip = keypoints[11]
            right_hip = keypoints[12]
            left_ankle = keypoints[15]
            right_ankle = keypoints[16]
            
            # حساب المسافات والزوايا
            shoulder_y = (left_shoulder[1] + right_shoulder[1]) / 2
            hip_y = (left_hip[1] + right_hip[1]) / 2
            ankle_y = (left_ankle[1] + right_ankle[1]) / 2
            
            # تحديد الوضعية بناءً على المواضع النسبية
            if ankle_y - hip_y < 50:  # الكاحلين قريبان من الوركين
                return 'sitting'
            elif abs(shoulder_y - hip_y) < 100:  # الكتفان والوركان على نفس المستوى تقريباً
                return 'lying'
            elif ankle_y > hip_y + 100:  # الكاحلان أسفل الوركين بوضوح
                return 'standing'
            else:
                return 'unknown'
                
        except Exception as e:
            logger.error(f"خطأ في تصنيف الوضعية: {e}")
            return 'unknown'
    
    def _detect_action(self, keypoints: List[Tuple[int, int, float]]) -> str:
        """كشف الفعل"""
        try:
            if len(keypoints) < 17:
                return 'unknown'
            
            # تحليل مواضع اليدين والذراعين
            left_wrist = keypoints[9]
            right_wrist = keypoints[10]
            left_elbow = keypoints[7]
            right_elbow = keypoints[8]
            nose = keypoints[0]
            
            # كشف التلويح (اليد مرفوعة)
            if (left_wrist[1] < nose[1] - 50 or right_wrist[1] < nose[1] - 50):
                return 'waving'
            
            # كشف التصفيق (اليدان قريبتان من بعض)
            wrist_distance = abs(left_wrist[0] - right_wrist[0])
            if wrist_distance < 100 and left_wrist[2] > 0.5 and right_wrist[2] > 0.5:
                return 'clapping'
            
            # كشف الإشارة (يد واحدة ممدودة)
            if (abs(left_wrist[0] - left_elbow[0]) > 100 or 
                abs(right_wrist[0] - right_elbow[0]) > 100):
                return 'pointing'
            
            return 'calm'
            
        except Exception as e:
            logger.error(f"خطأ في كشف الفعل: {e}")
            return 'unknown'
    
    def _understand_scene(self, instances: List[InstanceSegmentation], 
                         poses: List[PoseAnalysis]) -> Dict[str, Any]:
        """فهم المشهد"""
        try:
            scene_info = {
                'person_count': len(poses),
                'object_count': len([i for i in instances if i.class_name != 'person']),
                'dominant_objects': [],
                'scene_activity': 'low',
                'complexity_score': 0.0
            }
            
            # الأشياء المهيمنة
            object_areas = {}
            for instance in instances:
                if instance.class_name != 'person':
                    if instance.class_name in object_areas:
                        object_areas[instance.class_name] += instance.area
                    else:
                        object_areas[instance.class_name] = instance.area
            
            # ترتيب حسب المساحة
            sorted_objects = sorted(object_areas.items(), key=lambda x: x[1], reverse=True)
            scene_info['dominant_objects'] = [obj for obj, area in sorted_objects[:3]]
            
            # تحديد مستوى النشاط
            active_poses = [p for p in poses if p.action_detected in ['waving', 'clapping', 'pointing']]
            if len(active_poses) > 0:
                scene_info['scene_activity'] = 'high'
            elif len(poses) > 1:
                scene_info['scene_activity'] = 'medium'
            
            # حساب تعقيد المشهد
            complexity = (len(instances) * 5) + (len(poses) * 10) + (len(active_poses) * 15)
            scene_info['complexity_score'] = min(complexity, 100.0)
            
            return scene_info
            
        except Exception as e:
            logger.error(f"خطأ في فهم المشهد: {e}")
            return {'error': str(e)}
    
    def _analyze_interactions(self, instances: List[InstanceSegmentation], 
                            poses: List[PoseAnalysis]) -> Dict[str, Any]:
        """تحليل التفاعلات"""
        try:
            interactions = {
                'person_interactions': [],
                'object_interactions': [],
                'social_score': 0.0
            }
            
            # تحليل التفاعلات بين الأشخاص
            for i, pose1 in enumerate(poses):
                for j, pose2 in enumerate(poses[i+1:], i+1):
                    # حساب المسافة بين الأشخاص
                    person1_center = self._get_person_center(pose1.keypoints)
                    person2_center = self._get_person_center(pose2.keypoints)
                    
                    if person1_center and person2_center:
                        distance = np.sqrt(
                            (person1_center[0] - person2_center[0])**2 + 
                            (person1_center[1] - person2_center[1])**2
                        )
                        
                        if distance < 200:  # قريبان من بعض
                            interaction = {
                                'person1_id': pose1.person_id,
                                'person2_id': pose2.person_id,
                                'distance': float(distance),
                                'interaction_type': 'close'
                            }
                            interactions['person_interactions'].append(interaction)
            
            # حساب النقاط الاجتماعية
            social_score = len(interactions['person_interactions']) * 20
            active_people = len([p for p in poses if p.action_detected != 'calm'])
            social_score += active_people * 15
            
            interactions['social_score'] = min(social_score, 100.0)
            
            return interactions
            
        except Exception as e:
            logger.error(f"خطأ في تحليل التفاعلات: {e}")
            return {'error': str(e)}
    
    def _analyze_movement(self, poses: List[PoseAnalysis]) -> Dict[str, Any]:
        """تحليل الحركة"""
        try:
            movement_info = {
                'total_people': len(poses),
                'active_people': 0,
                'movement_types': [],
                'energy_level': 'low'
            }
            
            # تحليل حركة كل شخص
            for pose in poses:
                if pose.action_detected in ['waving', 'clapping', 'pointing']:
                    movement_info['active_people'] += 1
                    movement_info['movement_types'].append(pose.action_detected)
            
            # تحديد مستوى الطاقة
            activity_ratio = movement_info['active_people'] / max(len(poses), 1)
            if activity_ratio > 0.7:
                movement_info['energy_level'] = 'high'
            elif activity_ratio > 0.3:
                movement_info['energy_level'] = 'medium'
            
            return movement_info
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الحركة: {e}")
            return {'error': str(e)}
    
    def _get_person_center(self, keypoints: List[Tuple[int, int, float]]) -> Optional[Tuple[int, int]]:
        """الحصول على مركز الشخص"""
        try:
            if len(keypoints) < 17:
                return None
            
            # استخدام نقطة الأنف كمركز
            nose = keypoints[0]
            if nose[2] > 0.5:  # ثقة كافية
                return (nose[0], nose[1])
            
            # إذا لم تكن نقطة الأنف واضحة، استخدم متوسط الكتفين
            left_shoulder = keypoints[5]
            right_shoulder = keypoints[6]
            
            if left_shoulder[2] > 0.5 and right_shoulder[2] > 0.5:
                center_x = (left_shoulder[0] + right_shoulder[0]) // 2
                center_y = (left_shoulder[1] + right_shoulder[1]) // 2
                return (center_x, center_y)
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في حساب مركز الشخص: {e}")
            return None
