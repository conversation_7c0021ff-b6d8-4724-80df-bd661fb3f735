"""
محلل المحتوى المحسن مع دعم المكتبات المتقدمة
Enhanced Content Analyzer with Advanced Libraries Support
"""

import logging
import numpy as np
import cv2
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)

# محاولة استيراد المكتبات المتقدمة
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    logger.info("✅ MediaPipe متوفر")
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    logger.warning("⚠️ MediaPipe غير متوفر")

try:
    from deepface import DeepFace
    DEEPFACE_AVAILABLE = True
    logger.info("✅ DeepFace متوفر")
except ImportError:
    DEEPFACE_AVAILABLE = False
    logger.warning("⚠️ DeepFace غير متوفر")

try:
    import py_feat
    PY_FEAT_AVAILABLE = True
    logger.info("✅ Py-Feat متوفر")
except ImportError:
    PY_FEAT_AVAILABLE = False
    logger.warning("⚠️ Py-Feat غير متوفر")

try:
    import librosa
    LIBROSA_AVAILABLE = True
    logger.info("✅ Librosa متوفر")
except ImportError:
    LIBROSA_AVAILABLE = False
    logger.warning("⚠️ Librosa غير متوفر")

class EnhancedContentAnalyzer:
    """محلل المحتوى المحسن مع دعم التحليل المتقدم"""
    
    def __init__(self):
        """تهيئة محلل المحتوى المحسن"""
        self.available_features = {
            'mediapipe': MEDIAPIPE_AVAILABLE,
            'deepface': DEEPFACE_AVAILABLE,
            'py_feat': PY_FEAT_AVAILABLE,
            'librosa': LIBROSA_AVAILABLE
        }
        
        # تهيئة MediaPipe
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_detection = mp.solutions.face_detection.FaceDetection(
                model_selection=1, min_detection_confidence=0.5
            )
            self.mp_pose = mp.solutions.pose.Pose(
                static_image_mode=False,
                model_complexity=1,
                enable_segmentation=False,
                min_detection_confidence=0.5
            )
            self.mp_hands = mp.solutions.hands.Hands(
                static_image_mode=False,
                max_num_hands=2,
                min_detection_confidence=0.5
            )
        
        logger.info(f"تم تهيئة محلل المحتوى المحسن - الميزات المتاحة: {sum(self.available_features.values())}/4")
    
    def analyze_video_advanced(self, video_path: str, sample_rate: int = 1) -> Dict[str, Any]:
        """تحليل متقدم للفيديو باستخدام جميع المكتبات المتاحة
        
        Args:
            video_path: مسار ملف الفيديو
            sample_rate: معدل أخذ العينات (كل كم إطار)
            
        Returns:
            نتائج التحليل المتقدم
        """
        results = {
            'face_analysis': [],
            'pose_analysis': [],
            'hand_analysis': [],
            'emotion_analysis': [],
            'movement_analysis': [],
            'audio_analysis': {},
            'excitement_moments': [],
            'reaction_moments': []
        }
        
        try:
            # تحليل الفيديو
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0
            
            frame_count = 0
            processed_frames = 0
            
            logger.info(f"بدء التحليل المتقدم للفيديو: {duration:.1f} ثانية، {total_frames} إطار")
            
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                # أخذ عينات من الإطارات
                if frame_count % sample_rate == 0:
                    timestamp = frame_count / fps
                    
                    # تحليل الوجوه والمشاعر
                    face_data = self._analyze_faces_in_frame(frame, timestamp)
                    if face_data:
                        results['face_analysis'].extend(face_data)
                    
                    # تحليل الوضعيات
                    pose_data = self._analyze_pose_in_frame(frame, timestamp)
                    if pose_data:
                        results['pose_analysis'].append(pose_data)
                    
                    # تحليل اليدين
                    hand_data = self._analyze_hands_in_frame(frame, timestamp)
                    if hand_data:
                        results['hand_analysis'].append(hand_data)
                    
                    # تحليل المشاعر المتقدم
                    emotion_data = self._analyze_emotions_advanced(frame, timestamp)
                    if emotion_data:
                        results['emotion_analysis'].append(emotion_data)
                    
                    processed_frames += 1
                
                frame_count += 1
                
                # تحديث التقدم كل 100 إطار
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"تقدم التحليل: {progress:.1f}%")
            
            cap.release()
            
            # تحليل الصوت
            if LIBROSA_AVAILABLE:
                results['audio_analysis'] = self._analyze_audio_advanced(video_path)
            
            # اكتشاف اللحظات المثيرة
            results['excitement_moments'] = self._detect_excitement_moments(results)
            
            # اكتشاف ردود الأفعال
            results['reaction_moments'] = self._detect_reaction_moments(results)
            
            logger.info(f"تم الانتهاء من التحليل المتقدم - معالجة {processed_frames} إطار")
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم: {e}")
        
        return results
    
    def _analyze_faces_in_frame(self, frame: np.ndarray, timestamp: float) -> List[Dict[str, Any]]:
        """تحليل الوجوه في إطار واحد"""
        faces_data = []
        
        if not MEDIAPIPE_AVAILABLE:
            return faces_data
        
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.mp_face_detection.process(rgb_frame)
            
            if results.detections:
                for i, detection in enumerate(results.detections):
                    bbox = detection.location_data.relative_bounding_box
                    confidence = detection.score[0]
                    
                    face_data = {
                        'timestamp': timestamp,
                        'face_id': i,
                        'confidence': confidence,
                        'bbox': {
                            'x': bbox.xmin,
                            'y': bbox.ymin,
                            'width': bbox.width,
                            'height': bbox.height
                        }
                    }
                    faces_data.append(face_data)
                    
        except Exception as e:
            logger.error(f"خطأ في تحليل الوجوه: {e}")
        
        return faces_data
    
    def _analyze_pose_in_frame(self, frame: np.ndarray, timestamp: float) -> Optional[Dict[str, Any]]:
        """تحليل الوضعية في إطار واحد"""
        if not MEDIAPIPE_AVAILABLE:
            return None
        
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.mp_pose.process(rgb_frame)
            
            if results.pose_landmarks:
                # حساب مؤشرات الحركة
                landmarks = results.pose_landmarks.landmark
                
                # حساب مركز الكتلة
                center_x = np.mean([lm.x for lm in landmarks])
                center_y = np.mean([lm.y for lm in landmarks])
                
                # حساب انتشار النقاط (مؤشر على الحركة)
                spread = np.std([lm.x for lm in landmarks]) + np.std([lm.y for lm in landmarks])
                
                return {
                    'timestamp': timestamp,
                    'center_x': center_x,
                    'center_y': center_y,
                    'movement_spread': spread,
                    'visibility_avg': np.mean([lm.visibility for lm in landmarks])
                }
                
        except Exception as e:
            logger.error(f"خطأ في تحليل الوضعية: {e}")
        
        return None
    
    def _analyze_hands_in_frame(self, frame: np.ndarray, timestamp: float) -> Optional[Dict[str, Any]]:
        """تحليل اليدين في إطار واحد"""
        if not MEDIAPIPE_AVAILABLE:
            return None
        
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.mp_hands.process(rgb_frame)
            
            if results.multi_hand_landmarks:
                hands_data = {
                    'timestamp': timestamp,
                    'hands_count': len(results.multi_hand_landmarks),
                    'hands_activity': []
                }
                
                for hand_landmarks in results.multi_hand_landmarks:
                    # حساب نشاط اليد
                    landmarks = hand_landmarks.landmark
                    activity = np.std([lm.x for lm in landmarks]) + np.std([lm.y for lm in landmarks])
                    hands_data['hands_activity'].append(activity)
                
                return hands_data
                
        except Exception as e:
            logger.error(f"خطأ في تحليل اليدين: {e}")
        
        return None
    
    def _analyze_emotions_advanced(self, frame: np.ndarray, timestamp: float) -> Optional[Dict[str, Any]]:
        """تحليل المشاعر المتقدم"""
        if not DEEPFACE_AVAILABLE:
            return None
        
        try:
            # تحليل المشاعر باستخدام DeepFace
            analysis = DeepFace.analyze(frame, actions=['emotion'], enforce_detection=False)
            
            if analysis:
                emotions = analysis[0]['emotion'] if isinstance(analysis, list) else analysis['emotion']
                
                # العثور على المشاعر المهيمنة
                dominant_emotion = max(emotions, key=emotions.get)
                emotion_confidence = emotions[dominant_emotion]
                
                return {
                    'timestamp': timestamp,
                    'dominant_emotion': dominant_emotion,
                    'confidence': emotion_confidence,
                    'all_emotions': emotions
                }
                
        except Exception as e:
            logger.debug(f"لا يمكن تحليل المشاعر في الإطار {timestamp}: {e}")
        
        return None
    
    def _analyze_audio_advanced(self, video_path: str) -> Dict[str, Any]:
        """تحليل الصوت المتقدم"""
        audio_data = {}
        
        if not LIBROSA_AVAILABLE:
            return audio_data
        
        try:
            # تحميل الصوت
            y, sr = librosa.load(video_path, sr=None)
            
            # تحليل الطاقة
            energy = librosa.feature.rms(y=y)[0]
            
            # تحليل الطيف
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            
            # تحليل الإيقاع
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
            
            # اكتشاف الذروات الصوتية
            onset_frames = librosa.onset.onset_detect(y=y, sr=sr)
            onset_times = librosa.frames_to_time(onset_frames, sr=sr)
            
            audio_data = {
                'duration': len(y) / sr,
                'sample_rate': sr,
                'energy_mean': float(np.mean(energy)),
                'energy_std': float(np.std(energy)),
                'spectral_centroid_mean': float(np.mean(spectral_centroids)),
                'tempo': float(tempo),
                'beats_count': len(beats),
                'onsets_count': len(onset_times),
                'onset_times': onset_times.tolist()
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الصوت المتقدم: {e}")
        
        return audio_data
    
    def _detect_excitement_moments(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """اكتشاف اللحظات المثيرة بناءً على التحليل المتقدم"""
        excitement_moments = []
        
        try:
            # تحليل المشاعر للعثور على لحظات الإثارة
            emotion_data = analysis_results.get('emotion_analysis', [])
            for emotion in emotion_data:
                if emotion['dominant_emotion'] in ['happy', 'surprise'] and emotion['confidence'] > 0.7:
                    excitement_moments.append({
                        'timestamp': emotion['timestamp'],
                        'type': 'emotion_excitement',
                        'confidence': emotion['confidence'],
                        'emotion': emotion['dominant_emotion']
                    })
            
            # تحليل الحركة للعثور على نشاط عالي
            pose_data = analysis_results.get('pose_analysis', [])
            if pose_data:
                movements = [p['movement_spread'] for p in pose_data]
                movement_threshold = np.mean(movements) + 2 * np.std(movements)
                
                for pose in pose_data:
                    if pose['movement_spread'] > movement_threshold:
                        excitement_moments.append({
                            'timestamp': pose['timestamp'],
                            'type': 'high_movement',
                            'confidence': min(pose['movement_spread'] / movement_threshold, 1.0),
                            'movement_level': pose['movement_spread']
                        })
            
            # تحليل الصوت للعثور على ذروات
            audio_data = analysis_results.get('audio_analysis', {})
            onset_times = audio_data.get('onset_times', [])
            for onset_time in onset_times:
                excitement_moments.append({
                    'timestamp': onset_time,
                    'type': 'audio_peak',
                    'confidence': 0.6
                })
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف اللحظات المثيرة: {e}")
        
        return excitement_moments
    
    def _detect_reaction_moments(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """اكتشاف ردود الأفعال"""
        reaction_moments = []
        
        try:
            # البحث عن تغييرات مفاجئة في المشاعر
            emotion_data = analysis_results.get('emotion_analysis', [])
            
            for i in range(1, len(emotion_data)):
                prev_emotion = emotion_data[i-1]
                curr_emotion = emotion_data[i]
                
                # إذا تغيرت المشاعر بشكل كبير
                if (prev_emotion['dominant_emotion'] != curr_emotion['dominant_emotion'] and 
                    curr_emotion['confidence'] > 0.6):
                    
                    reaction_moments.append({
                        'timestamp': curr_emotion['timestamp'],
                        'type': 'emotion_change',
                        'from_emotion': prev_emotion['dominant_emotion'],
                        'to_emotion': curr_emotion['dominant_emotion'],
                        'confidence': curr_emotion['confidence']
                    })
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف ردود الأفعال: {e}")
        
        return reaction_moments
    
    def get_available_features(self) -> Dict[str, bool]:
        """الحصول على الميزات المتاحة"""
        return self.available_features.copy()
    
    def is_advanced_analysis_available(self) -> bool:
        """التحقق من توفر التحليل المتقدم"""
        return any(self.available_features.values())
