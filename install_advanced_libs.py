#!/usr/bin/env python3
"""
تثبيت المكتبات المتقدمة للتحليل المحسن
Install advanced libraries for enhanced analysis
"""

import subprocess
import sys
import logging

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def install_package(package_name, description=""):
    """تثبيت مكتبة واحدة"""
    try:
        logger.info(f"🔄 تثبيت {package_name}...")
        if description:
            logger.info(f"   📝 {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"✅ تم تثبيت {package_name} بنجاح")
            return True
        else:
            logger.error(f"❌ فشل تثبيت {package_name}: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ انتهت مهلة تثبيت {package_name}")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في تثبيت {package_name}: {e}")
        return False

def main():
    """تثبيت جميع المكتبات المتقدمة"""
    print("🚀 تثبيت المكتبات المتقدمة للتحليل المحسن")
    print("=" * 50)
    
    # قائمة المكتبات المتقدمة
    advanced_packages = [
        # ("mediapipe>=0.10.0", "تحليل الوجوه والوضعيات المتقدم"), # Not available for Python 3.13 yet
        ("deepface", "تحليل المشاعر المتقدم"),
        ("py-feat>=0.5.0", "تحليل وحدات الحركة"),
        ("yt-dlp>=2023.7.6", "تحميل YouTube محسن"),
        ("speech-recognition>=3.10.0", "تحويل الصوت إلى نص"),
        ("tensorflow", "مكتبة التعلم العميق (مطلوبة لـ DeepFace)"),
        ("torch>=2.0.0", "PyTorch (مطلوبة لـ Py-Feat)"),
        ("torchvision>=0.15.0", "PyTorch Vision"),
        ("opencv-contrib-python", "OpenCV مع الإضافات")
    ]
    
    # المكتبات الأساسية (إذا لم تكن مثبتة)
    basic_packages = [
        ("librosa>=0.10.1", "تحليل الصوت المتقدم"),
        ("scikit-learn>=1.3.0", "خوارزميات التعلم الآلي"),
        ("pandas>=2.0.0", "معالجة البيانات"),
        ("matplotlib>=3.7.0", "الرسوم البيانية"),
        ("seaborn>=0.12.0", "رسوم بيانية متقدمة")
    ]
    
    installed = 0
    failed = 0
    total = len(advanced_packages) + len(basic_packages)
    
    print("📦 تثبيت المكتبات الأساسية...")
    for package, description in basic_packages:
        if install_package(package, description):
            installed += 1
        else:
            failed += 1
    
    print("\n🧠 تثبيت المكتبات المتقدمة...")
    for package, description in advanced_packages:
        if install_package(package, description):
            installed += 1
        else:
            failed += 1
    
    # النتائج
    print("\n" + "=" * 50)
    print("📊 نتائج التثبيت:")
    print(f"✅ نجح: {installed}/{total}")
    print(f"❌ فشل: {failed}/{total}")
    
    if failed == 0:
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("\n🔄 يُنصح بإعادة تشغيل التطبيق للاستفادة من الميزات الجديدة")
    else:
        print("⚠️ بعض المكتبات فشلت في التثبيت")
        print("💡 يمكنك تشغيل التطبيق بالمكتبات المتاحة")
    
    print("\n📝 ملاحظات:")
    print("• MediaPipe: تحليل متقدم للوجوه والوضعيات")
    print("• DeepFace: تحليل المشاعر بدقة عالية")
    print("• Py-Feat: تحليل وحدات الحركة الدقيقة")
    print("• yt-dlp: تحميل محسن من YouTube")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التثبيت بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
