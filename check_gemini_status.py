#!/usr/bin/env python3
"""
فحص سريع لحالة مفاتيح Gemini
Quick Gemini Keys Status Check
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_gemini_status():
    """فحص حالة مفاتيح Gemini وعرض النتائج"""
    
    print("🔍 فحص حالة مفاتيح Gemini API...")
    print("=" * 50)
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        
        # الحصول على ملخص حالة المفاتيح
        status_summary = gemini_key_manager.get_keys_status_summary()
        
        total_keys = status_summary['total_keys']
        available_keys = status_summary['available_keys']
        blocked_keys = status_summary['blocked_keys']
        current_key = status_summary['current_key_suffix']
        
        print(f"📊 الإحصائيات العامة:")
        print(f"   إجمالي المفاتيح: {total_keys}")
        print(f"   المفاتيح المتاحة: {available_keys}")
        print(f"   المفاتيح المحظورة: {blocked_keys}")
        print(f"   المفتاح الحالي: {current_key}")

        # عرض حالة المفاتيح الجديدة
        try:
            new_status = gemini_key_manager.get_new_keys_status()
            print(f"   🆕 المفاتيح الجديدة: {new_status['available_new_keys']}/{new_status['total_new_keys']} متاح")
        except:
            pass

        # تحديد الحالة العامة
        if available_keys == 0 and total_keys > 0:
            print(f"\n⚠️  حالة النظام: جميع المفاتيح محظورة")
            
            # أولاً: محاولة إعطاء أولوية للمفاتيح الجديدة
            print(f"\n🆕 إعطاء أولوية للمفاتيح الجديدة...")
            new_reset_count = gemini_key_manager.prioritize_new_keys()

            if new_reset_count > 0:
                print(f"✅ تم إعادة تعيين {new_reset_count} مفتاح جديد!")

                # تحديث الحالة
                status_summary = gemini_key_manager.get_keys_status_summary()
                available_keys = status_summary['available_keys']

                if available_keys > 0:
                    print(f"🎉 النظام جاهز للاستخدام مع المفاتيح الجديدة!")
                    return True

            # ثانياً: محاولة إعادة تفعيل المفاتيح القديمة
            print(f"\n🔄 محاولة إعادة تفعيل المفاتيح القديمة...")
            reset_count = gemini_key_manager.reset_old_keys(hours_threshold=23.5)
            
            if reset_count > 0:
                print(f"✅ تم إعادة تفعيل {reset_count} مفتاح بنجاح!")
                
                # تحديث الحالة
                status_summary = gemini_key_manager.get_keys_status_summary()
                available_keys = status_summary['available_keys']
                print(f"📊 المفاتيح المتاحة الآن: {available_keys}")
                
                if available_keys > 0:
                    print(f"🎉 النظام جاهز للاستخدام مع جميع الميزات!")
                    return True
            else:
                print(f"❌ لم يتم إعادة تفعيل أي مفتاح")
                
                # عرض وقت توفر المفتاح التالي
                next_available = gemini_key_manager._get_next_key_available_time()
                if next_available:
                    print(f"⏰ أقرب مفتاح سيتوفر خلال: {next_available}")
                
                print(f"🛡️  سيتم استخدام النظام الاحتياطي للتحليل")
                return False
                
        elif available_keys > 0:
            print(f"\n✅ حالة النظام: مفاتيح Gemini تعمل بشكل طبيعي")
            print(f"🎉 النظام جاهز للاستخدام مع جميع الميزات!")
            return True
            
        else:
            print(f"\n⚠️  حالة النظام: لا توجد مفاتيح Gemini مُعرفة")
            print(f"💡 يمكنك إضافة مفاتيح في ملف .env")
            print(f"🛡️  سيتم استخدام النظام الاحتياطي للتحليل")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد مدير مفاتيح Gemini: {e}")
        print(f"🛡️  سيتم استخدام النظام الاحتياطي للتحليل")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في فحص مفاتيح Gemini: {e}")
        print(f"🛡️  سيتم استخدام النظام الاحتياطي للتحليل")
        return False

def test_current_key():
    """اختبار المفتاح الحالي"""
    print(f"\n🧪 اختبار المفتاح الحالي...")
    print("-" * 30)
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        from ai.gemini_client import GeminiClient
        
        current_key = gemini_key_manager.get_current_key()
        if not current_key:
            print(f"❌ لا يوجد مفتاح متاح للاختبار")
            return False
        
        print(f"🔑 المفتاح الحالي: ...{current_key[-10:]}")
        
        # اختبار بسيط
        client = GeminiClient()
        result = client.analyze_text("مرحبا", "test")
        
        if result:
            print(f"✅ المفتاح يعمل بشكل صحيح")
            gemini_key_manager.record_successful_request(current_key)
            return True
        else:
            print(f"❌ المفتاح لا يعمل أو محظور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المفتاح: {e}")
        return False

def show_recommendations():
    """عرض التوصيات بناءً على حالة النظام"""
    print(f"\n💡 التوصيات:")
    print("-" * 20)
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        
        status_summary = gemini_key_manager.get_keys_status_summary()
        available_keys = status_summary['available_keys']
        total_keys = status_summary['total_keys']
        
        if available_keys == 0 and total_keys > 0:
            print(f"1. استخدم أداة إدارة المفاتيح:")
            print(f"   python manage_gemini_keys.py")
            print(f"2. أو انتظر حتى يتم إعادة تفعيل المفاتيح تلقائياً")
            print(f"3. النظام سيعمل بالنظام الاحتياطي في الوقت الحالي")
            
        elif available_keys > 0:
            print(f"1. النظام يعمل بشكل مثالي!")
            print(f"2. يمكنك استخدام جميع الميزات المتقدمة")
            
        else:
            print(f"1. أضف مفاتيح Gemini في ملف .env")
            print(f"2. أو استخدم النظام الاحتياطي (يعمل بدون مفاتيح)")
            
    except Exception:
        print(f"1. تأكد من تثبيت جميع المتطلبات")
        print(f"2. استخدم النظام الاحتياطي إذا لزم الأمر")

def main():
    """الدالة الرئيسية"""
    print("🚀 فحص حالة مفاتيح Gemini API")
    print("=" * 60)
    
    # فحص الحالة العامة
    status_ok = check_gemini_status()
    
    # اختبار المفتاح الحالي إذا كان متاحاً
    if status_ok:
        test_current_key()
    
    # عرض التوصيات
    show_recommendations()
    
    print(f"\n" + "=" * 60)
    
    if status_ok:
        print(f"🎉 النتيجة: النظام جاهز للاستخدام مع جميع الميزات!")
    else:
        print(f"⚠️  النتيجة: سيتم استخدام النظام الاحتياطي")
    
    print(f"=" * 60)

if __name__ == "__main__":
    main()
