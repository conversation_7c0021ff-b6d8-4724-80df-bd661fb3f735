#!/usr/bin/env python3
"""
إعداد متغيرات البيئة لتقليل التحذيرات
Environment Setup for Reducing Warnings

يقوم بإعداد متغيرات البيئة المناسبة لتشغيل التطبيق بدون تحذيرات مزعجة
"""

import os
import sys
import logging

def setup_tensorflow_environment():
    """إعداد متغيرات البيئة لـ TensorFlow"""
    
    # تعطيل تحذيرات TensorFlow
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # تعطيل INFO و WARNING
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # تعطيل oneDNN
    
    # تعطيل تحذيرات protobuf
    os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION'] = 'python'
    
    # تعطيل تحذيرات CUDA إذا لم تكن متوفرة
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
    
    print("✅ تم إعداد متغيرات البيئة لـ TensorFlow")

def setup_pytorch_environment():
    """إعداد متغيرات البيئة لـ PyTorch"""
    
    # تعطيل تحذيرات PyTorch
    os.environ['PYTORCH_DISABLE_PER_OP_PROFILING'] = '1'
    
    print("✅ تم إعداد متغيرات البيئة لـ PyTorch")

def setup_general_environment():
    """إعداد متغيرات البيئة العامة"""
    
    # تعطيل تحذيرات Python العامة
    import warnings
    warnings.filterwarnings('ignore', category=UserWarning)
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=DeprecationWarning)
    
    # إعداد الترميز
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    print("✅ تم إعداد متغيرات البيئة العامة")

def setup_logging_environment():
    """إعداد نظام السجلات لتقليل الضوضاء"""
    
    # تعطيل سجلات المكتبات الخارجية
    logging.getLogger('tensorflow').setLevel(logging.ERROR)
    logging.getLogger('torch').setLevel(logging.ERROR)
    logging.getLogger('ultralytics').setLevel(logging.ERROR)
    logging.getLogger('PIL').setLevel(logging.ERROR)
    logging.getLogger('matplotlib').setLevel(logging.ERROR)
    
    print("✅ تم إعداد نظام السجلات")

def main():
    """إعداد البيئة الكاملة"""
    print("🔧 إعداد بيئة التشغيل...")
    print("=" * 50)
    
    try:
        setup_general_environment()
        setup_tensorflow_environment()
        setup_pytorch_environment()
        setup_logging_environment()
        
        print("\n🎉 تم إعداد البيئة بنجاح!")
        print("يمكنك الآن تشغيل التطبيق بدون تحذيرات مزعجة.")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد البيئة: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
