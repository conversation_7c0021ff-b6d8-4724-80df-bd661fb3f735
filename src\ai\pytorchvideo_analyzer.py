"""
محلل PyTorchVideo لفهم الفيديو
PyTorchVideo Video Understanding System

يستخدم PyTorchVideo لتصنيف الفيديو المتقدم وكشف الأحداث والتتبع
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import time
import torch

logger = logging.getLogger(__name__)

# محاولة استيراد PyTorchVideo
try:
    import pytorchvideo
    # محاولة استيراد النماذج والتحويلات
    try:
        from pytorchvideo.models.hub import slow_r50, slowfast_r50
        MODELS_AVAILABLE = True
    except ImportError:
        MODELS_AVAILABLE = False
        logger.warning("⚠️ نماذج PyTorchVideo غير متوفرة")

    try:
        from pytorchvideo.transforms import (
            ApplyTransformToKey,
            Normalize,
            ShortSideScale,
            UniformTemporalSubsample
        )
        from torchvision.transforms import Compose, Lambda
        TRANSFORMS_AVAILABLE = True
    except ImportError:
        TRANSFORMS_AVAILABLE = False
        logger.warning("⚠️ تحويلات PyTorchVideo غير متوفرة")

    PYTORCHVIDEO_AVAILABLE = MODELS_AVAILABLE and TRANSFORMS_AVAILABLE
    if PYTORCHVIDEO_AVAILABLE:
        logger.info("✅ PyTorchVideo متوفر")
    else:
        logger.warning("⚠️ PyTorchVideo متوفر جزئياً فقط")

except ImportError:
    PYTORCHVIDEO_AVAILABLE = False
    MODELS_AVAILABLE = False
    TRANSFORMS_AVAILABLE = False
    logger.warning("⚠️ PyTorchVideo غير متوفر - pip install pytorchvideo")

@dataclass
class ActionClassification:
    """تصنيف الفعل"""
    action_name: str
    confidence: float
    start_time: float
    end_time: float
    category: str

@dataclass
class EventDetection:
    """كشف الحدث"""
    event_type: str
    confidence: float
    timestamp: float
    duration: float
    importance_score: float
    description: str

@dataclass
class TemporalAnalysis:
    """التحليل الزمني"""
    clip_start: float
    clip_end: float
    actions: List[ActionClassification]
    events: List[EventDetection]
    scene_changes: List[float]
    activity_timeline: Dict[str, List[Tuple[float, float]]]
    narrative_flow: str

class PyTorchVideoAnalyzer:
    """محلل PyTorchVideo لفهم الفيديو"""
    
    def __init__(self, model_type: str = "slow_r50"):
        """تهيئة محلل PyTorchVideo
        
        Args:
            model_type: نوع النموذج (slow_r50, slowfast_r50)
        """
        self.available = PYTORCHVIDEO_AVAILABLE
        self.model = None
        self.model_type = model_type
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # تصنيفات الأفعال (Kinetics-400)
        self.action_categories = {
            'sports': ['playing basketball', 'playing football', 'playing tennis', 'swimming'],
            'gaming': ['playing video games', 'using computer', 'typing'],
            'social': ['talking', 'laughing', 'clapping', 'waving hand'],
            'entertainment': ['dancing', 'singing', 'playing music', 'performing'],
            'daily': ['eating', 'drinking', 'cooking', 'cleaning'],
            'reaction': ['surprised', 'excited', 'celebrating', 'cheering']
        }
        
        # أنواع الأحداث
        self.event_types = {
            'highlight': 'لحظة مميزة',
            'reaction': 'رد فعل',
            'transition': 'انتقال',
            'climax': 'ذروة',
            'funny': 'مضحك',
            'exciting': 'مثير',
            'shocking': 'صادم'
        }
        
        if self.available:
            self._load_model()
    
    def _load_model(self):
        """تحميل نموذج PyTorchVideo"""
        try:
            logger.info(f"تحميل نموذج PyTorchVideo: {self.model_type}")
            
            if self.model_type == "slow_r50":
                self.model = slow_r50(pretrained=True)
            elif self.model_type == "slowfast_r50":
                self.model = slowfast_r50(pretrained=True)
            else:
                raise ValueError(f"نوع نموذج غير مدعوم: {self.model_type}")
            
            self.model = self.model.to(self.device)
            self.model.eval()
            
            # إعداد التحويلات
            self.transform = self._create_transform()
            
            logger.info("✅ تم تحميل نموذج PyTorchVideo بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل نموذج PyTorchVideo: {e}")
            self.available = False
    
    def _create_transform(self):
        """إنشاء تحويلات الفيديو"""
        try:
            side_size = 256
            mean = [0.45, 0.45, 0.45]
            std = [0.225, 0.225, 0.225]
            crop_size = 256
            num_frames = 8
            
            transform = Compose([
                ApplyTransformToKey(
                    key="video",
                    transform=Compose([
                        UniformTemporalSubsample(num_frames),
                        Lambda(lambda x: x / 255.0),
                        Normalize(mean, std),
                        ShortSideScale(size=side_size),
                        Lambda(lambda x: x[:, :, :crop_size, :crop_size])
                    ]),
                ),
            ])
            
            return transform
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التحويلات: {e}")
            return None
    
    def is_available(self) -> bool:
        """فحص توفر PyTorchVideo"""
        return self.available and self.model is not None
    
    def analyze_video_clip(self, video_path: str, start_time: float, 
                          end_time: float) -> TemporalAnalysis:
        """تحليل مقطع فيديو
        
        Args:
            video_path: مسار الفيديو
            start_time: وقت البداية بالثواني
            end_time: وقت النهاية بالثواني
            
        Returns:
            التحليل الزمني
        """
        if not self.is_available():
            return TemporalAnalysis(
                clip_start=start_time,
                clip_end=end_time,
                actions=[],
                events=[],
                scene_changes=[],
                activity_timeline={},
                narrative_flow='unknown'
            )
        
        try:
            # استخراج مقطع الفيديو
            video_frames = self._extract_video_clip(video_path, start_time, end_time)
            
            if video_frames is None:
                logger.error("فشل في استخراج مقطع الفيديو")
                return self._empty_analysis(start_time, end_time)
            
            # تصنيف الأفعال
            actions = self._classify_actions(video_frames, start_time, end_time)
            
            # كشف الأحداث
            events = self._detect_events(video_frames, actions, start_time, end_time)
            
            # كشف تغييرات المشهد
            scene_changes = self._detect_scene_changes(video_frames, start_time, end_time)
            
            # تحليل الخط الزمني للنشاط
            activity_timeline = self._analyze_activity_timeline(actions, events)
            
            # تحليل التدفق السردي
            narrative_flow = self._analyze_narrative_flow(actions, events)
            
            return TemporalAnalysis(
                clip_start=start_time,
                clip_end=end_time,
                actions=actions,
                events=events,
                scene_changes=scene_changes,
                activity_timeline=activity_timeline,
                narrative_flow=narrative_flow
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل مقطع الفيديو: {e}")
            return self._empty_analysis(start_time, end_time)
    
    def _extract_video_clip(self, video_path: str, start_time: float, 
                           end_time: float) -> Optional[torch.Tensor]:
        """استخراج مقطع فيديو"""
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            frames = []
            frame_count = 0
            max_frames = end_frame - start_frame
            
            while frame_count < max_frames:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تحويل BGR إلى RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame_rgb)
                frame_count += 1
            
            cap.release()
            
            if not frames:
                return None
            
            # تحويل إلى tensor
            video_tensor = torch.from_numpy(np.array(frames)).float()
            video_tensor = video_tensor.permute(3, 0, 1, 2)  # (C, T, H, W)
            
            return video_tensor
            
        except Exception as e:
            logger.error(f"خطأ في استخراج مقطع الفيديو: {e}")
            return None
    
    def _classify_actions(self, video_frames: torch.Tensor, 
                         start_time: float, end_time: float) -> List[ActionClassification]:
        """تصنيف الأفعال"""
        try:
            # تحضير البيانات للنموذج
            video_dict = {"video": video_frames.unsqueeze(0)}
            
            if self.transform:
                video_dict = self.transform(video_dict)
            
            video_input = video_dict["video"].to(self.device)
            
            # تشغيل النموذج
            with torch.no_grad():
                predictions = self.model(video_input)
                probabilities = torch.nn.functional.softmax(predictions, dim=1)
            
            # الحصول على أفضل التنبؤات
            top_k = 5
            top_probs, top_indices = torch.topk(probabilities, top_k)
            
            actions = []
            for i in range(top_k):
                confidence = float(top_probs[0][i])
                if confidence > 0.1:  # عتبة الثقة
                    # محاكاة اسم الفعل (في التطبيق الحقيقي من Kinetics-400 labels)
                    action_name = f"action_{int(top_indices[0][i])}"
                    category = self._categorize_action(action_name)
                    
                    action = ActionClassification(
                        action_name=action_name,
                        confidence=confidence,
                        start_time=start_time,
                        end_time=end_time,
                        category=category
                    )
                    actions.append(action)
            
            return actions
            
        except Exception as e:
            logger.error(f"خطأ في تصنيف الأفعال: {e}")
            return []
    
    def _categorize_action(self, action_name: str) -> str:
        """تصنيف الفعل إلى فئة"""
        action_lower = action_name.lower()
        
        for category, keywords in self.action_categories.items():
            if any(keyword in action_lower for keyword in keywords):
                return category
        
        return 'other'
    
    def _detect_events(self, video_frames: torch.Tensor, 
                      actions: List[ActionClassification],
                      start_time: float, end_time: float) -> List[EventDetection]:
        """كشف الأحداث"""
        try:
            events = []
            duration = end_time - start_time
            
            # تحليل الأفعال لكشف الأحداث
            for action in actions:
                if action.confidence > 0.5:
                    event_type = self._classify_event_type(action)
                    importance_score = self._calculate_importance(action, actions)
                    
                    event = EventDetection(
                        event_type=event_type,
                        confidence=action.confidence,
                        timestamp=start_time + duration / 2,  # منتصف المقطع
                        duration=duration,
                        importance_score=importance_score,
                        description=f"{action.action_name} في فئة {action.category}"
                    )
                    events.append(event)
            
            # ترتيب حسب الأهمية
            events.sort(key=lambda x: x.importance_score, reverse=True)
            
            return events[:3]  # أفضل 3 أحداث
            
        except Exception as e:
            logger.error(f"خطأ في كشف الأحداث: {e}")
            return []
    
    def _classify_event_type(self, action: ActionClassification) -> str:
        """تصنيف نوع الحدث"""
        if action.category == 'reaction':
            return 'reaction'
        elif action.category == 'sports' and action.confidence > 0.7:
            return 'exciting'
        elif action.category == 'social' and action.confidence > 0.6:
            return 'highlight'
        elif action.confidence > 0.8:
            return 'climax'
        else:
            return 'transition'
    
    def _calculate_importance(self, action: ActionClassification, 
                            all_actions: List[ActionClassification]) -> float:
        """حساب أهمية الحدث"""
        importance = action.confidence * 50
        
        # زيادة الأهمية للفئات المثيرة
        if action.category in ['reaction', 'sports', 'entertainment']:
            importance += 20
        
        # زيادة الأهمية إذا كان الفعل نادراً
        same_category_count = len([a for a in all_actions if a.category == action.category])
        if same_category_count == 1:
            importance += 15
        
        return min(importance, 100.0)
    
    def _detect_scene_changes(self, video_frames: torch.Tensor, 
                             start_time: float, end_time: float) -> List[float]:
        """كشف تغييرات المشهد"""
        try:
            # تحويل إلى numpy للمعالجة
            frames_np = video_frames.permute(1, 2, 3, 0).numpy()
            
            scene_changes = []
            threshold = 0.3
            
            for i in range(1, len(frames_np)):
                # حساب الفرق بين الإطارات المتتالية
                diff = np.mean(np.abs(frames_np[i] - frames_np[i-1]))
                
                if diff > threshold:
                    # حساب الوقت النسبي
                    relative_time = (i / len(frames_np)) * (end_time - start_time)
                    absolute_time = start_time + relative_time
                    scene_changes.append(absolute_time)
            
            return scene_changes
            
        except Exception as e:
            logger.error(f"خطأ في كشف تغييرات المشهد: {e}")
            return []
    
    def _analyze_activity_timeline(self, actions: List[ActionClassification], 
                                  events: List[EventDetection]) -> Dict[str, List[Tuple[float, float]]]:
        """تحليل الخط الزمني للنشاط"""
        try:
            timeline = {}
            
            # تجميع الأفعال حسب الفئة
            for action in actions:
                if action.category not in timeline:
                    timeline[action.category] = []
                timeline[action.category].append((action.start_time, action.end_time))
            
            # إضافة الأحداث
            for event in events:
                event_key = f"event_{event.event_type}"
                if event_key not in timeline:
                    timeline[event_key] = []
                timeline[event_key].append((event.timestamp, event.timestamp + event.duration))
            
            return timeline
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الخط الزمني: {e}")
            return {}
    
    def _analyze_narrative_flow(self, actions: List[ActionClassification], 
                               events: List[EventDetection]) -> str:
        """تحليل التدفق السردي"""
        try:
            if not actions and not events:
                return 'static'
            
            # تحليل تنوع الأفعال
            action_categories = set(action.category for action in actions)
            
            # تحليل كثافة الأحداث
            high_importance_events = [e for e in events if e.importance_score > 70]
            
            if len(high_importance_events) > 1:
                return 'dynamic'
            elif len(action_categories) > 2:
                return 'varied'
            elif any(action.category == 'reaction' for action in actions):
                return 'reactive'
            else:
                return 'steady'
                
        except Exception as e:
            logger.error(f"خطأ في تحليل التدفق السردي: {e}")
            return 'unknown'
    
    def _empty_analysis(self, start_time: float, end_time: float) -> TemporalAnalysis:
        """تحليل فارغ في حالة الخطأ"""
        return TemporalAnalysis(
            clip_start=start_time,
            clip_end=end_time,
            actions=[],
            events=[],
            scene_changes=[],
            activity_timeline={},
            narrative_flow='error'
        )
    
    def analyze_long_video(self, video_path: str, segment_duration: float = 30.0) -> List[TemporalAnalysis]:
        """تحليل فيديو طويل بتقسيمه إلى مقاطع
        
        Args:
            video_path: مسار الفيديو
            segment_duration: مدة كل مقطع بالثواني
            
        Returns:
            قائمة تحليلات المقاطع
        """
        if not self.is_available():
            return []
        
        try:
            # الحصول على مدة الفيديو
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            total_duration = total_frames / fps
            cap.release()
            
            analyses = []
            current_time = 0.0
            
            while current_time < total_duration:
                end_time = min(current_time + segment_duration, total_duration)
                
                logger.info(f"تحليل المقطع: {current_time:.1f}s - {end_time:.1f}s")
                
                analysis = self.analyze_video_clip(video_path, current_time, end_time)
                analyses.append(analysis)
                
                current_time = end_time
            
            logger.info(f"تم تحليل {len(analyses)} مقطع من الفيديو الطويل")
            return analyses
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الفيديو الطويل: {e}")
            return []
