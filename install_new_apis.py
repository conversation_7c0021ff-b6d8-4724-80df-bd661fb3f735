#!/usr/bin/env python3
"""
سكريبت تثبيت المكتبات الجديدة لخدمات الذكاء الاصطناعي
Installation script for new AI services libraries
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """تشغيل أمر مع معالجة الأخطاء"""
    print(f"\n🔄 {description}...")
    print(f"📝 الأمر: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"✅ {description} - تم بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - فشل")
        print(f"خطأ: {e.stderr}")
        return False

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    print(f"📊 إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def install_basic_requirements():
    """تثبيت المتطلبات الأساسية"""
    print("\n" + "="*60)
    print("📦 تثبيت المتطلبات الأساسية")
    print("="*60)
    
    # تحديث pip
    if not run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "تحديث pip"
    ):
        return False
    
    # تثبيت المتطلبات الأساسية
    if not run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "تثبيت المتطلبات الأساسية"
    ):
        return False
    
    return True

def install_new_api_libraries():
    """تثبيت مكتبات APIs الجديدة"""
    print("\n" + "="*60)
    print("🆕 تثبيت مكتبات APIs الجديدة")
    print("="*60)
    
    # تثبيت المكتبات الجديدة
    if not run_command(
        f"{sys.executable} -m pip install -r requirements_new_apis.txt",
        "تثبيت مكتبات APIs الجديدة"
    ):
        return False
    
    return True

def install_optional_libraries():
    """تثبيت المكتبات الاختيارية"""
    print("\n" + "="*60)
    print("🔧 تثبيت المكتبات الاختيارية")
    print("="*60)
    
    optional_libraries = [
        ("mediapipe", "تحليل الوضعيات والوجوه المتقدم"),
        ("deepface", "تحليل المشاعر المتقدم"),
        ("py-feat", "تحليل وحدات الحركة"),
        ("tensorflow", "التعلم العميق (للمكتبات المتقدمة)"),
        ("torch", "PyTorch للتعلم العميق")
    ]
    
    installed_count = 0
    
    for lib_name, description in optional_libraries:
        print(f"\n🔄 محاولة تثبيت {lib_name} ({description})...")
        
        if run_command(
            f"{sys.executable} -m pip install {lib_name}",
            f"تثبيت {lib_name}"
        ):
            installed_count += 1
        else:
            print(f"⚠️ تخطي {lib_name} - قد يكون اختيارياً أو يحتاج متطلبات إضافية")
    
    print(f"\n📊 تم تثبيت {installed_count} من {len(optional_libraries)} مكتبة اختيارية")
    return True

def verify_installation():
    """التحقق من التثبيت"""
    print("\n" + "="*60)
    print("🔍 التحقق من التثبيت")
    print("="*60)
    
    # قائمة المكتبات للفحص
    libraries_to_check = [
        ("boto3", "Amazon AWS SDK"),
        ("azure.cognitiveservices.vision.computervision", "Azure Computer Vision"),
        ("clarifai_grpc", "Clarifai gRPC"),
        ("requests", "HTTP Requests"),
        ("PIL", "Pillow (معالجة الصور)"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy")
    ]
    
    success_count = 0
    
    for lib_name, description in libraries_to_check:
        try:
            __import__(lib_name)
            print(f"✅ {description} - متوفر")
            success_count += 1
        except ImportError:
            print(f"❌ {description} - غير متوفر")
    
    print(f"\n📊 نتيجة الفحص: {success_count}/{len(libraries_to_check)} مكتبة متوفرة")
    
    if success_count >= len(libraries_to_check) * 0.8:  # 80% نجاح
        print("✅ التثبيت ناجح بشكل عام")
        return True
    else:
        print("⚠️ قد تحتاج لحل بعض المشاكل")
        return False

def create_test_script():
    """إنشاء سكريبت اختبار"""
    print("\n🧪 إنشاء سكريبت اختبار...")
    
    test_script_content = '''#!/usr/bin/env python3
"""
سكريبت اختبار المكتبات الجديدة
Test script for new libraries
"""

import sys
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_new_apis():
    """اختبار APIs الجديدة"""
    print("🧪 اختبار APIs الجديدة...")
    
    # اختبار AWS Rekognition
    try:
        from src.ai.aws_rekognition_client import AWSRekognitionClient
        aws_client = AWSRekognitionClient()
        status = aws_client.get_service_status()
        print(f"AWS Rekognition: {'✅' if status['available'] else '❌'}")
    except Exception as e:
        print(f"AWS Rekognition: ❌ ({e})")
    
    # اختبار Azure Computer Vision
    try:
        from src.ai.azure_client import AzureComputerVisionClient
        azure_client = AzureComputerVisionClient()
        print(f"Azure Computer Vision: {'✅' if azure_client.is_available() else '❌'}")
    except Exception as e:
        print(f"Azure Computer Vision: ❌ ({e})")
    
    # اختبار Clarifai
    try:
        from src.ai.clarifai_client import ClarifaiClient
        clarifai_client = ClarifaiClient()
        status = clarifai_client.get_service_status()
        print(f"Clarifai: {'✅' if status['available'] else '❌'}")
    except Exception as e:
        print(f"Clarifai: ❌ ({e})")

if __name__ == "__main__":
    test_new_apis()
'''
    
    try:
        with open("test_new_apis.py", "w", encoding="utf-8") as f:
            f.write(test_script_content)
        print("✅ تم إنشاء سكريبت الاختبار: test_new_apis.py")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء سكريبت الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في أداة تثبيت المكتبات الجديدة")
    print("="*60)
    
    # فحص إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # تثبيت المتطلبات الأساسية
    if not install_basic_requirements():
        print("❌ فشل في تثبيت المتطلبات الأساسية")
        sys.exit(1)
    
    # تثبيت مكتبات APIs الجديدة
    if not install_new_api_libraries():
        print("❌ فشل في تثبيت مكتبات APIs الجديدة")
        sys.exit(1)
    
    # تثبيت المكتبات الاختيارية
    install_optional_libraries()
    
    # التحقق من التثبيت
    if not verify_installation():
        print("⚠️ قد تحتاج لحل بعض المشاكل قبل المتابعة")
    
    # إنشاء سكريبت اختبار
    create_test_script()
    
    print("\n" + "="*60)
    print("🎉 انتهى التثبيت!")
    print("="*60)
    print("📝 الخطوات التالية:")
    print("1. قم بتكوين مفاتيح API في ملف .env")
    print("2. شغّل: python api_keys_manager.py setup")
    print("3. اختبر النظام: python test_new_apis.py")
    print("4. شغّل التطبيق: python main.py")

if __name__ == "__main__":
    main()
