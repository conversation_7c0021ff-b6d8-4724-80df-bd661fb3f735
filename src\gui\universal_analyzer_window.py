"""
نافذة المحلل الشامل للفيديو - يدعم البثوث والمقاطع العادية
Universal Video Analyzer Window - Supports livestreams and regular videos
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any

from ai.universal_video_analyzer import UniversalVideoAnalyzer
from utils.file_utils import FileManager
from config.settings import UISettings

logger = logging.getLogger(__name__)

class UniversalAnalyzerWindow:
    """نافذة المحلل الشامل للفيديو"""
    
    def __init__(self, parent):
        self.parent = parent
        self.analyzer = UniversalVideoAnalyzer()
        self.file_manager = FileManager()
        
        # متغيرات النافذة
        self.window = None
        self.current_video_path: Optional[str] = None
        self.analysis_results: List[Dict[str, Any]] = []
        self.is_analyzing = False
        
        # إنشاء النافذة
        self.create_window()
        self.create_widgets()
        
        logger.info("تم فتح نافذة المحلل الشامل")
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("محلل الفيديو الشامل - البثوث والمقاطع العادية")
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إعداد الإغلاق
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # عنوان النافذة
        title_label = ttk.Label(main_frame, text="محلل الفيديو الشامل", 
                               font=UISettings.HEADER_FONT)
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # قسم اختيار الملف
        file_frame = ttk.LabelFrame(main_frame, text="اختيار الفيديو", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # مسار الملف
        ttk.Label(file_frame, text="الملف:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state="readonly")
        file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(file_frame, text="تصفح", command=self.browse_file).grid(row=0, column=2)
        
        # نوع الفيديو
        ttk.Label(file_frame, text="نوع الفيديو:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.video_type_var = tk.StringVar(value="auto")
        type_combo = ttk.Combobox(file_frame, textvariable=self.video_type_var, 
                                 values=["auto", "livestream", "regular"], state="readonly")
        type_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        
        # عدد المقاطع
        ttk.Label(file_frame, text="عدد المقاطع:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.clips_count_var = tk.StringVar(value="تلقائي")
        clips_combo = ttk.Combobox(file_frame, textvariable=self.clips_count_var,
                                  values=["تلقائي", "1", "2", "3", "4", "5", "10", "15", "20"])
        clips_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        
        # أزرار التحكم
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=(0, 10))
        
        self.analyze_button = ttk.Button(control_frame, text="بدء التحليل", 
                                        command=self.start_analysis)
        self.analyze_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="إيقاف", 
                                     command=self.stop_analysis, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="فتح مجلد النتائج", 
                  command=self.open_results_folder).pack(side=tk.LEFT)
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                           mode='indeterminate')
        self.progress_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # حالة التحليل
        self.status_var = tk.StringVar(value="جاهز للتحليل")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=4, column=0, columnspan=3, pady=(0, 10))
        
        # قائمة النتائج
        results_frame = ttk.LabelFrame(main_frame, text="نتائج التحليل", padding="10")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # جدول النتائج
        columns = ("الرقم", "النوع", "المدة", "الثقة", "الوصف")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=10)
        
        # تكوين الأعمدة
        self.results_tree.heading("الرقم", text="الرقم")
        self.results_tree.heading("النوع", text="النوع")
        self.results_tree.heading("المدة", text="المدة (ث)")
        self.results_tree.heading("الثقة", text="الثقة")
        self.results_tree.heading("الوصف", text="الوصف")
        
        self.results_tree.column("الرقم", width=50)
        self.results_tree.column("النوع", width=100)
        self.results_tree.column("المدة", width=80)
        self.results_tree.column("الثقة", width=80)
        self.results_tree.column("الوصف", width=300)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # ربط الأحداث
        self.results_tree.bind("<Double-1>", self.on_result_double_click)
        
        # أزرار النتائج
        results_buttons_frame = ttk.Frame(results_frame)
        results_buttons_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(results_buttons_frame, text="تشغيل المقطع", 
                  command=self.play_selected_clip).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(results_buttons_frame, text="فتح مجلد المقطع", 
                  command=self.open_clip_folder).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(results_buttons_frame, text="حذف المقطع", 
                  command=self.delete_selected_clip).pack(side=tk.LEFT)
    
    def browse_file(self):
        """تصفح واختيار ملف الفيديو"""
        file_types = [
            ("ملفات الفيديو", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.webm *.m4v"),
            ("جميع الملفات", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="اختر ملف الفيديو",
            filetypes=file_types
        )
        
        if file_path:
            if self.analyzer.is_supported_format(file_path):
                self.current_video_path = file_path
                self.file_path_var.set(file_path)
                self.status_var.set(f"تم اختيار الملف: {Path(file_path).name}")
                
                # تنظيف النتائج السابقة
                self.clear_results()
            else:
                messagebox.showerror("خطأ", "تنسيق الملف غير مدعوم")
    
    def start_analysis(self):
        """بدء تحليل الفيديو"""
        if not self.current_video_path:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف فيديو أولاً")
            return
        
        if self.is_analyzing:
            messagebox.showwarning("تحذير", "التحليل قيد التشغيل بالفعل")
            return
        
        # تحديد عدد المقاطع
        clips_count = None
        if self.clips_count_var.get() != "تلقائي":
            try:
                clips_count = int(self.clips_count_var.get())
            except ValueError:
                clips_count = None
        
        # بدء التحليل في خيط منفصل
        self.is_analyzing = True
        self.analyze_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.progress_bar.start()
        
        analysis_thread = threading.Thread(
            target=self.run_analysis,
            args=(self.current_video_path, self.video_type_var.get(), clips_count),
            daemon=True
        )
        analysis_thread.start()
    
    def run_analysis(self, video_path: str, video_type: str, clips_count: Optional[int]):
        """تشغيل التحليل في خيط منفصل"""
        try:
            self.window.after(0, lambda: self.status_var.set("جاري التحليل..."))
            
            # تشغيل التحليل
            results = self.analyzer.analyze_video(video_path, video_type, clips_count)
            
            # تحديث الواجهة بالنتائج
            self.window.after(0, lambda: self.analysis_completed(results))
            
        except Exception as e:
            logger.error(f"خطأ في التحليل: {e}")
            self.window.after(0, lambda: self.analysis_failed(str(e)))
    
    def analysis_completed(self, results: List[Dict[str, Any]]):
        """التحليل اكتمل بنجاح"""
        self.is_analyzing = False
        self.analyze_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.progress_bar.stop()
        
        self.analysis_results = results
        self.display_results(results)
        
        if results:
            self.status_var.set(f"تم التحليل بنجاح - تم إنشاء {len(results)} مقطع")
            messagebox.showinfo("نجح", f"تم إنشاء {len(results)} مقطع بنجاح!")
        else:
            self.status_var.set("لم يتم العثور على مقاطع مناسبة")
            messagebox.showwarning("تحذير", "لم يتم العثور على مقاطع مناسبة في الفيديو")
    
    def analysis_failed(self, error_msg: str):
        """فشل التحليل"""
        self.is_analyzing = False
        self.analyze_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.progress_bar.stop()
        
        self.status_var.set("فشل التحليل")
        messagebox.showerror("خطأ", f"فشل في تحليل الفيديو:\n{error_msg}")
    
    def stop_analysis(self):
        """إيقاف التحليل"""
        # ملاحظة: إيقاف التحليل الجاري صعب، لكن يمكن تعطيل الواجهة
        self.is_analyzing = False
        self.analyze_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.progress_bar.stop()
        self.status_var.set("تم إيقاف التحليل")
    
    def display_results(self, results: List[Dict[str, Any]]):
        """عرض النتائج في الجدول"""
        # تنظيف الجدول
        self.clear_results()
        
        # إضافة النتائج
        for i, result in enumerate(results, 1):
            duration = result.get('duration', 0)
            confidence = result.get('confidence', 0)
            clip_type = result.get('type', 'غير محدد')
            description = result.get('description', 'بدون وصف')
            
            self.results_tree.insert("", "end", values=(
                i,
                clip_type,
                f"{duration:.1f}",
                f"{confidence:.2f}",
                description
            ))
    
    def clear_results(self):
        """تنظيف النتائج"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.analysis_results = []
    
    def on_result_double_click(self, event):
        """النقر المزدوج على نتيجة"""
        self.play_selected_clip()
    
    def play_selected_clip(self):
        """تشغيل المقطع المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقطع أولاً")
            return
        
        try:
            item = self.results_tree.item(selection[0])
            clip_index = int(item['values'][0]) - 1
            
            if 0 <= clip_index < len(self.analysis_results):
                clip_path = self.analysis_results[clip_index].get('file_path')
                if clip_path and Path(clip_path).exists():
                    import os
                    os.startfile(clip_path)  # Windows
                else:
                    messagebox.showerror("خطأ", "ملف المقطع غير موجود")
            
        except Exception as e:
            logger.error(f"خطأ في تشغيل المقطع: {e}")
            messagebox.showerror("خطأ", f"لا يمكن تشغيل المقطع: {e}")
    
    def open_clip_folder(self):
        """فتح مجلد المقطع المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقطع أولاً")
            return
        
        try:
            item = self.results_tree.item(selection[0])
            clip_index = int(item['values'][0]) - 1
            
            if 0 <= clip_index < len(self.analysis_results):
                clip_path = self.analysis_results[clip_index].get('file_path')
                if clip_path and Path(clip_path).exists():
                    import os
                    os.startfile(Path(clip_path).parent)  # Windows
                else:
                    messagebox.showerror("خطأ", "ملف المقطع غير موجود")
            
        except Exception as e:
            logger.error(f"خطأ في فتح مجلد المقطع: {e}")
            messagebox.showerror("خطأ", f"لا يمكن فتح مجلد المقطع: {e}")
    
    def delete_selected_clip(self):
        """حذف المقطع المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقطع أولاً")
            return
        
        if messagebox.askyesno("تأكيد", "هل تريد حذف المقطع المحدد؟"):
            try:
                item = self.results_tree.item(selection[0])
                clip_index = int(item['values'][0]) - 1
                
                if 0 <= clip_index < len(self.analysis_results):
                    clip_path = self.analysis_results[clip_index].get('file_path')
                    if clip_path and Path(clip_path).exists():
                        Path(clip_path).unlink()
                        
                        # إزالة من القائمة والجدول
                        del self.analysis_results[clip_index]
                        self.results_tree.delete(selection[0])
                        
                        messagebox.showinfo("نجح", "تم حذف المقطع بنجاح")
                    else:
                        messagebox.showerror("خطأ", "ملف المقطع غير موجود")
                
            except Exception as e:
                logger.error(f"خطأ في حذف المقطع: {e}")
                messagebox.showerror("خطأ", f"لا يمكن حذف المقطع: {e}")
    
    def open_results_folder(self):
        """فتح مجلد النتائج"""
        try:
            import os
            from config.settings import AppSettings
            os.startfile(AppSettings.OUTPUT_DIR)  # Windows
        except Exception as e:
            logger.error(f"خطأ في فتح مجلد النتائج: {e}")
            messagebox.showerror("خطأ", f"لا يمكن فتح مجلد النتائج: {e}")
    
    def on_closing(self):
        """إغلاق النافذة"""
        if self.is_analyzing:
            if messagebox.askyesno("تأكيد", "التحليل قيد التشغيل. هل تريد الإغلاق؟"):
                self.is_analyzing = False
                self.window.destroy()
        else:
            self.window.destroy()
