#!/usr/bin/env python3
"""
تثبيت المكتبات المفقودة
Install Missing Libraries
"""

import subprocess
import sys

def install_package(package_name, description=""):
    """تثبيت حزمة واحدة"""
    print(f"🔄 تثبيت {package_name}...")
    if description:
        print(f"   📝 {description}")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name
        ])
        print(f"✅ تم تثبيت {package_name} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {package_name}: {e}")
        return False

def main():
    """تثبيت المكتبات المطلوبة"""
    print("🚀 تثبيت المكتبات المطلوبة للخدمات الجديدة")
    print("=" * 50)
    
    # قائمة المكتبات المطلوبة
    packages = [
        ("boto3", "Amazon AWS SDK"),
        ("azure-cognitiveservices-vision-computervision", "Azure Computer Vision"),
        ("clarifai-grpc", "Clarifai AI Platform"),
        ("python-dotenv", "إدارة متغيرات البيئة"),
        ("requests", "HTTP Requests"),
    ]
    
    success_count = 0
    
    for package, description in packages:
        if install_package(package, description):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"📊 النتيجة: تم تثبيت {success_count}/{len(packages)} حزمة")
    
    if success_count == len(packages):
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("\n📝 الخطوات التالية:")
        print("1. قم بتكوين مفاتيح API في ملف .env")
        print("2. شغّل: python api_keys_manager.py setup")
        print("3. اختبر النظام: python api_keys_manager.py check")
    else:
        print("⚠️ بعض المكتبات لم يتم تثبيتها. حاول مرة أخرى أو ثبتها يدوياً.")

if __name__ == "__main__":
    main()
