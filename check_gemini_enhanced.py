#!/usr/bin/env python3
"""
أداة فحص مفاتيح Gemini المحسنة
Enhanced Gemini API Keys Checker

هذه الأداة تقوم بـ:
- فحص جميع مفاتيح Gemini المتاحة
- التحقق من صحة كل مفتاح
- عرض تقرير مفصل عن الحالة
- إعطاء توصيات للاستخدام الأمثل
"""

import os
import sys
import json
import requests
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

class GeminiKeysChecker:
    """فاحص مفاتيح Gemini المحسن"""
    
    def __init__(self):
        self.api_keys = self._load_gemini_keys()
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"
        self.test_results = {}
        
    def _load_gemini_keys(self) -> List[str]:
        """تحميل جميع مفاتيح Gemini من متغيرات البيئة"""
        keys = []
        
        # البحث عن مفاتيح Gemini
        for i in range(1, 11):  # البحث عن حتى 10 مفاتيح
            if i == 1:
                key_name = "GOOGLE_GEMINI_API_KEY"
            else:
                key_name = f"GOOGLE_GEMINI_API_KEY_{i}"
            
            key_value = os.getenv(key_name)
            if key_value and key_value.strip() and key_value != "your_gemini_key_here":
                keys.append(key_value.strip())
        
        return keys
    
    def _test_single_key(self, api_key: str) -> Dict[str, Any]:
        """اختبار مفتاح واحد"""
        result = {
            'key': api_key[:10] + "..." + api_key[-4:],  # إخفاء جزء من المفتاح
            'valid': False,
            'error': None,
            'response_time': None,
            'quota_info': None,
            'models_available': []
        }
        
        try:
            start_time = time.time()
            
            # اختبار الحصول على قائمة النماذج
            url = f"{self.base_url}?key={api_key}"
            response = requests.get(url, timeout=10)
            
            end_time = time.time()
            result['response_time'] = round((end_time - start_time) * 1000, 2)  # بالميلي ثانية
            
            if response.status_code == 200:
                data = response.json()
                result['valid'] = True
                
                # استخراج النماذج المتاحة
                if 'models' in data:
                    for model in data['models']:
                        model_name = model.get('name', '').split('/')[-1]
                        result['models_available'].append(model_name)
                
                # اختبار توليد نص بسيط للتحقق من الكوتا
                self._test_generation(api_key, result)
                
            elif response.status_code == 403:
                result['error'] = "مفتاح API غير صالح أو منتهي الصلاحية"
            elif response.status_code == 429:
                result['error'] = "تم تجاوز حد الطلبات"
            else:
                result['error'] = f"خطأ HTTP {response.status_code}: {response.text[:100]}"
                
        except requests.exceptions.Timeout:
            result['error'] = "انتهت مهلة الاتصال"
        except requests.exceptions.ConnectionError:
            result['error'] = "خطأ في الاتصال بالإنترنت"
        except Exception as e:
            result['error'] = f"خطأ غير متوقع: {str(e)}"
        
        return result
    
    def _test_generation(self, api_key: str, result: Dict[str, Any]):
        """اختبار توليد نص بسيط"""
        try:
            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": "Hello"
                    }]
                }]
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                result['quota_info'] = "متاح للاستخدام"
            elif response.status_code == 429:
                result['quota_info'] = "تم تجاوز الحد اليومي"
            else:
                result['quota_info'] = f"خطأ في التوليد: {response.status_code}"
                
        except Exception as e:
            result['quota_info'] = f"خطأ في اختبار التوليد: {str(e)}"
    
    def check_all_keys(self) -> Dict[str, Any]:
        """فحص جميع المفاتيح"""
        print("🔍 فحص مفاتيح Gemini...")
        print("=" * 50)
        
        if not self.api_keys:
            return {
                'total_keys': 0,
                'valid_keys': 0,
                'invalid_keys': 0,
                'results': [],
                'recommendations': ["لا توجد مفاتيح Gemini مُكوّنة في ملف .env"]
            }
        
        results = []
        valid_count = 0
        
        for i, api_key in enumerate(self.api_keys, 1):
            print(f"🔑 فحص المفتاح {i}/{len(self.api_keys)}...")
            
            result = self._test_single_key(api_key)
            results.append(result)
            
            if result['valid']:
                valid_count += 1
                print(f"   ✅ صالح - وقت الاستجابة: {result['response_time']}ms")
            else:
                print(f"   ❌ غير صالح - {result['error']}")
        
        # إنشاء التقرير
        report = {
            'total_keys': len(self.api_keys),
            'valid_keys': valid_count,
            'invalid_keys': len(self.api_keys) - valid_count,
            'results': results,
            'recommendations': self._generate_recommendations(results)
        }
        
        return report
    
    def _generate_recommendations(self, results: List[Dict[str, Any]]) -> List[str]:
        """توليد توصيات بناءً على النتائج"""
        recommendations = []
        
        valid_keys = [r for r in results if r['valid']]
        invalid_keys = [r for r in results if not r['valid']]
        
        if not valid_keys:
            recommendations.extend([
                "❌ لا توجد مفاتيح Gemini صالحة",
                "🔧 احصل على مفتاح جديد من: https://makersuite.google.com/app/apikey",
                "📝 أضف المفتاح في ملف .env: GOOGLE_GEMINI_API_KEY=your_key_here"
            ])
        elif len(valid_keys) == 1:
            recommendations.extend([
                "✅ لديك مفتاح واحد صالح",
                "💡 فكر في إضافة مفاتيح إضافية لزيادة الحدود",
                "📊 كل مفتاح يعطي حدود منفصلة"
            ])
        else:
            recommendations.extend([
                f"✅ لديك {len(valid_keys)} مفاتيح صالحة",
                "🚀 ممتاز! يمكنك استخدام عدة مفاتيح لحدود أعلى",
                "⚡ النظام سيتنقل بين المفاتيح تلقائياً"
            ])
        
        if invalid_keys:
            recommendations.append(f"🔧 احذف أو استبدل {len(invalid_keys)} مفتاح غير صالح")
        
        # فحص أوقات الاستجابة
        response_times = [r['response_time'] for r in valid_keys if r['response_time']]
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            if avg_time > 2000:  # أكثر من ثانيتين
                recommendations.append("⚠️ أوقات الاستجابة بطيئة - تحقق من الاتصال")
            elif avg_time < 500:  # أقل من نصف ثانية
                recommendations.append("🚀 أوقات استجابة ممتازة!")
        
        return recommendations
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """حفظ التقرير في ملف"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gemini_keys_report_{timestamp}.json"
        
        try:
            # إزالة المفاتيح الكاملة من التقرير المحفوظ لأمان إضافي
            safe_report = report.copy()
            for result in safe_report['results']:
                if 'full_key' in result:
                    del result['full_key']
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(safe_report, f, ensure_ascii=False, indent=2)
            
            print(f"💾 تم حفظ التقرير في: {filename}")
            return True
        except Exception as e:
            print(f"❌ فشل في حفظ التقرير: {e}")
            return False
    
    def print_detailed_report(self, report: Dict[str, Any]):
        """طباعة تقرير مفصل"""
        print("\n" + "=" * 60)
        print("📊 تقرير مفاتيح Gemini المفصل")
        print("=" * 60)
        
        print(f"📈 إجمالي المفاتيح: {report['total_keys']}")
        print(f"✅ المفاتيح الصالحة: {report['valid_keys']}")
        print(f"❌ المفاتيح غير الصالحة: {report['invalid_keys']}")
        
        if report['total_keys'] > 0:
            success_rate = (report['valid_keys'] / report['total_keys']) * 100
            print(f"📊 معدل النجاح: {success_rate:.1f}%")
        
        print("\n🔍 تفاصيل المفاتيح:")
        print("-" * 40)
        
        for i, result in enumerate(report['results'], 1):
            status = "✅" if result['valid'] else "❌"
            print(f"{status} المفتاح {i}: {result['key']}")
            
            if result['valid']:
                print(f"   ⏱️ وقت الاستجابة: {result['response_time']}ms")
                print(f"   📊 حالة الكوتا: {result['quota_info']}")
                if result['models_available']:
                    print(f"   🤖 النماذج المتاحة: {len(result['models_available'])}")
            else:
                print(f"   ❌ الخطأ: {result['error']}")
            print()
        
        print("💡 التوصيات:")
        print("-" * 20)
        for recommendation in report['recommendations']:
            print(f"   {recommendation}")

def main():
    """الدالة الرئيسية"""
    print("🔑 أداة فحص مفاتيح Gemini المحسنة")
    print("=" * 50)
    
    checker = GeminiKeysChecker()
    
    # فحص المفاتيح
    report = checker.check_all_keys()
    
    # طباعة التقرير المفصل
    checker.print_detailed_report(report)
    
    # حفظ التقرير
    if '--save' in sys.argv:
        checker.save_report(report)
    
    # إرجاع كود الخروج المناسب
    if report['valid_keys'] == 0:
        print("\n❌ لا توجد مفاتيح صالحة - يحتاج إعداد")
        sys.exit(1)
    elif report['invalid_keys'] > 0:
        print(f"\n⚠️ يوجد {report['invalid_keys']} مفتاح غير صالح")
        sys.exit(2)
    else:
        print("\n✅ جميع المفاتيح صالحة!")
        sys.exit(0)

if __name__ == "__main__":
    main()
