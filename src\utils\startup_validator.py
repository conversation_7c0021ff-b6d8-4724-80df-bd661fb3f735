"""
مُحقق بدء التشغيل - فحص جميع المتطلبات والإعدادات
Startup Validator - Check all requirements and settings
"""

import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any
import subprocess

from config.settings import AppSettings, AISettings
from ai.gemini_client import GeminiClient
from ai.google_cloud_client import GoogleCloudClient
from ai.huggingface_client import HuggingFaceClient

logger = logging.getLogger(__name__)

class StartupValidator:
    """مُحقق بدء التشغيل للتأكد من توفر جميع المتطلبات"""
    
    def __init__(self):
        self.validation_results = {}
        self.critical_errors = []
        self.warnings = []
        
    def validate_all(self) -> Dict[str, Any]:
        """فحص جميع المتطلبات والإعدادات"""
        logger.info("🔍 بدء فحص متطلبات النظام...")
        
        # فحص FFmpeg
        self._validate_ffmpeg()
        
        # فحص المكتبات المطلوبة
        self._validate_required_libraries()
        
        # فحص مفاتيح API
        self._validate_api_keys()
        
        # فحص المجلدات المطلوبة
        self._validate_directories()
        
        # فحص المكتبات المتقدمة (اختيارية)
        self._validate_optional_libraries()
        
        # إنشاء التقرير النهائي
        return self._generate_report()
    
    def _validate_ffmpeg(self):
        """فحص توفر FFmpeg"""
        try:
            # فحص ffmpeg
            result = subprocess.run([AppSettings.FFMPEG_PATH, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("✅ FFmpeg متوفر ويعمل بشكل صحيح")
                self.validation_results['ffmpeg'] = True
            else:
                self.critical_errors.append("❌ FFmpeg غير متوفر أو لا يعمل بشكل صحيح")
                self.validation_results['ffmpeg'] = False
                
        except Exception as e:
            self.critical_errors.append(f"❌ خطأ في فحص FFmpeg: {e}")
            self.validation_results['ffmpeg'] = False
        
        try:
            # فحص ffprobe
            result = subprocess.run([AppSettings.FFPROBE_PATH, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("✅ FFprobe متوفر ويعمل بشكل صحيح")
                self.validation_results['ffprobe'] = True
            else:
                self.critical_errors.append("❌ FFprobe غير متوفر أو لا يعمل بشكل صحيح")
                self.validation_results['ffprobe'] = False
                
        except Exception as e:
            self.critical_errors.append(f"❌ خطأ في فحص FFprobe: {e}")
            self.validation_results['ffprobe'] = False
    
    def _validate_required_libraries(self):
        """فحص المكتبات المطلوبة"""
        required_libraries = [
            'tkinter', 'PIL', 'cv2', 'numpy', 'requests', 
            'moviepy', 'pydub', 'librosa'
        ]
        
        missing_libraries = []
        
        for lib in required_libraries:
            try:
                __import__(lib)
                logger.info(f"✅ مكتبة {lib} متوفرة")
                self.validation_results[f'lib_{lib}'] = True
            except ImportError:
                missing_libraries.append(lib)
                self.validation_results[f'lib_{lib}'] = False
                
        if missing_libraries:
            error_msg = f"❌ مكتبات مطلوبة غير متوفرة: {', '.join(missing_libraries)}"
            self.critical_errors.append(error_msg)
        else:
            logger.info("✅ جميع المكتبات المطلوبة متوفرة")
    
    def _validate_api_keys(self):
        """فحص مفاتيح API"""
        # فحص Gemini API
        try:
            gemini_client = GeminiClient()
            if gemini_client.is_available():
                keys_info = gemini_client.get_available_keys_info()
                if keys_info['available_keys'] > 0:
                    logger.info(f"✅ Gemini API: {keys_info['available_keys']} مفتاح متاح من أصل {keys_info['total_keys']}")
                    self.validation_results['gemini_api'] = True
                else:
                    self.warnings.append(f"⚠️ جميع مفاتيح Gemini API محظورة ({keys_info['blocked_keys']} مفتاح)")
                    self.validation_results['gemini_api'] = False
            else:
                self.warnings.append("⚠️ لا توجد مفاتيح Gemini API متاحة")
                self.validation_results['gemini_api'] = False
        except Exception as e:
            self.warnings.append(f"⚠️ خطأ في فحص Gemini API: {e}")
            self.validation_results['gemini_api'] = False
        
        # فحص Google Cloud
        try:
            gc_client = GoogleCloudClient()
            status = gc_client.get_credentials_status()
            if status['video_client_available']:
                logger.info("✅ Google Cloud Video Intelligence متوفر")
                self.validation_results['google_cloud'] = True
            else:
                self.warnings.append("⚠️ Google Cloud Video Intelligence غير متوفر")
                self.validation_results['google_cloud'] = False
        except Exception as e:
            self.warnings.append(f"⚠️ خطأ في فحص Google Cloud: {e}")
            self.validation_results['google_cloud'] = False
        
        # فحص Hugging Face
        try:
            hf_client = HuggingFaceClient()
            if hf_client.is_available():
                logger.info("✅ Hugging Face API متوفر")
                self.validation_results['huggingface_api'] = True
            else:
                self.warnings.append("⚠️ Hugging Face API غير متوفر")
                self.validation_results['huggingface_api'] = False
        except Exception as e:
            self.warnings.append(f"⚠️ خطأ في فحص Hugging Face API: {e}")
            self.validation_results['huggingface_api'] = False

        # فحص الخدمات الجديدة
        self._validate_new_ai_services()

    def _validate_new_ai_services(self):
        """فحص خدمات الذكاء الاصطناعي الجديدة"""
        # فحص AWS Rekognition
        try:
            from ..ai.aws_rekognition_client import AWSRekognitionClient
            aws_client = AWSRekognitionClient()
            if aws_client.is_available():
                logger.info("✅ AWS Rekognition متوفر")
                self.validation_results['aws_rekognition'] = True
            else:
                self.warnings.append("⚠️ AWS Rekognition غير متوفر")
                self.validation_results['aws_rekognition'] = False
        except Exception as e:
            self.warnings.append(f"⚠️ خطأ في فحص AWS Rekognition: {e}")
            self.validation_results['aws_rekognition'] = False

        # فحص Azure Computer Vision
        try:
            from ..ai.azure_client import AzureComputerVisionClient
            azure_cv_client = AzureComputerVisionClient()
            if azure_cv_client.is_available():
                logger.info("✅ Azure Computer Vision متوفر")
                self.validation_results['azure_cv'] = True
            else:
                self.warnings.append("⚠️ Azure Computer Vision غير متوفر")
                self.validation_results['azure_cv'] = False
        except Exception as e:
            self.warnings.append(f"⚠️ خطأ في فحص Azure Computer Vision: {e}")
            self.validation_results['azure_cv'] = False

        # فحص Azure Video Indexer
        try:
            from ..ai.azure_client import AzureVideoIndexerClient
            azure_vi_client = AzureVideoIndexerClient()
            if azure_vi_client.is_available():
                logger.info("✅ Azure Video Indexer متوفر")
                self.validation_results['azure_video_indexer'] = True
            else:
                self.warnings.append("⚠️ Azure Video Indexer غير متوفر")
                self.validation_results['azure_video_indexer'] = False
        except Exception as e:
            self.warnings.append(f"⚠️ خطأ في فحص Azure Video Indexer: {e}")
            self.validation_results['azure_video_indexer'] = False

        # فحص Clarifai
        try:
            from ..ai.clarifai_client import ClarifaiClient
            clarifai_client = ClarifaiClient()
            if clarifai_client.is_available():
                logger.info("✅ Clarifai متوفر")
                self.validation_results['clarifai'] = True
            else:
                self.warnings.append("⚠️ Clarifai غير متوفر")
                self.validation_results['clarifai'] = False
        except Exception as e:
            self.warnings.append(f"⚠️ خطأ في فحص Clarifai: {e}")
            self.validation_results['clarifai'] = False

    def _validate_directories(self):
        """فحص المجلدات المطلوبة"""
        required_dirs = [
            AppSettings.TEMP_DIR,
            AppSettings.OUTPUT_DIR,
            Path("data"),  # مجلد البيانات
            Path("logs")   # مجلد السجلات
        ]
        
        for dir_path in required_dirs:
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ مجلد {dir_path} متوفر")
                self.validation_results[f'dir_{dir_path.name}'] = True
            except Exception as e:
                error_msg = f"❌ خطأ في إنشاء مجلد {dir_path}: {e}"
                self.critical_errors.append(error_msg)
                self.validation_results[f'dir_{dir_path.name}'] = False
    
    def _validate_optional_libraries(self):
        """فحص المكتبات الاختيارية المتقدمة"""
        optional_libraries = {
            'mediapipe': 'تحليل الوضعيات والوجوه المتقدم',
            'deepface': 'تحليل المشاعر المتقدم',
            'py_feat': 'تحليل وحدات الحركة',
        }
        
        available_optional = []
        missing_optional = []
        
        for lib, description in optional_libraries.items():
            try:
                __import__(lib)
                available_optional.append(f"{lib} ({description})")
                self.validation_results[f'optional_{lib}'] = True
            except ImportError:
                missing_optional.append(f"{lib} ({description})")
                self.validation_results[f'optional_{lib}'] = False
        
        if available_optional:
            logger.info(f"✅ مكتبات متقدمة متوفرة: {', '.join(available_optional)}")
        
        if missing_optional:
            self.warnings.append(f"⚠️ مكتبات متقدمة غير متوفرة: {', '.join(missing_optional)}")
    
    def _generate_report(self) -> Dict[str, Any]:
        """إنشاء التقرير النهائي"""
        total_checks = len(self.validation_results)
        passed_checks = sum(1 for result in self.validation_results.values() if result)
        
        success_percentage = (passed_checks / total_checks * 100) if total_checks > 0 else 0

        report = {
            'overall_status': len(self.critical_errors) == 0,
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'failed_checks': total_checks - passed_checks,
            'success_percentage': success_percentage,
            'critical_errors': self.critical_errors,
            'warnings': self.warnings,
            'detailed_results': self.validation_results
        }
        
        # طباعة التقرير
        logger.info("=" * 60)
        logger.info("📋 تقرير فحص متطلبات النظام")
        logger.info("=" * 60)
        
        if report['overall_status']:
            logger.info("🎉 جميع المتطلبات الأساسية متوفرة!")
        else:
            logger.error("💥 توجد مشاكل في المتطلبات الأساسية!")
        
        logger.info(f"📊 النتائج: {passed_checks}/{total_checks} فحص نجح")
        
        if self.critical_errors:
            logger.error("🚨 أخطاء حرجة:")
            for error in self.critical_errors:
                logger.error(f"  {error}")
        
        if self.warnings:
            logger.warning("⚠️ تحذيرات:")
            for warning in self.warnings:
                logger.warning(f"  {warning}")
        
        logger.info("=" * 60)
        
        return report

def validate_startup() -> Dict[str, Any]:
    """دالة مساعدة لفحص بدء التشغيل"""
    validator = StartupValidator()
    return validator.validate_all()
