@echo off
chcp 65001 >nul
echo 🚀 بدء تشغيل أداة تحليل البثوث المحسنة
echo ==========================================

echo 🔍 فحص Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo 🔍 اختبار سريع للنظام...
python quick_test.py
if %errorlevel% neq 0 (
    echo ⚠️ هناك مشاكل في النظام، لكن سنحاول التشغيل...
    timeout /t 3 >nul
)

echo 🎬 تشغيل التطبيق...
python main.py

echo 📝 انتهى التشغيل
pause
