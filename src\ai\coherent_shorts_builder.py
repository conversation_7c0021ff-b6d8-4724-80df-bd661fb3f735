"""
بناء مقاطع شورتس مترابطة سردياً
Coherent Shorts Builder for Creating Narrative-Connected Videos
"""

import logging
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json

from ai.narrative_analyzer import NarrativeAnalyzer, NarrativeEvent, EventType, NarrativeArc
from core.video_processor import VideoProcessor
from ai.gemini_client import GeminiClient
from utils.file_utils import FileManager

logger = logging.getLogger(__name__)

@dataclass
class CoherentShort:
    """مقطع شورتس مترابط"""
    start_time: float
    end_time: float
    events: List[NarrativeEvent]
    story_arc: str  # "setup-climax-reaction", "challenge-attempt-result", etc.
    coherence_score: float
    viral_potential: float
    transition_points: List[float]  # نقاط الانتقال بين الأحداث
    title_suggestion: str
    description_suggestion: str
    hashtags: List[str]
    thumbnail_timestamp: float  # أفضل وقت لأخذ الصورة المصغرة

@dataclass
class TransitionSegment:
    """قطعة انتقالية لربط الأحداث"""
    start_time: float
    end_time: float
    transition_type: str  # "fade", "cut", "zoom", "text_overlay"
    purpose: str  # "context", "time_jump", "location_change"

class CoherentShortsBuilder:
    """بناء مقاطع شورتس مترابطة سردياً"""
    
    def __init__(self):
        self.narrative_analyzer = NarrativeAnalyzer()
        self.video_processor = VideoProcessor()
        self.gemini_client = GeminiClient()
        self.file_manager = FileManager()
        
        # أنماط القصص الشائعة
        self.story_patterns = {
            'challenge_pattern': {
                'sequence': [EventType.INTRODUCTION, EventType.SETUP, EventType.CLIMAX, EventType.REACTION],
                'description': 'تحدي → محاولة → نتيجة → ردة فعل',
                'viral_multiplier': 1.2
            },
            'reaction_pattern': {
                'sequence': [EventType.SETUP, EventType.BUILDUP, EventType.CLIMAX, EventType.REACTION],
                'description': 'إعداد → تصاعد → ذروة → ردة فعل',
                'viral_multiplier': 1.1
            },
            'discovery_pattern': {
                'sequence': [EventType.INTRODUCTION, EventType.BUILDUP, EventType.CLIMAX, EventType.CONCLUSION],
                'description': 'مقدمة → اكتشاف → ذروة → خلاصة',
                'viral_multiplier': 1.0
            },
            'tutorial_pattern': {
                'sequence': [EventType.INTRODUCTION, EventType.SETUP, EventType.BUILDUP, EventType.CONCLUSION],
                'description': 'مقدمة → شرح → تطبيق → نتيجة',
                'viral_multiplier': 0.9
            }
        }
        
        # قوالب العناوين والأوصاف
        self.content_templates = {
            'challenge': {
                'titles': [
                    "تحدي {theme} مع {celebrity}!",
                    "{celebrity} يجرب {theme} لأول مرة!",
                    "ردة فعل {celebrity} على {theme}",
                    "هل سينجح {celebrity} في {theme}؟"
                ],
                'descriptions': [
                    "شاهد {celebrity} وهو يواجه تحدي {theme} المجنون!",
                    "لن تصدق ردة فعل {celebrity} عندما جرب {theme}",
                    "لحظات مذهلة من تحدي {theme} مع {celebrity}"
                ],
                'hashtags': ['#تحدي', '#ردة_فعل', '#مجنون', '#لا_يصدق', '#فيروسي']
            },
            'gaming': {
                'titles': [
                    "{celebrity} يلعب {theme} - ردة فعل مجنونة!",
                    "أفضل لحظات {celebrity} في {theme}",
                    "لحظة فوز {celebrity} الملحمية في {theme}",
                    "{celebrity} ضد {theme} - من سيفوز؟"
                ],
                'descriptions': [
                    "أفضل اللحظات من بث {celebrity} وهو يلعب {theme}",
                    "ردود فعل {celebrity} المجنونة أثناء لعب {theme}",
                    "لحظات لا تُنسى من جلسة {theme} مع {celebrity}"
                ],
                'hashtags': ['#ألعاب', '#بث_مباشر', '#ردة_فعل', '#فوز', '#ملحمي']
            },
            'reaction': {
                'titles': [
                    "ردة فعل {celebrity} على {theme}",
                    "{celebrity} يشاهد {theme} لأول مرة!",
                    "لن تصدق ردة فعل {celebrity} على {theme}",
                    "{celebrity} صُدم من {theme}!"
                ],
                'descriptions': [
                    "ردة فعل {celebrity} الصادقة على {theme}",
                    "شاهد كيف تفاعل {celebrity} مع {theme}",
                    "لحظات صدمة {celebrity} أثناء مشاهدة {theme}"
                ],
                'hashtags': ['#ردة_فعل', '#صدمة', '#مفاجأة', '#أول_مرة', '#تفاعل']
            }
        }
        
        logger.info("تم تهيئة بناء مقاطع الشورتس المترابطة")

    def build_coherent_shorts(self, video_path: str, target_count: int = 3,
                            max_duration: float = 60.0) -> List[CoherentShort]:
        """بناء مقاطع شورتس مترابطة من الفيديو مع التعامل مع الحالات المختلفة"""
        try:
            logger.info(f"بدء بناء مقاطع شورتس مترابطة: {video_path}")

            # تحليل البنية السردية
            narrative_arc = self.narrative_analyzer.analyze_narrative_structure(video_path)

            if not narrative_arc.events:
                logger.warning("لم يتم العثور على أحداث سردية")
                return []

            # استراتيجية متعددة المراحل لبناء المقاطع
            coherent_shorts = []

            # المرحلة 1: البحث عن أنماط مترابطة كاملة
            connected_patterns = self._identify_best_story_patterns(narrative_arc.events)

            for pattern_name, pattern_events in connected_patterns[:target_count]:
                short = self._build_single_coherent_short(
                    pattern_events, pattern_name, narrative_arc, max_duration
                )
                if short and short.coherence_score > 0.6:  # جودة عالية فقط
                    coherent_shorts.append(short)

            # المرحلة 2: إذا لم نحصل على العدد المطلوب، ابحث عن لقطات منفصلة قابلة للدمج
            if len(coherent_shorts) < target_count:
                remaining_count = target_count - len(coherent_shorts)
                merged_shorts = self._create_merged_highlight_shorts(
                    narrative_arc.events, remaining_count, max_duration, coherent_shorts
                )
                coherent_shorts.extend(merged_shorts)

            # المرحلة 3: إذا ما زلنا نحتاج المزيد، قسم الأحداث الطويلة
            if len(coherent_shorts) < target_count:
                remaining_count = target_count - len(coherent_shorts)
                split_shorts = self._split_long_events_into_shorts(
                    narrative_arc.events, remaining_count, max_duration, coherent_shorts
                )
                coherent_shorts.extend(split_shorts)

            # ترتيب المقاطع حسب الجودة والإمكانية الفيروسية
            coherent_shorts.sort(key=lambda s: (s.viral_potential * 0.6 + s.coherence_score * 0.4), reverse=True)

            # الاحتفاظ بالعدد المطلوب فقط
            coherent_shorts = coherent_shorts[:target_count]

            logger.info(f"تم بناء {len(coherent_shorts)} مقطع شورتس (مترابط ومدمج)")
            return coherent_shorts

        except Exception as e:
            logger.error(f"خطأ في بناء مقاطع الشورتس المترابطة: {e}")
            return []

    def _identify_best_story_patterns(self, events: List[NarrativeEvent]) -> List[Tuple[str, List[NarrativeEvent]]]:
        """تحديد أفضل الأنماط السردية في الأحداث"""
        try:
            pattern_matches = []
            
            for pattern_name, pattern_info in self.story_patterns.items():
                expected_sequence = pattern_info['sequence']
                
                # البحث عن تطابقات للنمط
                matches = self._find_pattern_matches(events, expected_sequence)
                
                for match in matches:
                    # حساب جودة التطابق
                    match_quality = self._evaluate_pattern_match(match, expected_sequence)
                    pattern_matches.append((pattern_name, match, match_quality))
            
            # ترتيب التطابقات حسب الجودة
            pattern_matches.sort(key=lambda x: x[2], reverse=True)
            
            # إرجاع أفضل التطابقات (بدون تداخل)
            best_matches = []
            used_events = set()
            
            for pattern_name, events_match, quality in pattern_matches:
                # التحقق من عدم التداخل مع الأحداث المستخدمة
                event_ids = {id(event) for event in events_match}
                if not event_ids.intersection(used_events):
                    best_matches.append((pattern_name, events_match))
                    used_events.update(event_ids)
                    
                    if len(best_matches) >= 5:  # حد أقصى 5 أنماط
                        break
            
            return best_matches
            
        except Exception as e:
            logger.error(f"خطأ في تحديد الأنماط السردية: {e}")
            return []

    def _find_pattern_matches(self, events: List[NarrativeEvent], 
                            expected_sequence: List[EventType]) -> List[List[NarrativeEvent]]:
        """العثور على تطابقات للنمط في الأحداث"""
        try:
            matches = []
            
            # البحث عن تسلسلات تطابق النمط
            for i in range(len(events)):
                for j in range(i + len(expected_sequence), len(events) + 1):
                    subsequence = events[i:j]
                    
                    if self._sequence_matches_pattern(subsequence, expected_sequence):
                        matches.append(subsequence)
            
            return matches
            
        except Exception as e:
            logger.error(f"خطأ في العثور على تطابقات النمط: {e}")
            return []

    def _sequence_matches_pattern(self, sequence: List[NarrativeEvent], 
                                expected_pattern: List[EventType]) -> bool:
        """فحص ما إذا كان التسلسل يطابق النمط المتوقع"""
        try:
            if len(sequence) < len(expected_pattern):
                return False
            
            # استخراج أنواع الأحداث من التسلسل
            sequence_types = [event.event_type for event in sequence]
            
            # فحص وجود جميع أنواع الأحداث المطلوبة
            for expected_type in expected_pattern:
                if expected_type not in sequence_types:
                    return False
            
            # فحص الترتيب النسبي (لا يجب أن يكون دقيقاً 100%)
            pattern_positions = []
            for expected_type in expected_pattern:
                try:
                    position = sequence_types.index(expected_type)
                    pattern_positions.append(position)
                except ValueError:
                    return False
            
            # التحقق من أن المواقع مرتبة تصاعدياً (تقريباً)
            for i in range(1, len(pattern_positions)):
                if pattern_positions[i] < pattern_positions[i-1] - 1:  # سماح بانحراف بسيط
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في فحص تطابق التسلسل: {e}")
            return False

    def _evaluate_pattern_match(self, events: List[NarrativeEvent], 
                               expected_pattern: List[EventType]) -> float:
        """تقييم جودة تطابق النمط"""
        try:
            if not events:
                return 0.0
            
            score = 0.0
            
            # نقاط الاكتمال (وجود جميع الأحداث المطلوبة)
            event_types = [event.event_type for event in events]
            completeness = sum(1 for et in expected_pattern if et in event_types) / len(expected_pattern)
            score += completeness * 0.4
            
            # نقاط الترتيب (الترتيب الصحيح للأحداث)
            order_score = self._calculate_order_score(event_types, expected_pattern)
            score += order_score * 0.3
            
            # نقاط الجودة (متوسط أهمية وكثافة الأحداث)
            avg_importance = sum(event.importance_score for event in events) / len(events)
            avg_intensity = sum(event.emotional_intensity for event in events) / len(events)
            quality_score = (avg_importance + avg_intensity) / 2
            score += quality_score * 0.3
            
            return score
            
        except Exception as e:
            logger.error(f"خطأ في تقييم تطابق النمط: {e}")
            return 0.0

    def _calculate_order_score(self, actual_types: List[EventType], 
                             expected_pattern: List[EventType]) -> float:
        """حساب نقاط الترتيب"""
        try:
            if not actual_types or not expected_pattern:
                return 0.0
            
            # العثور على مواقع الأحداث المتوقعة
            expected_positions = []
            actual_positions = []
            
            for expected_type in expected_pattern:
                if expected_type in actual_types:
                    expected_pos = expected_pattern.index(expected_type)
                    actual_pos = actual_types.index(expected_type)
                    expected_positions.append(expected_pos)
                    actual_positions.append(actual_pos)
            
            if len(expected_positions) < 2:
                return 1.0  # إذا كان هناك حدث واحد فقط، فالترتيب صحيح
            
            # حساب معامل الارتباط بين الترتيب المتوقع والفعلي
            correlation = np.corrcoef(expected_positions, actual_positions)[0, 1]
            
            # تحويل الارتباط إلى نقاط (0-1)
            order_score = (correlation + 1) / 2  # تحويل من [-1, 1] إلى [0, 1]
            
            return max(0.0, min(1.0, order_score))
            
        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الترتيب: {e}")
            return 0.5

    def _build_single_coherent_short(self, events: List[NarrativeEvent], pattern_name: str,
                                   narrative_arc: NarrativeArc, max_duration: float) -> Optional[CoherentShort]:
        """بناء مقطع شورتس واحد مترابط"""
        try:
            if not events:
                return None

            # حساب الأوقات
            start_time = events[0].start_time
            end_time = events[-1].end_time
            duration = end_time - start_time

            # تقليم المدة إذا كانت تتجاوز الحد الأقصى
            if duration > max_duration:
                # الاحتفاظ بالأحداث الأكثر أهمية
                events = self._trim_events_to_duration(events, max_duration)
                start_time = events[0].start_time
                end_time = events[-1].end_time

            # تحديد نقاط الانتقال
            transition_points = self._identify_transition_points(events)

            # حساب درجات الجودة
            coherence_score = self._calculate_short_coherence(events, pattern_name)
            viral_potential = self._calculate_short_viral_potential(events, pattern_name)

            # تحديد أفضل وقت للصورة المصغرة
            thumbnail_timestamp = self._find_best_thumbnail_timestamp(events)

            # توليد المحتوى النصي
            content_type = self._determine_content_type(events, narrative_arc.story_theme)
            title = self._generate_title(content_type, narrative_arc.story_theme, events)
            description = self._generate_description(content_type, narrative_arc.story_theme, events)
            hashtags = self._generate_hashtags(content_type, events)

            # تحديد نوع القوس السردي
            story_arc = self._determine_story_arc(events, pattern_name)

            coherent_short = CoherentShort(
                start_time=start_time,
                end_time=end_time,
                events=events,
                story_arc=story_arc,
                coherence_score=coherence_score,
                viral_potential=viral_potential,
                transition_points=transition_points,
                title_suggestion=title,
                description_suggestion=description,
                hashtags=hashtags,
                thumbnail_timestamp=thumbnail_timestamp
            )

            return coherent_short

        except Exception as e:
            logger.error(f"خطأ في بناء مقطع الشورتس المترابط: {e}")
            return None

    def _trim_events_to_duration(self, events: List[NarrativeEvent], max_duration: float) -> List[NarrativeEvent]:
        """تقليم الأحداث لتناسب المدة المحددة"""
        try:
            if not events:
                return events

            # ترتيب الأحداث حسب الأهمية
            sorted_events = sorted(events, key=lambda e: e.importance_score, reverse=True)

            selected_events = []
            current_duration = 0.0

            for event in sorted_events:
                event_duration = event.end_time - event.start_time
                if current_duration + event_duration <= max_duration:
                    selected_events.append(event)
                    current_duration += event_duration
                else:
                    # محاولة تقليم الحدث ليناسب المدة المتبقية
                    remaining_duration = max_duration - current_duration
                    if remaining_duration > 5.0:  # على الأقل 5 ثوان
                        trimmed_event = NarrativeEvent(
                            start_time=event.start_time,
                            end_time=event.start_time + remaining_duration,
                            event_type=event.event_type,
                            confidence=event.confidence,
                            description=event.description,
                            keywords=event.keywords,
                            emotional_intensity=event.emotional_intensity,
                            importance_score=event.importance_score
                        )
                        selected_events.append(trimmed_event)
                    break

            # إعادة ترتيب الأحداث حسب الوقت
            selected_events.sort(key=lambda e: e.start_time)

            return selected_events

        except Exception as e:
            logger.error(f"خطأ في تقليم الأحداث: {e}")
            return events

    def _identify_transition_points(self, events: List[NarrativeEvent]) -> List[float]:
        """تحديد نقاط الانتقال بين الأحداث"""
        try:
            transition_points = []

            for i in range(len(events) - 1):
                current_event = events[i]
                next_event = events[i + 1]

                # إضافة نقطة انتقال بين الأحداث المختلفة
                if current_event.event_type != next_event.event_type:
                    # نقطة الانتقال في منتصف الفجوة بين الأحداث
                    gap_start = current_event.end_time
                    gap_end = next_event.start_time

                    if gap_end > gap_start:
                        transition_point = (gap_start + gap_end) / 2
                    else:
                        transition_point = current_event.end_time

                    transition_points.append(transition_point)

            return transition_points

        except Exception as e:
            logger.error(f"خطأ في تحديد نقاط الانتقال: {e}")
            return []

    def _calculate_short_coherence(self, events: List[NarrativeEvent], pattern_name: str) -> float:
        """حساب درجة تماسك مقطع الشورتس"""
        try:
            if not events:
                return 0.0

            # نقاط التسلسل المنطقي
            pattern_info = self.story_patterns.get(pattern_name, {})
            expected_sequence = pattern_info.get('sequence', [])

            if expected_sequence:
                event_types = [event.event_type for event in events]
                sequence_score = self._calculate_order_score(event_types, expected_sequence)
            else:
                sequence_score = 0.5

            # نقاط التدفق الزمني (عدم وجود فجوات كبيرة)
            time_gaps = []
            for i in range(len(events) - 1):
                gap = events[i + 1].start_time - events[i].end_time
                time_gaps.append(gap)

            if time_gaps:
                avg_gap = sum(time_gaps) / len(time_gaps)
                # كلما قلت الفجوات، زادت النقاط
                flow_score = max(0.0, 1.0 - avg_gap / 30.0)  # 30 ثانية كحد أقصى للفجوة
            else:
                flow_score = 1.0

            # نقاط التنوع في الكثافة العاطفية
            intensities = [event.emotional_intensity for event in events]
            if len(set(intensities)) > 1:
                variety_score = 1.0
            else:
                variety_score = 0.5

            # الدرجة النهائية
            coherence = (sequence_score * 0.5 + flow_score * 0.3 + variety_score * 0.2)

            return coherence

        except Exception as e:
            logger.error(f"خطأ في حساب تماسك الشورتس: {e}")
            return 0.5

    def _calculate_short_viral_potential(self, events: List[NarrativeEvent], pattern_name: str) -> float:
        """حساب الإمكانية الفيروسية لمقطع الشورتس"""
        try:
            if not events:
                return 0.0

            # نقاط الكثافة العاطفية
            max_intensity = max(event.emotional_intensity for event in events)
            avg_intensity = sum(event.emotional_intensity for event in events) / len(events)
            intensity_score = (max_intensity * 0.6 + avg_intensity * 0.4)

            # نقاط الأهمية
            avg_importance = sum(event.importance_score for event in events) / len(events)

            # مكافأة النمط
            pattern_info = self.story_patterns.get(pattern_name, {})
            pattern_multiplier = pattern_info.get('viral_multiplier', 1.0)

            # نقاط التنوع في أنواع الأحداث
            unique_types = len(set(event.event_type for event in events))
            diversity_score = unique_types / len(EventType)

            # الدرجة النهائية
            viral_potential = (intensity_score * 0.4 + avg_importance * 0.3 + diversity_score * 0.3) * pattern_multiplier

            return min(viral_potential, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب الإمكانية الفيروسية: {e}")
            return 0.5

    def _find_best_thumbnail_timestamp(self, events: List[NarrativeEvent]) -> float:
        """العثور على أفضل وقت لأخذ الصورة المصغرة"""
        try:
            if not events:
                return 0.0

            # البحث عن أحداث الذروة أولاً
            climax_events = [e for e in events if e.event_type == EventType.CLIMAX]
            if climax_events:
                # اختيار الذروة الأعلى كثافة
                best_climax = max(climax_events, key=lambda e: e.emotional_intensity)
                return (best_climax.start_time + best_climax.end_time) / 2

            # إذا لم توجد ذروة، البحث عن أعلى كثافة عاطفية
            best_event = max(events, key=lambda e: e.emotional_intensity)
            return (best_event.start_time + best_event.end_time) / 2

        except Exception as e:
            logger.error(f"خطأ في العثور على وقت الصورة المصغرة: {e}")
            return events[0].start_time if events else 0.0

    def _determine_content_type(self, events: List[NarrativeEvent], story_theme: str) -> str:
        """تحديد نوع المحتوى"""
        try:
            # تحليل الكلمات المفتاحية
            all_keywords = []
            for event in events:
                all_keywords.extend(event.keywords)

            keywords_text = ' '.join(all_keywords + [story_theme]).lower()

            # تحديد النوع بناءً على الكلمات المفتاحية
            if any(word in keywords_text for word in ['game', 'play', 'level', 'win', 'lose']):
                return 'gaming'
            elif any(word in keywords_text for word in ['challenge', 'try', 'attempt', 'test']):
                return 'challenge'
            elif any(word in keywords_text for word in ['react', 'watch', 'see', 'look']):
                return 'reaction'
            else:
                return 'general'

        except Exception as e:
            logger.error(f"خطأ في تحديد نوع المحتوى: {e}")
            return 'general'

    def _generate_title(self, content_type: str, story_theme: str, events: List[NarrativeEvent]) -> str:
        """توليد عنوان للمقطع"""
        try:
            templates = self.content_templates.get(content_type, self.content_templates['challenge'])
            title_templates = templates['titles']

            # اختيار قالب عشوائي
            import random
            template = random.choice(title_templates)

            # استبدال المتغيرات
            celebrity = "المشهور"  # يمكن تحسينها لاحقاً لاستخراج اسم المشهور
            theme = story_theme or "المحتوى"

            title = template.format(celebrity=celebrity, theme=theme)

            return title

        except Exception as e:
            logger.error(f"خطأ في توليد العنوان: {e}")
            return f"مقطع مثير من {story_theme}"

    def _generate_description(self, content_type: str, story_theme: str, events: List[NarrativeEvent]) -> str:
        """توليد وصف للمقطع"""
        try:
            templates = self.content_templates.get(content_type, self.content_templates['challenge'])
            desc_templates = templates['descriptions']

            # اختيار قالب عشوائي
            import random
            template = random.choice(desc_templates)

            # استبدال المتغيرات
            celebrity = "المشهور"
            theme = story_theme or "المحتوى"

            description = template.format(celebrity=celebrity, theme=theme)

            # إضافة معلومات عن الأحداث
            event_descriptions = []
            for event in events[:3]:  # أول 3 أحداث
                if event.description:
                    event_descriptions.append(event.description[:50] + "...")

            if event_descriptions:
                description += "\n\nأبرز اللحظات:\n" + "\n".join(f"• {desc}" for desc in event_descriptions)

            return description

        except Exception as e:
            logger.error(f"خطأ في توليد الوصف: {e}")
            return f"مقطع رائع من {story_theme} مليء باللحظات المثيرة!"

    def _generate_hashtags(self, content_type: str, events: List[NarrativeEvent]) -> List[str]:
        """توليد هاشتاغات للمقطع"""
        try:
            templates = self.content_templates.get(content_type, self.content_templates['challenge'])
            base_hashtags = templates['hashtags'].copy()

            # إضافة هاشتاغات بناءً على أنواع الأحداث
            event_hashtags = {
                EventType.CLIMAX: ['#ذروة', '#مجنون', '#لا_يصدق'],
                EventType.REACTION: ['#ردة_فعل', '#صدمة', '#مفاجأة'],
                EventType.INTRODUCTION: ['#بداية', '#مقدمة'],
                EventType.CONCLUSION: ['#نهاية', '#خلاصة']
            }

            for event in events:
                if event.event_type in event_hashtags:
                    base_hashtags.extend(event_hashtags[event.event_type])

            # إزالة المكررات والاحتفاظ بأفضل 10 هاشتاغات
            unique_hashtags = list(set(base_hashtags))
            return unique_hashtags[:10]

        except Exception as e:
            logger.error(f"خطأ في توليد الهاشتاغات: {e}")
            return ['#شورتس', '#فيروسي', '#مثير', '#ترفيه']

    def _determine_story_arc(self, events: List[NarrativeEvent], pattern_name: str) -> str:
        """تحديد نوع القوس السردي"""
        try:
            pattern_info = self.story_patterns.get(pattern_name, {})
            return pattern_info.get('description', 'قصة عامة')

        except Exception as e:
            logger.error(f"خطأ في تحديد القوس السردي: {e}")
            return 'قصة عامة'

    def create_transition_segments(self, coherent_short: CoherentShort) -> List[TransitionSegment]:
        """إنشاء قطع انتقالية لربط الأحداث"""
        try:
            transition_segments = []

            for i, transition_point in enumerate(coherent_short.transition_points):
                # تحديد نوع الانتقال بناءً على الأحداث المجاورة
                if i < len(coherent_short.events) - 1:
                    current_event = coherent_short.events[i]
                    next_event = coherent_short.events[i + 1]

                    # تحديد نوع الانتقال
                    if current_event.event_type == EventType.SETUP and next_event.event_type == EventType.CLIMAX:
                        transition_type = "buildup_fade"
                        purpose = "building_tension"
                    elif current_event.event_type == EventType.CLIMAX and next_event.event_type == EventType.REACTION:
                        transition_type = "quick_cut"
                        purpose = "immediate_reaction"
                    else:
                        transition_type = "smooth_fade"
                        purpose = "context_change"

                    # إنشاء قطعة انتقالية قصيرة (1-2 ثانية)
                    segment = TransitionSegment(
                        start_time=transition_point - 0.5,
                        end_time=transition_point + 0.5,
                        transition_type=transition_type,
                        purpose=purpose
                    )

                    transition_segments.append(segment)

            return transition_segments

        except Exception as e:
            logger.error(f"خطأ في إنشاء قطع الانتقال: {e}")
            return []

    def _create_merged_highlight_shorts(self, events: List[NarrativeEvent],
                                      target_count: int, max_duration: float,
                                      existing_shorts: List[CoherentShort]) -> List[CoherentShort]:
        """إنشاء مقاطع شورتس من دمج اللقطات المثيرة المنفصلة"""
        try:
            logger.info("البحث عن لقطات مثيرة منفصلة للدمج")

            # استخراج الأحداث المستخدمة بالفعل
            used_events = set()
            for short in existing_shorts:
                for event in short.events:
                    used_events.add(id(event))

            # العثور على الأحداث المثيرة غير المستخدمة
            available_events = [e for e in events if id(e) not in used_events]
            exciting_events = [e for e in available_events
                             if e.emotional_intensity > 0.7 or e.importance_score > 0.7]

            if not exciting_events:
                logger.warning("لا توجد أحداث مثيرة متاحة للدمج")
                return []

            # ترتيب الأحداث حسب الجودة
            exciting_events.sort(key=lambda e: e.emotional_intensity * e.importance_score, reverse=True)

            merged_shorts = []

            for i in range(target_count):
                # جمع أحداث للمقطع الواحد
                clip_events = []
                current_duration = 0.0

                # البدء بأفضل حدث متاح
                if not exciting_events:
                    break

                primary_event = exciting_events.pop(0)
                clip_events.append(primary_event)
                current_duration = primary_event.end_time - primary_event.start_time

                # إضافة أحداث إضافية إذا كان هناك مساحة
                for event in exciting_events[:]:
                    event_duration = event.end_time - event.start_time

                    # التحقق من إمكانية الإضافة
                    if current_duration + event_duration <= max_duration:
                        # التحقق من عدم التداخل الزمني الكبير
                        time_gap = abs(event.start_time - primary_event.end_time)
                        if time_gap < 300:  # أقل من 5 دقائق
                            clip_events.append(event)
                            current_duration += event_duration
                            exciting_events.remove(event)

                            if current_duration >= max_duration * 0.8:  # 80% من الحد الأقصى
                                break

                # إنشاء مقطع مدمج
                if clip_events:
                    merged_short = self._build_merged_short(clip_events, max_duration, i)
                    if merged_short:
                        merged_shorts.append(merged_short)

            logger.info(f"تم إنشاء {len(merged_shorts)} مقطع مدمج من اللقطات المنفصلة")
            return merged_shorts

        except Exception as e:
            logger.error(f"خطأ في إنشاء المقاطع المدمجة: {e}")
            return []

    def _build_merged_short(self, events: List[NarrativeEvent],
                          max_duration: float, index: int) -> Optional[CoherentShort]:
        """بناء مقطع شورتس من أحداث مدمجة"""
        try:
            if not events:
                return None

            # ترتيب الأحداث زمنياً
            events.sort(key=lambda e: e.start_time)

            # حساب الأوقات
            start_time = events[0].start_time
            end_time = events[-1].end_time

            # تقليم إذا تجاوز الحد الأقصى
            if end_time - start_time > max_duration:
                # الاحتفاظ بأهم الأحداث
                events = self._trim_events_to_duration(events, max_duration)
                start_time = events[0].start_time
                end_time = events[-1].end_time

            # تحديد نقاط الانتقال
            transition_points = []
            for i in range(len(events) - 1):
                gap_start = events[i].end_time
                gap_end = events[i + 1].start_time
                if gap_end > gap_start:
                    transition_points.append((gap_start + gap_end) / 2)

            # حساب درجات الجودة
            avg_intensity = sum(e.emotional_intensity for e in events) / len(events)
            avg_importance = sum(e.importance_score for e in events) / len(events)

            # درجة التماسك أقل للمقاطع المدمجة
            coherence_score = (avg_importance + avg_intensity) / 2 * 0.7  # تقليل بسبب عدم الترابط الطبيعي

            # الإمكانية الفيروسية عالية بسبب تركيز اللقطات المثيرة
            viral_potential = avg_intensity * 1.2  # زيادة بسبب تركيز الإثارة
            viral_potential = min(viral_potential, 1.0)

            # تحديد أفضل وقت للصورة المصغرة
            best_event = max(events, key=lambda e: e.emotional_intensity)
            thumbnail_timestamp = (best_event.start_time + best_event.end_time) / 2

            # توليد المحتوى النصي
            title = self._generate_merged_title(events, index)
            description = self._generate_merged_description(events)
            hashtags = self._generate_merged_hashtags(events)

            # تحديد نوع القوس السردي
            story_arc = f"مجموعة لقطات مثيرة #{index + 1}"

            merged_short = CoherentShort(
                start_time=start_time,
                end_time=end_time,
                events=events,
                story_arc=story_arc,
                coherence_score=coherence_score,
                viral_potential=viral_potential,
                transition_points=transition_points,
                title_suggestion=title,
                description_suggestion=description,
                hashtags=hashtags,
                thumbnail_timestamp=thumbnail_timestamp
            )

            return merged_short

        except Exception as e:
            logger.error(f"خطأ في بناء المقطع المدمج: {e}")
            return None

    def _split_long_events_into_shorts(self, events: List[NarrativeEvent],
                                     target_count: int, max_duration: float,
                                     existing_shorts: List[CoherentShort]) -> List[CoherentShort]:
        """تقسيم الأحداث الطويلة إلى مقاطع شورتس منفصلة"""
        try:
            logger.info("تقسيم الأحداث الطويلة إلى مقاطع منفصلة")

            # استخراج الأحداث المستخدمة بالفعل
            used_events = set()
            for short in existing_shorts:
                for event in short.events:
                    used_events.add(id(event))

            # العثور على الأحداث الطويلة غير المستخدمة
            available_events = [e for e in events if id(e) not in used_events]
            long_events = [e for e in available_events
                          if (e.end_time - e.start_time) > max_duration * 0.5]  # أطول من نصف الحد الأقصى

            if not long_events:
                logger.warning("لا توجد أحداث طويلة متاحة للتقسيم")
                return []

            # ترتيب الأحداث حسب الجودة
            long_events.sort(key=lambda e: e.emotional_intensity * e.importance_score, reverse=True)

            split_shorts = []

            for event in long_events[:target_count]:
                # تقسيم الحدث إلى أجزاء
                event_duration = event.end_time - event.start_time

                if event_duration <= max_duration:
                    # الحدث يناسب مقطع واحد
                    split_short = self._build_single_event_short(event, 0)
                    if split_short:
                        split_shorts.append(split_short)
                else:
                    # تقسيم الحدث إلى أجزاء متعددة
                    num_parts = int(event_duration / max_duration) + 1
                    part_duration = event_duration / num_parts

                    for i in range(min(num_parts, target_count - len(split_shorts))):
                        part_start = event.start_time + (i * part_duration)
                        part_end = min(part_start + max_duration, event.end_time)

                        # إنشاء حدث جزئي
                        part_event = NarrativeEvent(
                            start_time=part_start,
                            end_time=part_end,
                            event_type=event.event_type,
                            confidence=event.confidence * 0.8,  # تقليل الثقة للأجزاء
                            description=f"{event.description} - الجزء {i + 1}",
                            keywords=event.keywords,
                            emotional_intensity=event.emotional_intensity,
                            importance_score=event.importance_score * 0.9,  # تقليل الأهمية قليلاً
                            related_objects=event.related_objects,
                            speaker_emotions=event.speaker_emotions
                        )

                        split_short = self._build_single_event_short(part_event, i)
                        if split_short:
                            split_shorts.append(split_short)

                        if len(split_shorts) >= target_count:
                            break

                if len(split_shorts) >= target_count:
                    break

            logger.info(f"تم إنشاء {len(split_shorts)} مقطع من تقسيم الأحداث الطويلة")
            return split_shorts

        except Exception as e:
            logger.error(f"خطأ في تقسيم الأحداث الطويلة: {e}")
            return []

    def _build_single_event_short(self, event: NarrativeEvent, index: int) -> Optional[CoherentShort]:
        """بناء مقطع شورتس من حدث واحد"""
        try:
            # حساب درجات الجودة
            coherence_score = event.importance_score * 0.6  # أقل تماسك للأحداث المنفردة
            viral_potential = event.emotional_intensity

            # تحديد أفضل وقت للصورة المصغرة (منتصف الحدث)
            thumbnail_timestamp = (event.start_time + event.end_time) / 2

            # توليد المحتوى النصي
            title = self._generate_single_event_title(event, index)
            description = self._generate_single_event_description(event)
            hashtags = self._generate_single_event_hashtags(event)

            # تحديد نوع القوس السردي
            story_arc = f"لحظة {event.event_type.value} #{index + 1}"

            single_short = CoherentShort(
                start_time=event.start_time,
                end_time=event.end_time,
                events=[event],
                story_arc=story_arc,
                coherence_score=coherence_score,
                viral_potential=viral_potential,
                transition_points=[],  # لا توجد انتقالات للحدث الواحد
                title_suggestion=title,
                description_suggestion=description,
                hashtags=hashtags,
                thumbnail_timestamp=thumbnail_timestamp
            )

            return single_short

        except Exception as e:
            logger.error(f"خطأ في بناء مقطع الحدث الواحد: {e}")
            return None

    def _generate_merged_title(self, events: List[NarrativeEvent], index: int) -> str:
        """توليد عنوان للمقطع المدمج"""
        try:
            # تحديد النوع الأكثر شيوعاً
            event_types = [e.event_type for e in events]
            most_common_type = max(set(event_types), key=event_types.count)

            # قوالب العناوين للمقاطع المدمجة
            merged_titles = {
                EventType.CLIMAX: [
                    f"أفضل اللحظات المجنونة #{index + 1} 🔥",
                    f"مجموعة لقطات لا تصدق #{index + 1} ⚡",
                    f"أقوى اللحظات #{index + 1} 🚀"
                ],
                EventType.REACTION: [
                    f"ردود أفعال مجنونة #{index + 1} 😱",
                    f"أفضل ردود الأفعال #{index + 1} 🤯",
                    f"لحظات صدمة #{index + 1} 😲"
                ],
                EventType.INTRODUCTION: [
                    f"أفضل اللحظات #{index + 1} ✨",
                    f"مقاطع مميزة #{index + 1} 🎬",
                    f"لحظات رائعة #{index + 1} 🌟"
                ]
            }

            titles = merged_titles.get(most_common_type, merged_titles[EventType.CLIMAX])

            import random
            return random.choice(titles)

        except Exception as e:
            logger.error(f"خطأ في توليد عنوان المقطع المدمج: {e}")
            return f"مجموعة لقطات مثيرة #{index + 1}"

    def _generate_merged_description(self, events: List[NarrativeEvent]) -> str:
        """توليد وصف للمقطع المدمج"""
        try:
            description = f"مجموعة من أفضل اللحظات المثيرة ({len(events)} لحظة):\n\n"

            for i, event in enumerate(events[:3], 1):  # أول 3 أحداث
                event_desc = event.description[:40] + "..." if len(event.description) > 40 else event.description
                description += f"• اللحظة {i}: {event_desc}\n"

            if len(events) > 3:
                description += f"• وأكثر من {len(events) - 3} لحظة مثيرة أخرى!\n"

            description += "\n🔥 مقطع مليء بالإثارة والمفاجآت!"

            return description

        except Exception as e:
            logger.error(f"خطأ في توليد وصف المقطع المدمج: {e}")
            return "مجموعة من أفضل اللحظات المثيرة!"

    def _generate_merged_hashtags(self, events: List[NarrativeEvent]) -> List[str]:
        """توليد هاشتاغات للمقطع المدمج"""
        try:
            base_hashtags = ["#shorts", "#viral", "#compilation", "#highlights", "#best_moments"]

            # إضافة هاشتاغات بناءً على أنواع الأحداث
            event_types = [e.event_type for e in events]

            if EventType.CLIMAX in event_types:
                base_hashtags.extend(["#مجنون", "#لا_يصدق", "#ذروة"])

            if EventType.REACTION in event_types:
                base_hashtags.extend(["#ردة_فعل", "#صدمة", "#مفاجأة"])

            # إضافة هاشتاغات عامة
            base_hashtags.extend(["#مثير", "#ترفيه", "#فيروسي", "#مجموعة"])

            return list(set(base_hashtags))[:12]  # إزالة المكررات والاحتفاظ بـ 12

        except Exception as e:
            logger.error(f"خطأ في توليد هاشتاغات المقطع المدمج: {e}")
            return ["#shorts", "#viral", "#highlights"]

    def _generate_single_event_title(self, event: NarrativeEvent, index: int) -> str:
        """توليد عنوان للحدث الواحد"""
        try:
            # قوالب العناوين للأحداث المنفردة
            single_titles = {
                EventType.CLIMAX: [
                    f"لحظة مجنونة #{index + 1} 🔥",
                    f"هذا لا يصدق #{index + 1} ⚡",
                    f"لحظة أسطورية #{index + 1} 🚀"
                ],
                EventType.REACTION: [
                    f"ردة فعل صادمة #{index + 1} 😱",
                    f"لحظة صدمة #{index + 1} 🤯",
                    f"ردة فعل مجنونة #{index + 1} 😲"
                ],
                EventType.BUILDUP: [
                    f"لحظة مثيرة #{index + 1} 📈",
                    f"تصاعد مجنون #{index + 1} ⬆️",
                    f"بناء التشويق #{index + 1} 🎯"
                ]
            }

            titles = single_titles.get(event.event_type, single_titles[EventType.CLIMAX])

            import random
            return random.choice(titles)

        except Exception as e:
            logger.error(f"خطأ في توليد عنوان الحدث الواحد: {e}")
            return f"لحظة مميزة #{index + 1}"

    def _generate_single_event_description(self, event: NarrativeEvent) -> str:
        """توليد وصف للحدث الواحد"""
        try:
            description = f"لحظة {event.event_type.value} مثيرة:\n\n"
            description += f"📝 {event.description}\n\n"

            if event.keywords:
                description += f"🏷️ الكلمات المفتاحية: {', '.join(event.keywords[:5])}\n"

            intensity_emoji = "🔥" if event.emotional_intensity > 0.8 else "⚡" if event.emotional_intensity > 0.6 else "✨"
            description += f"{intensity_emoji} كثافة عاطفية: {event.emotional_intensity*100:.0f}%"

            return description

        except Exception as e:
            logger.error(f"خطأ في توليد وصف الحدث الواحد: {e}")
            return f"لحظة {event.event_type.value} مثيرة!"

    def _generate_single_event_hashtags(self, event: NarrativeEvent) -> List[str]:
        """توليد هاشتاغات للحدث الواحد"""
        try:
            base_hashtags = ["#shorts", "#viral", "#moment"]

            # إضافة هاشتاغات بناءً على نوع الحدث
            event_hashtags = {
                EventType.CLIMAX: ["#ذروة", "#مجنون", "#لا_يصدق", "#climax"],
                EventType.REACTION: ["#ردة_فعل", "#صدمة", "#مفاجأة", "#reaction"],
                EventType.BUILDUP: ["#تصاعد", "#تشويق", "#buildup"],
                EventType.INTRODUCTION: ["#مقدمة", "#بداية", "#intro"],
                EventType.CONCLUSION: ["#خاتمة", "#نهاية", "#conclusion"]
            }

            base_hashtags.extend(event_hashtags.get(event.event_type, []))

            # إضافة هاشتاغات من الكلمات المفتاحية
            for keyword in event.keywords[:3]:
                if len(keyword) > 2:
                    base_hashtags.append(f"#{keyword}")

            return list(set(base_hashtags))[:10]  # إزالة المكررات والاحتفاظ بـ 10

        except Exception as e:
            logger.error(f"خطأ في توليد هاشتاغات الحدث الواحد: {e}")
            return ["#shorts", "#viral", "#moment"]
