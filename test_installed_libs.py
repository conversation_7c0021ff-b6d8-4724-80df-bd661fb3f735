#!/usr/bin/env python3
"""
اختبار المكتبات المثبتة
Test installed libraries
"""

import sys
from pathlib import Path

# إضافة مجلد src للمسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_libraries():
    """اختبار المكتبات المثبتة"""
    print('🔍 اختبار المكتبات المثبتة:')
    print('=' * 40)

    # اختبار المكتبات الأساسية
    libs = [
        ('yt_dlp', 'تحميل YouTube'),
        ('speech_recognition', 'تحويل الصوت إلى نص'),
        ('sklearn', 'التعلم الآلي'),
        ('pandas', 'معالجة البيانات'),
        ('matplotlib', 'الرسوم البيانية'),
        ('seaborn', 'رسوم متقدمة'),
        ('cv2', 'OpenCV'),
        ('tensorflow', 'TensorFlow'),
        ('torch', 'PyTorch'),
        ('deepface', 'تحليل المشاعر')
    ]

    installed = 0
    total = len(libs)

    for lib, desc in libs:
        try:
            __import__(lib)
            print(f'✅ {lib} - {desc}')
            installed += 1
        except ImportError:
            print(f'❌ {lib} - {desc}')

    print(f'\n📊 النتيجة: {installed}/{total} مكتبة مثبتة')

    # اختبار المحلل المحسن
    print('\n🧠 اختبار المحلل المحسن:')
    try:
        from ai.enhanced_content_analyzer import EnhancedContentAnalyzer
        analyzer = EnhancedContentAnalyzer()
        features = analyzer.get_available_features()
        
        for feature, available in features.items():
            status = '✅' if available else '❌'
            print(f'{status} {feature}')
        
        advanced_available = analyzer.is_advanced_analysis_available()
        print(f'\n📊 التحليل المتقدم: {"✅ متاح" if advanced_available else "❌ غير متاح"}')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')

    return installed, total

if __name__ == "__main__":
    try:
        installed, total = test_libraries()
        print(f'\n🎉 تم تثبيت {installed} من أصل {total} مكتبة بنجاح!')
    except Exception as e:
        print(f'💥 خطأ: {e}')
