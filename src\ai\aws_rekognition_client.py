"""
عميل Amazon Rekognition Video
Amazon Rekognition Video Client
"""

import logging
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
import json

try:
    import boto3
    from botocore.exceptions import ClientError, NoCredentialsError
    AWS_AVAILABLE = True
except ImportError:
    AWS_AVAILABLE = False
    boto3 = None

from ..config.api_keys import api_keys_manager

logger = logging.getLogger(__name__)

class AWSRekognitionClient:
    """عميل للتفاعل مع Amazon Rekognition Video"""
    
    def __init__(self):
        self.config = api_keys_manager.get_service_config('aws_rekognition')
        self.client = None
        self.s3_client = None
        
        if not AWS_AVAILABLE:
            logger.error("مكتبة boto3 غير متوفرة. قم بتثبيتها: pip install boto3")
            return
        
        if not self.config or not self.config.get('enabled'):
            logger.warning("AWS Rekognition غير مُفعل في الإعدادات")
            return
        
        self._initialize_client()
    
    def _initialize_client(self):
        """تهيئة عميل AWS Rekognition"""
        try:
            if not self.config.get('access_key') or not self.config.get('secret_key'):
                logger.error("مفاتيح AWS غير متوفرة")
                return
            
            # إنشاء جلسة AWS
            session = boto3.Session(
                aws_access_key_id=self.config['access_key'],
                aws_secret_access_key=self.config['secret_key'],
                region_name=self.config.get('region', 'us-east-1')
            )
            
            self.client = session.client('rekognition')
            self.s3_client = session.client('s3')
            
            # اختبار الاتصال
            self._test_connection()
            
            logger.info("تم تهيئة عميل AWS Rekognition بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة عميل AWS Rekognition: {e}")
            self.client = None
    
    def _test_connection(self):
        """اختبار الاتصال مع AWS"""
        try:
            # اختبار بسيط للتحقق من الاتصال
            self.client.list_collections(MaxResults=1)
            logger.info("✅ اتصال AWS Rekognition ناجح")
        except Exception as e:
            logger.warning(f"⚠️ تحذير في اختبار اتصال AWS: {e}")
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return (AWS_AVAILABLE and 
                self.client is not None and 
                self.config and 
                self.config.get('enabled', False))
    
    def detect_faces_in_image(self, image_path: str) -> Optional[List[Dict]]:
        """كشف الوجوه في صورة"""
        if not self.is_available():
            logger.error("خدمة AWS Rekognition غير متوفرة")
            return None
        
        try:
            # قراءة الصورة
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
            
            # كشف الوجوه
            response = self.client.detect_faces(
                Image={'Bytes': image_bytes},
                Attributes=['ALL']
            )
            
            faces = []
            for face_detail in response.get('FaceDetails', []):
                face_info = {
                    'confidence': face_detail.get('Confidence', 0),
                    'bounding_box': face_detail.get('BoundingBox', {}),
                    'emotions': [],
                    'age_range': face_detail.get('AgeRange', {}),
                    'gender': face_detail.get('Gender', {}),
                    'smile': face_detail.get('Smile', {}),
                    'eyeglasses': face_detail.get('Eyeglasses', {}),
                    'sunglasses': face_detail.get('Sunglasses', {}),
                    'beard': face_detail.get('Beard', {}),
                    'mustache': face_detail.get('Mustache', {}),
                    'eyes_open': face_detail.get('EyesOpen', {}),
                    'mouth_open': face_detail.get('MouthOpen', {})
                }
                
                # استخراج المشاعر
                for emotion in face_detail.get('Emotions', []):
                    face_info['emotions'].append({
                        'type': emotion.get('Type'),
                        'confidence': emotion.get('Confidence', 0)
                    })
                
                faces.append(face_info)
            
            logger.info(f"تم كشف {len(faces)} وجه في الصورة")
            return faces
            
        except Exception as e:
            logger.error(f"خطأ في كشف الوجوه: {e}")
            return None
    
    def detect_labels_in_image(self, image_path: str, max_labels: int = 10) -> Optional[List[Dict]]:
        """كشف الكائنات والمشاهد في صورة"""
        if not self.is_available():
            logger.error("خدمة AWS Rekognition غير متوفرة")
            return None
        
        try:
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
            
            response = self.client.detect_labels(
                Image={'Bytes': image_bytes},
                MaxLabels=max_labels,
                MinConfidence=70
            )
            
            labels = []
            for label in response.get('Labels', []):
                label_info = {
                    'name': label.get('Name'),
                    'confidence': label.get('Confidence', 0),
                    'instances': [],
                    'parents': [parent.get('Name') for parent in label.get('Parents', [])]
                }
                
                # معلومات المواقع
                for instance in label.get('Instances', []):
                    label_info['instances'].append({
                        'confidence': instance.get('Confidence', 0),
                        'bounding_box': instance.get('BoundingBox', {})
                    })
                
                labels.append(label_info)
            
            logger.info(f"تم كشف {len(labels)} كائن/مشهد في الصورة")
            return labels
            
        except Exception as e:
            logger.error(f"خطأ في كشف الكائنات: {e}")
            return None
    
    def detect_text_in_image(self, image_path: str) -> Optional[List[Dict]]:
        """كشف النصوص في صورة"""
        if not self.is_available():
            logger.error("خدمة AWS Rekognition غير متوفرة")
            return None
        
        try:
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
            
            response = self.client.detect_text(
                Image={'Bytes': image_bytes}
            )
            
            texts = []
            for text_detection in response.get('TextDetections', []):
                if text_detection.get('Type') == 'LINE':  # نريد الأسطر فقط
                    text_info = {
                        'text': text_detection.get('DetectedText'),
                        'confidence': text_detection.get('Confidence', 0),
                        'bounding_box': text_detection.get('Geometry', {}).get('BoundingBox', {}),
                        'id': text_detection.get('Id')
                    }
                    texts.append(text_info)
            
            logger.info(f"تم كشف {len(texts)} نص في الصورة")
            return texts
            
        except Exception as e:
            logger.error(f"خطأ في كشف النصوص: {e}")
            return None
    
    def detect_moderation_labels(self, image_path: str) -> Optional[List[Dict]]:
        """كشف المحتوى غير اللائق"""
        if not self.is_available():
            logger.error("خدمة AWS Rekognition غير متوفرة")
            return None
        
        try:
            with open(image_path, 'rb') as image_file:
                image_bytes = image_file.read()
            
            response = self.client.detect_moderation_labels(
                Image={'Bytes': image_bytes},
                MinConfidence=60
            )
            
            moderation_labels = []
            for label in response.get('ModerationLabels', []):
                label_info = {
                    'name': label.get('Name'),
                    'confidence': label.get('Confidence', 0),
                    'parent_name': label.get('ParentName', '')
                }
                moderation_labels.append(label_info)
            
            logger.info(f"تم فحص المحتوى: {len(moderation_labels)} تحذير")
            return moderation_labels
            
        except Exception as e:
            logger.error(f"خطأ في فحص المحتوى: {e}")
            return None
    
    def get_service_status(self) -> Dict[str, Any]:
        """الحصول على حالة الخدمة"""
        return {
            'service_name': 'AWS Rekognition',
            'available': self.is_available(),
            'aws_sdk_available': AWS_AVAILABLE,
            'client_initialized': self.client is not None,
            'config_enabled': self.config.get('enabled', False) if self.config else False,
            'region': self.config.get('region') if self.config else None,
            'free_tier': self.config.get('free_tier', False) if self.config else False,
            'monthly_limit': self.config.get('monthly_limit') if self.config else None
        }
