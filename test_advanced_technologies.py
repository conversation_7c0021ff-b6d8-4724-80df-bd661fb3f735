#!/usr/bin/env python3
"""
اختبار التقنيات المتقدمة الجديدة
Test Advanced Technologies

اختبار شامل للتقنيات الجديدة: YOLO، TensorFlow، Detectron2، PyTorchVideo، والنظام الهجين
"""

import sys
import os
import logging
from pathlib import Path
import time
import cv2
import numpy as np

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_yolo_detector():
    """اختبار كاشف YOLO"""
    logger.info("🔍 اختبار كاشف YOLO...")
    
    try:
        from ai.yolo_detector import YOLODetector
        
        detector = YOLODetector()
        
        if detector.is_available():
            logger.info("✅ YOLO متوفر ويعمل")
            
            # إنشاء إطار تجريبي
            test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # اختبار الكشف
            analysis = detector.detect_frame(test_frame, 0, 0.0)
            
            logger.info(f"   - تم تحليل الإطار: {analysis.person_count} أشخاص، {analysis.object_count} أشياء")
            logger.info(f"   - نقاط النشاط: {analysis.activity_score}")
            
            return True
        else:
            logger.warning("⚠️ YOLO غير متوفر")
            return False
            
    except ImportError:
        logger.error("❌ فشل في استيراد YOLODetector")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار YOLO: {e}")
        return False

def test_tensorflow_detector():
    """اختبار كاشف TensorFlow"""
    logger.info("🔍 اختبار كاشف TensorFlow...")
    
    try:
        from ai.tensorflow_detector import TensorFlowDetector
        
        detector = TensorFlowDetector()
        
        if detector.is_available():
            logger.info("✅ TensorFlow متوفر ويعمل")
            
            # إنشاء إطار تجريبي
            test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # اختبار تحليل المشهد
            analysis = detector.analyze_scene(test_frame, 0.0)
            
            logger.info(f"   - نوع المشهد: {analysis.scene_type}")
            logger.info(f"   - جودة المشهد: {analysis.quality_score}")
            logger.info(f"   - تعقيد المشهد: {analysis.scene_complexity}")
            
            return True
        else:
            logger.warning("⚠️ TensorFlow غير متوفر")
            return False
            
    except ImportError:
        logger.error("❌ فشل في استيراد TensorFlowDetector")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار TensorFlow: {e}")
        return False

def test_detectron2_analyzer():
    """اختبار محلل Detectron2"""
    logger.info("🔍 اختبار محلل Detectron2...")
    
    try:
        from ai.detectron2_analyzer import Detectron2Analyzer
        
        analyzer = Detectron2Analyzer()
        
        if analyzer.is_available():
            logger.info("✅ Detectron2 متوفر ويعمل")
            
            # إنشاء إطار تجريبي
            test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # اختبار التحليل المتقدم
            analysis = analyzer.analyze_frame(test_frame, 0.0)
            
            logger.info(f"   - عدد المثيلات: {len(analysis.instances)}")
            logger.info(f"   - عدد الوضعيات: {len(analysis.poses)}")
            logger.info(f"   - فهم المشهد: {bool(analysis.scene_understanding)}")
            
            return True
        else:
            logger.warning("⚠️ Detectron2 غير متوفر")
            return False
            
    except ImportError:
        logger.error("❌ فشل في استيراد Detectron2Analyzer")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار Detectron2: {e}")
        return False

def test_pytorchvideo_analyzer():
    """اختبار محلل PyTorchVideo"""
    logger.info("🔍 اختبار محلل PyTorchVideo...")
    
    try:
        from ai.pytorchvideo_analyzer import PyTorchVideoAnalyzer
        
        analyzer = PyTorchVideoAnalyzer()
        
        if analyzer.is_available():
            logger.info("✅ PyTorchVideo متوفر ويعمل")
            
            # إنشاء فيديو تجريبي صغير
            test_video_path = "temp_test_video.mp4"
            create_test_video(test_video_path)
            
            try:
                # اختبار تحليل مقطع فيديو
                analysis = analyzer.analyze_video_clip(test_video_path, 0.0, 2.0)
                
                logger.info(f"   - عدد الأفعال: {len(analysis.actions)}")
                logger.info(f"   - عدد الأحداث: {len(analysis.events)}")
                logger.info(f"   - التدفق السردي: {analysis.narrative_flow}")
                
                return True
                
            finally:
                # حذف الفيديو التجريبي
                if os.path.exists(test_video_path):
                    os.remove(test_video_path)
        else:
            logger.warning("⚠️ PyTorchVideo غير متوفر")
            return False
            
    except ImportError:
        logger.error("❌ فشل في استيراد PyTorchVideoAnalyzer")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار PyTorchVideo: {e}")
        return False

def test_hybrid_analyzer():
    """اختبار النظام الهجين المتقدم"""
    logger.info("🔍 اختبار النظام الهجين المتقدم...")
    
    try:
        from ai.hybrid_advanced_analyzer import HybridAdvancedAnalyzer
        
        analyzer = HybridAdvancedAnalyzer()
        available_analyzers = analyzer.get_available_analyzers()
        
        logger.info(f"✅ النظام الهجين متوفر مع {len(available_analyzers)} محلل")
        logger.info(f"   - المحللات المتاحة: {', '.join(available_analyzers)}")
        
        if available_analyzers:
            # إنشاء فيديو تجريبي
            test_video_path = "temp_test_video_hybrid.mp4"
            create_test_video(test_video_path)
            
            try:
                # اختبار تصنيف نوع المحتوى
                content_classification = analyzer.classify_content_type(test_video_path)
                logger.info(f"   - نوع المحتوى: {content_classification.content_type}")
                logger.info(f"   - الثقة: {content_classification.confidence:.2f}")
                
                # اختبار تحليل إطار
                test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
                frame_analysis = analyzer.analyze_frame_hybrid(test_frame, 0.0)
                
                logger.info(f"   - طريقة التحليل: {frame_analysis.analysis_method}")
                logger.info(f"   - نقاط الثقة: {frame_analysis.confidence_score:.2f}")
                logger.info(f"   - التوصية: {frame_analysis.recommendation}")
                
                return True
                
            finally:
                # حذف الفيديو التجريبي
                if os.path.exists(test_video_path):
                    os.remove(test_video_path)
        else:
            logger.warning("⚠️ لا توجد محللات متاحة في النظام الهجين")
            return False
            
    except ImportError:
        logger.error("❌ فشل في استيراد HybridAdvancedAnalyzer")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار النظام الهجين: {e}")
        return False

def test_enhanced_content_analyzer():
    """اختبار محلل المحتوى المحسن مع النظام الهجين"""
    logger.info("🔍 اختبار محلل المحتوى المحسن...")
    
    try:
        from ai.enhanced_content_analyzer import EnhancedContentAnalyzer
        
        analyzer = EnhancedContentAnalyzer()
        features = analyzer.get_available_features()
        
        logger.info(f"✅ محلل المحتوى المحسن متوفر")
        logger.info(f"   - الميزات المتاحة: {sum(features.values())}/{len(features)}")
        
        for feature, available in features.items():
            status = "✅" if available else "❌"
            logger.info(f"   - {feature}: {status}")
        
        # اختبار حالة النظام الهجين
        if features.get('hybrid_analyzer', False):
            hybrid_status = analyzer.get_hybrid_analyzer_status()
            if hybrid_status['available']:
                logger.info(f"   - النظام الهجين: ✅ ({hybrid_status['analyzers_count']} محلل)")
                logger.info(f"   - أنواع المحتوى المدعومة: {len(hybrid_status['supported_content_types'])}")
            else:
                logger.warning(f"   - النظام الهجين: ❌ {hybrid_status.get('error', 'غير متوفر')}")
        
        return True
        
    except ImportError:
        logger.error("❌ فشل في استيراد EnhancedContentAnalyzer")
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار محلل المحتوى المحسن: {e}")
        return False

def create_test_video(output_path: str, duration: float = 2.0, fps: int = 10):
    """إنشاء فيديو تجريبي للاختبار"""
    try:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (640, 480))
        
        total_frames = int(duration * fps)
        
        for i in range(total_frames):
            # إنشاء إطار ملون متغير
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            color = (i * 10 % 255, (i * 20) % 255, (i * 30) % 255)
            cv2.rectangle(frame, (100, 100), (540, 380), color, -1)
            
            # إضافة نص
            cv2.putText(frame, f"Frame {i}", (200, 250), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        logger.info(f"تم إنشاء فيديو تجريبي: {output_path}")
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الفيديو التجريبي: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("🚀 بدء اختبار التقنيات المتقدمة الجديدة")
    logger.info("=" * 60)
    
    test_results = {}
    
    # اختبار كل تقنية
    test_results['yolo'] = test_yolo_detector()
    print()
    
    test_results['tensorflow'] = test_tensorflow_detector()
    print()
    
    test_results['detectron2'] = test_detectron2_analyzer()
    print()
    
    test_results['pytorchvideo'] = test_pytorchvideo_analyzer()
    print()
    
    test_results['hybrid'] = test_hybrid_analyzer()
    print()
    
    test_results['enhanced_analyzer'] = test_enhanced_content_analyzer()
    print()
    
    # ملخص النتائج
    logger.info("📊 ملخص نتائج الاختبار:")
    logger.info("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    for test_name, result in test_results.items():
        status = "✅ نجح" if result else "❌ فشل"
        logger.info(f"   {test_name}: {status}")
    
    logger.info(f"\nالنتيجة الإجمالية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    if passed_tests == total_tests:
        logger.info("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    elif passed_tests > 0:
        logger.info("⚠️ بعض التقنيات متوفرة. يمكن استخدام النظام مع الميزات المتاحة.")
    else:
        logger.error("❌ لم تنجح أي اختبارات. يرجى التحقق من التثبيت.")
    
    return passed_tests > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
