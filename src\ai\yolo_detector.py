"""
كاشف YOLO للكشف عن الأشياء في الوقت الحقيقي
YOLO Real-time Object Detection System

يستخدم YOLOv8 لكشف الأشخاص والأشياء في البثوث المباشرة بسرعة عالية
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import time

logger = logging.getLogger(__name__)

# محاولة استيراد YOLO
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
    logger.info("✅ YOLOv8 متوفر")
except ImportError:
    YOLO_AVAILABLE = False
    logger.warning("⚠️ YOLOv8 غير متوفر - pip install ultralytics")

@dataclass
class DetectionResult:
    """نتيجة كشف واحدة"""
    class_name: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # x1, y1, x2, y2
    center: Tuple[int, int]
    area: float

@dataclass
class FrameAnalysis:
    """تحليل إطار واحد"""
    frame_number: int
    timestamp: float
    detections: List[DetectionResult]
    person_count: int
    object_count: int
    activity_score: float
    interesting_objects: List[str]

class YOLODetector:
    """كاشف YOLO للكشف عن الأشياء في الوقت الحقيقي"""
    
    def __init__(self, model_size: str = "yolov8n.pt"):
        """تهيئة كاشف YOLO
        
        Args:
            model_size: حجم النموذج (yolov8n.pt, yolov8s.pt, yolov8m.pt, yolov8l.pt, yolov8x.pt)
        """
        self.available = YOLO_AVAILABLE
        self.model = None
        self.model_size = model_size
        
        # الأشياء المثيرة للاهتمام في البثوث
        self.interesting_classes = {
            'person', 'cell phone', 'laptop', 'mouse', 'keyboard', 
            'tv', 'remote', 'microwave', 'bottle', 'cup', 'fork',
            'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich',
            'pizza', 'donut', 'cake', 'chair', 'couch', 'bed',
            'dining table', 'toilet', 'book', 'clock', 'vase',
            'scissors', 'teddy bear', 'hair drier', 'toothbrush',
            'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket'
        }
        
        # عتبات الكشف
        self.confidence_threshold = 0.5
        self.person_confidence_threshold = 0.3  # عتبة أقل للأشخاص
        
        if self.available:
            self._load_model()
    
    def _load_model(self):
        """تحميل نموذج YOLO"""
        try:
            logger.info(f"تحميل نموذج YOLO: {self.model_size}")
            self.model = YOLO(self.model_size)
            logger.info("✅ تم تحميل نموذج YOLO بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تحميل نموذج YOLO: {e}")
            self.available = False
    
    def is_available(self) -> bool:
        """فحص توفر YOLO"""
        return self.available and self.model is not None
    
    def detect_frame(self, frame: np.ndarray, frame_number: int = 0, 
                    timestamp: float = 0.0) -> FrameAnalysis:
        """كشف الأشياء في إطار واحد
        
        Args:
            frame: الإطار المراد تحليله
            frame_number: رقم الإطار
            timestamp: الوقت بالثواني
            
        Returns:
            تحليل الإطار مع النتائج
        """
        if not self.is_available():
            return FrameAnalysis(
                frame_number=frame_number,
                timestamp=timestamp,
                detections=[],
                person_count=0,
                object_count=0,
                activity_score=0.0,
                interesting_objects=[]
            )
        
        try:
            # تشغيل YOLO على الإطار
            results = self.model(frame, verbose=False)
            
            detections = []
            person_count = 0
            interesting_objects = []
            
            # معالجة النتائج
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # الحصول على معلومات الكشف
                        confidence = float(box.conf[0])
                        class_id = int(box.cls[0])
                        class_name = self.model.names[class_id]
                        
                        # تطبيق عتبات مختلفة
                        min_confidence = (self.person_confidence_threshold 
                                        if class_name == 'person' 
                                        else self.confidence_threshold)
                        
                        if confidence >= min_confidence:
                            # الحصول على إحداثيات المربع
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                            
                            # حساب المركز والمساحة
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            area = (x2 - x1) * (y2 - y1)
                            
                            detection = DetectionResult(
                                class_name=class_name,
                                confidence=confidence,
                                bbox=(x1, y1, x2, y2),
                                center=(center_x, center_y),
                                area=area
                            )
                            
                            detections.append(detection)
                            
                            # عد الأشخاص
                            if class_name == 'person':
                                person_count += 1
                            
                            # تسجيل الأشياء المثيرة
                            if class_name in self.interesting_classes:
                                interesting_objects.append(class_name)
            
            # حساب نقاط النشاط
            activity_score = self._calculate_activity_score(detections)
            
            return FrameAnalysis(
                frame_number=frame_number,
                timestamp=timestamp,
                detections=detections,
                person_count=person_count,
                object_count=len(detections) - person_count,
                activity_score=activity_score,
                interesting_objects=list(set(interesting_objects))
            )
            
        except Exception as e:
            logger.error(f"خطأ في كشف الأشياء: {e}")
            return FrameAnalysis(
                frame_number=frame_number,
                timestamp=timestamp,
                detections=[],
                person_count=0,
                object_count=0,
                activity_score=0.0,
                interesting_objects=[]
            )
    
    def _calculate_activity_score(self, detections: List[DetectionResult]) -> float:
        """حساب نقاط النشاط بناءً على الكشوفات
        
        Args:
            detections: قائمة الكشوفات
            
        Returns:
            نقاط النشاط (0-100)
        """
        if not detections:
            return 0.0
        
        score = 0.0
        
        # نقاط للأشخاص
        person_count = sum(1 for d in detections if d.class_name == 'person')
        score += person_count * 20  # 20 نقطة لكل شخص
        
        # نقاط للأشياء المثيرة
        interesting_count = sum(1 for d in detections 
                              if d.class_name in self.interesting_classes)
        score += interesting_count * 10  # 10 نقاط لكل شيء مثير
        
        # نقاط للثقة العالية
        high_confidence_count = sum(1 for d in detections if d.confidence > 0.8)
        score += high_confidence_count * 5  # 5 نقاط إضافية للثقة العالية
        
        # تطبيق حد أقصى
        return min(score, 100.0)
    
    def analyze_video_segment(self, video_path: str, start_time: float, 
                            end_time: float, sample_rate: int = 1) -> List[FrameAnalysis]:
        """تحليل مقطع فيديو
        
        Args:
            video_path: مسار الفيديو
            start_time: وقت البداية بالثواني
            end_time: وقت النهاية بالثواني
            sample_rate: معدل أخذ العينات (كل كم إطار)
            
        Returns:
            قائمة تحليلات الإطارات
        """
        if not self.is_available():
            logger.warning("YOLO غير متوفر")
            return []
        
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            analyses = []
            frame_number = start_frame
            
            while frame_number <= end_frame:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # أخذ عينة حسب المعدل المحدد
                if (frame_number - start_frame) % sample_rate == 0:
                    timestamp = frame_number / fps
                    analysis = self.detect_frame(frame, frame_number, timestamp)
                    analyses.append(analysis)
                
                frame_number += 1
            
            cap.release()
            logger.info(f"تم تحليل {len(analyses)} إطار من المقطع")
            return analyses
            
        except Exception as e:
            logger.error(f"خطأ في تحليل مقطع الفيديو: {e}")
            return []
    
    def find_interesting_moments(self, analyses: List[FrameAnalysis], 
                                min_score: float = 30.0) -> List[Dict[str, Any]]:
        """العثور على اللحظات المثيرة
        
        Args:
            analyses: قائمة تحليلات الإطارات
            min_score: الحد الأدنى لنقاط النشاط
            
        Returns:
            قائمة اللحظات المثيرة
        """
        interesting_moments = []
        
        for analysis in analyses:
            if analysis.activity_score >= min_score:
                moment = {
                    'timestamp': analysis.timestamp,
                    'frame_number': analysis.frame_number,
                    'activity_score': analysis.activity_score,
                    'person_count': analysis.person_count,
                    'object_count': analysis.object_count,
                    'interesting_objects': analysis.interesting_objects,
                    'detections': len(analysis.detections)
                }
                interesting_moments.append(moment)
        
        # ترتيب حسب نقاط النشاط
        interesting_moments.sort(key=lambda x: x['activity_score'], reverse=True)
        
        logger.info(f"تم العثور على {len(interesting_moments)} لحظة مثيرة")
        return interesting_moments
