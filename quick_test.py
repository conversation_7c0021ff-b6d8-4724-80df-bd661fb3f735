#!/usr/bin/env python3
"""
اختبار سريع للنظام
Quick system test
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src للمسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🔍 اختبار الاستيرادات الأساسية...")
    
    try:
        from config.settings import AppSettings
        print("✅ config.settings")
        
        from core.video_processor import VideoProcessor
        print("✅ core.video_processor")
        
        from ai.gemini_client import GeminiClient
        print("✅ ai.gemini_client")
        
        from ai.universal_video_analyzer import UniversalVideoAnalyzer
        print("✅ ai.universal_video_analyzer")
        
        from utils.startup_validator import validate_startup
        print("✅ utils.startup_validator")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_gemini_basic():
    """اختبار أساسي لـ Gemini"""
    print("\n🔑 اختبار Gemini الأساسي...")
    
    try:
        from ai.gemini_client import GeminiClient
        
        client = GeminiClient()
        
        if client.is_available():
            keys_info = client.get_available_keys_info()
            print(f"✅ Gemini متاح - {keys_info['available_keys']}/{keys_info['total_keys']} مفتاح")
            return True
        else:
            print("⚠️ Gemini غير متاح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في Gemini: {e}")
        return False

def test_video_processor_basic():
    """اختبار أساسي لمعالج الفيديو"""
    print("\n🎬 اختبار معالج الفيديو الأساسي...")
    
    try:
        from core.video_processor import VideoProcessor
        
        processor = VideoProcessor()
        
        # اختبار الطرق الجديدة
        methods = ['extract_audio_segments', 'transcribe_audio', 'detect_highlights']
        
        for method in methods:
            if hasattr(processor, method):
                print(f"✅ {method}")
            else:
                print(f"❌ {method} مفقود")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في معالج الفيديو: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🚀 اختبار سريع للنظام")
    print("=" * 30)
    
    tests = [
        ("الاستيرادات الأساسية", test_basic_imports),
        ("Gemini الأساسي", test_gemini_basic),
        ("معالج الفيديو", test_video_processor_basic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
    
    print("\n" + "=" * 30)
    print(f"📊 النتيجة: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 الاختبار السريع نجح!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"💥 خطأ: {e}")
        sys.exit(1)
