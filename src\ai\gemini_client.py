"""
عميل Google Gemini للترجمة وتوليد المحتوى
Google Gemini client for translation and content generation
"""

import logging
import requests
import json
import os
import hashlib
import time
from typing import Optional, Dict, Any, List
from config.settings import AISettings
from ai.gemini_key_manager import gemini_key_manager

logger = logging.getLogger(__name__)

class GeminiClient:
    """عميل للتفاعل مع Google Gemini API"""
    
    def __init__(self, api_key: Optional[str] = None):
        """تهيئة عميل Gemini"""
        # استخدام مدير المفاتيح للحصول على مفتاح متاح
        self.key_manager = gemini_key_manager
        self.api_key = api_key or self.key_manager.get_current_key() or os.getenv('GOOGLE_GEMINI_API_KEY')
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"

        # نظام التخزين المؤقت لتقليل الطلبات المكررة
        self._cache = {}
        self._cache_ttl = 3600  # ساعة واحدة

        if not self.api_key:
            logger.warning("مفتاح Google Gemini API غير متوفر")
            logger.warning("Google Gemini API key not available")
        else:
            logger.info(f"تم تهيئة عميل Google Gemini بنجاح - المفتاح: {self.api_key[-10:]}")
            logger.info("Google Gemini client initialized successfully")

        # التحقق من صحة المفتاح عند بدء التشغيل
        self._validate_api_key_on_startup()
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return bool(self.api_key)

    def _validate_api_key_on_startup(self):
        """التحقق من صحة مفتاح API عند بدء التشغيل"""
        if not self.api_key:
            return

        try:
            # اختبار بسيط للتحقق من صحة المفتاح
            test_url = f"{self.base_url}/gemini-2.0-flash-exp:generateContent"
            headers = {"Content-Type": "application/json"}
            data = {
                "contents": [{"parts": [{"text": "test"}]}],
                "generationConfig": {"maxOutputTokens": 1}
            }
            params = {"key": self.api_key}

            response = requests.post(test_url, headers=headers, json=data, params=params, timeout=10)

            if response.status_code == 200:
                logger.info("✅ مفتاح Gemini API صالح ويعمل بشكل صحيح")
            elif response.status_code == 400 and "API_KEY_INVALID" in response.text:
                logger.error("❌ مفتاح Gemini API غير صالح")
                self.api_key = None
            elif response.status_code == 429:
                logger.warning("⚠️ مفتاح Gemini API وصل للحد الأقصى اليومي")
                self.key_manager.mark_key_as_blocked(self.api_key, "Daily quota exceeded")
            else:
                logger.warning(f"⚠️ استجابة غير متوقعة من Gemini API: {response.status_code}")

        except Exception as e:
            logger.warning(f"⚠️ لا يمكن التحقق من صحة مفتاح Gemini API: {e}")

    def get_available_keys_info(self) -> Dict[str, Any]:
        """الحصول على معلومات المفاتيح المتاحة"""
        return {
            "total_keys": len(self.key_manager.api_keys),
            "available_keys": self.key_manager.get_available_keys_count(),
            "current_key": self.api_key[-10:] if self.api_key else None,
            "blocked_keys": len(self.key_manager.api_keys) - self.key_manager.get_available_keys_count()
        }

    def _get_cache_key(self, text: str, operation: str) -> str:
        """إنشاء مفتاح للتخزين المؤقت"""
        content = f"{operation}:{text}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """الحصول على قيمة من التخزين المؤقت"""
        if cache_key in self._cache:
            cached_data, timestamp = self._cache[cache_key]
            if time.time() - timestamp < self._cache_ttl:
                logger.info("استخدام النتيجة من التخزين المؤقت")
                return cached_data
            else:
                # إزالة البيانات المنتهية الصلاحية
                del self._cache[cache_key]
        return None

    def _save_to_cache(self, cache_key: str, data: Any):
        """حفظ البيانات في التخزين المؤقت"""
        self._cache[cache_key] = (data, time.time())

        # تنظيف التخزين المؤقت من البيانات القديمة
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self._cache.items()
            if current_time - timestamp >= self._cache_ttl
        ]
        for key in expired_keys:
            del self._cache[key]

    def _make_request_with_retry(self, url: str, headers: Dict, data: Dict, params: Dict) -> Optional[Dict]:
        """إجراء طلب مع إعادة المحاولة والتبديل التلقائي للمفاتيح"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # استخدام المفتاح الحالي
                current_key = self.key_manager.get_current_key()
                if not current_key:
                    # محاولة إعادة تفعيل المفاتيح القديمة
                    reset_count = self.key_manager.reset_old_keys(hours_threshold=23.5)  # 23.5 ساعة بدلاً من 24

                    if reset_count > 0:
                        current_key = self.key_manager.get_current_key()
                        logger.info(f"تم إعادة تفعيل {reset_count} مفتاح، المحاولة مرة أخرى")

                    if not current_key:
                        # كحل أخير، إجبار إعادة تفعيل أقرب مفتاح
                        if attempt == max_retries - 1:  # المحاولة الأخيرة
                            if self.key_manager.force_reset_next_key():
                                current_key = self.key_manager.get_current_key()
                                logger.warning("تم إجبار إعادة تفعيل مفتاح للطوارئ")

                    if not current_key:
                        next_available = self.key_manager._get_next_key_available_time()
                        error_msg = f"لا توجد مفاتيح Gemini متاحة حالياً"
                        if next_available:
                            error_msg += f". أقرب مفتاح سيتوفر خلال {next_available}"
                        logger.error(error_msg)
                        return None

                # تحديث المفتاح في الطلب
                params["key"] = current_key

                response = requests.post(url, headers=headers, json=data, params=params, timeout=30)

                if response.status_code == 200:
                    # تسجيل الطلب الناجح
                    self.key_manager.record_successful_request(current_key)
                    return response.json()

                elif response.status_code == 429:
                    # تجاوز الحد اليومي
                    error_text = response.text
                    logger.warning(f"تجاوز الحد اليومي للمفتاح: {current_key[-10:]}")

                    # تمييز المفتاح كمحظور والتبديل للتالي
                    switched = self.key_manager.handle_api_error(current_key, error_text)

                    if switched and attempt < max_retries - 1:
                        logger.info("تم التبديل لمفتاح جديد، إعادة المحاولة...")
                        continue
                    else:
                        logger.error("لا توجد مفاتيح متاحة أخرى")
                        return None

                else:
                    logger.error(f"خطأ في API: {response.status_code} - {response.text}")
                    return None

            except Exception as e:
                logger.error(f"خطأ في الطلب (المحاولة {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    return None

        return None
    
    def translate_text(self, text: str, target_language: str = "ar",
                      source_language: str = "en") -> Optional[str]:
        """ترجمة النص باستخدام Gemini"""
        if not self.is_available():
            logger.error("Gemini API غير متوفر للترجمة")
            return None

        # التحقق من التخزين المؤقت أولاً
        cache_key = self._get_cache_key(f"{text}:{target_language}", "translate")
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        try:
            # إعداد prompt للترجمة
            if target_language == "ar":
                prompt = f"""ترجم النص التالي إلى العربية بطريقة طبيعية ومفهومة:

النص: {text}

الترجمة:"""
            else:
                prompt = f"""Translate the following text to {target_language} naturally:

Text: {text}

Translation:"""
            
            # إرسال الطلب - استخدام Gemini 2.5 Pro
            url = f"{self.base_url}/gemini-2.0-flash-exp:generateContent"
            
            headers = {
                "Content-Type": "application/json",
            }
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.3,
                    "topK": 20,  # تقليل للحد من الاستهلاك
                    "topP": 0.8,  # تقليل للحد من الاستهلاك
                    "maxOutputTokens": 512,  # تقليل لتوفير الاستهلاك
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                content = result["candidates"][0]["content"]["parts"][0]["text"]
                # تنظيف النص المترجم
                translated_text = content.strip()

                # حفظ النتيجة في التخزين المؤقت
                self._save_to_cache(cache_key, translated_text)

                logger.info(f"تم ترجمة النص بنجاح: {text[:50]}...")
                return translated_text
            else:
                logger.error("لا توجد نتائج ترجمة في الاستجابة")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في ترجمة النص باستخدام Gemini: {e}")
            return None
    
    def generate_video_title(self, description: str, moment_type: str = "exciting") -> Optional[str]:
        """توليد عنوان للفيديو"""
        if not self.is_available():
            return None
        
        try:
            # تحديد نوع العنوان حسب نوع اللحظة
            type_prompts = {
                "exciting": "مثير وجذاب",
                "funny": "مضحك ومسلي", 
                "shocking": "صادم ومفاجئ"
            }
            
            type_desc = type_prompts.get(moment_type, "مثير")
            
            prompt = f"""أنشئ عنوان {type_desc} لمقطع فيديو قصير (شورتس) بناءً على الوصف التالي:

الوصف: {description}

متطلبات العنوان:
- يجب أن يكون جذاب ويحفز على المشاهدة
- لا يزيد عن 60 حرف
- يحتوي على إيموجي مناسب
- مناسب لمنصات التواصل الاجتماعي

العنوان:"""
            
            url = f"{self.base_url}/gemini-1.5-pro:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.8,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 100,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                title = result["candidates"][0]["content"]["parts"][0]["text"].strip()
                logger.info(f"تم توليد عنوان: {title}")
                return title

            return None
            
        except Exception as e:
            logger.error(f"خطأ في توليد العنوان: {e}")
            return None
    
    def generate_video_description(self, title: str, moment_type: str = "exciting") -> Optional[str]:
        """توليد وصف للفيديو"""
        if not self.is_available():
            return None
        
        try:
            prompt = f"""أنشئ وصف جذاب لمقطع فيديو شورتس بناءً على العنوان التالي:

العنوان: {title}

متطلبات الوصف:
- يجب أن يكون مشوق ويحفز على المشاهدة
- يحتوي على دعوة للإعجاب والمشاركة
- لا يزيد عن 150 كلمة
- يحتوي على هاشتاغات مناسبة

الوصف:"""
            
            url = f"{self.base_url}/gemini-2.0-flash-exp:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 20,  # تقليل للحد من الاستهلاك
                    "topP": 0.8,  # تقليل للحد من الاستهلاك
                    "maxOutputTokens": 200,  # تقليل لتوفير الاستهلاك
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                description = result["candidates"][0]["content"]["parts"][0]["text"].strip()
                logger.info("تم توليد وصف للفيديو")
                return description

            return None
            
        except Exception as e:
            logger.error(f"خطأ في توليد الوصف: {e}")
            return None
    
    def generate_hashtags(self, content: str, moment_type: str = "exciting") -> List[str]:
        """توليد هاشتاغات للمحتوى"""
        if not self.is_available():
            return []
        
        try:
            prompt = f"""أنشئ قائمة بأفضل الهاشتاغات لمقطع فيديو شورتس بناءً على المحتوى التالي:

المحتوى: {content}
نوع اللحظة: {moment_type}

متطلبات الهاشتاغات:
- 10-15 هاشتاغ
- مزيج من العربية والإنجليزية
- مناسبة لمنصات التواصل الاجتماعي
- تحتوي على هاشتاغات شائعة مثل #shorts #viral #trending

الهاشتاغات (كل هاشتاغ في سطر منفصل):"""
            
            url = f"{self.base_url}/gemini-2.0-flash-exp:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.6,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 200,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                hashtags_text = result["candidates"][0]["content"]["parts"][0]["text"].strip()

                # استخراج الهاشتاغات من النص
                hashtags = []
                for line in hashtags_text.split('\n'):
                    line = line.strip()
                    if line.startswith('#'):
                        hashtags.append(line)
                    elif line and not line.startswith('#'):
                        # إضافة # إذا لم تكن موجودة
                        hashtags.append(f"#{line}")

                logger.info(f"تم توليد {len(hashtags)} هاشتاغ")
                return hashtags[:15]  # حد أقصى 15 هاشتاغ

            return []
            
        except Exception as e:
            logger.error(f"خطأ في توليد الهاشتاغات: {e}")
            return []
    
    def analyze_sentiment(self, text: str) -> Optional[Dict[str, Any]]:
        """تحليل المشاعر في النص"""
        if not self.is_available():
            return None
        
        try:
            prompt = f"""حلل المشاعر في النص التالي وحدد:
1. نوع المشاعر (إيجابي، سلبي، محايد)
2. شدة المشاعر (1-10)
3. المشاعر المحددة (فرح، حزن، غضب، خوف، مفاجأة، إثارة)

النص: {text}

أجب بتنسيق JSON:
{{
  "sentiment": "positive/negative/neutral",
  "intensity": 1-10,
  "emotions": ["emotion1", "emotion2"],
  "confidence": 0.0-1.0
}}"""
            
            url = f"{self.base_url}/gemini-2.0-flash-exp:generateContent"
            
            headers = {"Content-Type": "application/json"}
            
            data = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "temperature": 0.1,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 200,
                }
            }
            
            params = {}  # سيتم تعيين المفتاح في _make_request_with_retry

            result = self._make_request_with_retry(url, headers, data, params)

            if result and "candidates" in result and len(result["candidates"]) > 0:
                response_text = result["candidates"][0]["content"]["parts"][0]["text"].strip()

                # محاولة استخراج JSON من الاستجابة
                try:
                    # البحث عن JSON في النص
                    start_idx = response_text.find('{')
                    end_idx = response_text.rfind('}') + 1

                    if start_idx != -1 and end_idx != -1:
                        json_str = response_text[start_idx:end_idx]
                        sentiment_data = json.loads(json_str)
                        logger.info("تم تحليل المشاعر بنجاح")
                        return sentiment_data
                except json.JSONDecodeError:
                    logger.warning("لا يمكن تحليل استجابة JSON لتحليل المشاعر")

            return None
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر: {e}")
            return None
