#!/usr/bin/env python3
"""
تشغيل التطبيق بدون تحذيرات
Clean Application Launcher

يقوم بتشغيل التطبيق مع إعداد البيئة المناسب لتقليل التحذيرات والرسائل المزعجة
"""

import sys
import os
from pathlib import Path

def main():
    """تشغيل التطبيق بشكل نظيف"""
    
    print("🚀 تشغيل أداة تحرير الفيديو المتقدمة...")
    print("=" * 50)
    
    try:
        # إعداد البيئة أولاً
        print("🔧 إعداد البيئة...")
        
        # إعداد متغيرات البيئة لتقليل التحذيرات
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
        os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
        os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION'] = 'python'
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # تعطيل التحذيرات
        import warnings
        warnings.filterwarnings('ignore', category=UserWarning)
        warnings.filterwarnings('ignore', category=FutureWarning)
        warnings.filterwarnings('ignore', category=DeprecationWarning)
        
        print("✅ تم إعداد البيئة")
        
        # تشغيل التطبيق الرئيسي
        print("🎬 تشغيل التطبيق...")
        
        # استيراد وتشغيل main.py
        import subprocess
        result = subprocess.run([sys.executable, "main.py"], 
                              capture_output=False, 
                              text=True)
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ تم إغلاق التطبيق بنجاح")
    else:
        print("\n❌ حدث خطأ أثناء تشغيل التطبيق")
    
    sys.exit(0 if success else 1)
