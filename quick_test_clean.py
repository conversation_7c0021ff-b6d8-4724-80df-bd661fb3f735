#!/usr/bin/env python3
"""
اختبار سريع ونظيف للتقنيات الجديدة
Quick Clean Test for New Technologies

اختبار سريع للتقنيات المتقدمة الجديدة بدون تحذيرات مزعجة
"""

import sys
import os
from pathlib import Path

# إعداد البيئة أولاً
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION'] = 'python'

import warnings
warnings.filterwarnings('ignore')

# إضافة مجلد src إلى مسار Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

import logging
import numpy as np

# إعداد السجلات البسيط
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_yolo():
    """اختبار YOLO بشكل سريع"""
    try:
        from ai.yolo_detector import YOLODetector
        
        detector = YOLODetector()
        if detector.is_available():
            logger.info("✅ YOLO يعمل بشكل ممتاز")
            return True
        else:
            logger.warning("⚠️ YOLO غير متوفر")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في YOLO: {e}")
        return False

def test_tensorflow():
    """اختبار TensorFlow بشكل سريع"""
    try:
        from ai.tensorflow_detector import TensorFlowDetector
        
        detector = TensorFlowDetector()
        if detector.is_available():
            logger.info("✅ TensorFlow يعمل بشكل ممتاز")
            return True
        else:
            logger.warning("⚠️ TensorFlow غير متوفر")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في TensorFlow: {e}")
        return False

def test_hybrid_system():
    """اختبار النظام الهجين بشكل سريع"""
    try:
        from ai.hybrid_advanced_analyzer import HybridAdvancedAnalyzer
        
        analyzer = HybridAdvancedAnalyzer()
        available_analyzers = analyzer.get_available_analyzers()
        
        if len(available_analyzers) > 0:
            logger.info(f"✅ النظام الهجين يعمل مع {len(available_analyzers)} محلل")
            logger.info(f"   المحللات: {', '.join(available_analyzers)}")
            return True
        else:
            logger.warning("⚠️ النظام الهجين لا يحتوي على محللات")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في النظام الهجين: {e}")
        return False

def test_enhanced_analyzer():
    """اختبار محلل المحتوى المحسن"""
    try:
        from ai.enhanced_content_analyzer import EnhancedContentAnalyzer
        
        analyzer = EnhancedContentAnalyzer()
        features = analyzer.get_available_features()
        
        available_count = sum(features.values())
        total_count = len(features)
        
        if available_count > 0:
            logger.info(f"✅ محلل المحتوى المحسن يعمل ({available_count}/{total_count} ميزة)")
            
            # عرض الميزات المتاحة
            for feature, available in features.items():
                status = "✅" if available else "❌"
                logger.info(f"   {feature}: {status}")
            
            return True
        else:
            logger.warning("⚠️ لا توجد ميزات متاحة في محلل المحتوى")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في محلل المحتوى المحسن: {e}")
        return False

def main():
    """الاختبار السريع الرئيسي"""
    print("🧪 اختبار سريع للتقنيات المتقدمة")
    print("=" * 50)
    
    tests = [
        ("YOLO", test_yolo),
        ("TensorFlow", test_tensorflow),
        ("النظام الهجين", test_hybrid_system),
        ("محلل المحتوى المحسن", test_enhanced_analyzer)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة الإجمالية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع التقنيات تعمل بشكل ممتاز!")
        print("النظام جاهز للاستخدام مع جميع الميزات المتقدمة.")
    elif passed > 0:
        print("⚠️ بعض التقنيات تعمل.")
        print("يمكن استخدام النظام مع الميزات المتاحة.")
    else:
        print("❌ لم تنجح أي تقنية.")
        print("يرجى التحقق من التثبيت.")
    
    return passed > 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
