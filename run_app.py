#!/usr/bin/env python3
"""
تشغيل أداة تحليل البثوث المحسنة
Run Enhanced Livestream Analyzer Tool
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging
from pathlib import Path

# إضافة مجلد src للمسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_logging():
    """إعداد نظام التسجيل"""
    # إنشاء مجلد السجلات
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # إعداد التسجيل
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(logs_dir / 'app.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # تقليل مستوى التسجيل للمكتبات الخارجية
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        messagebox.showerror(
            "خطأ في الإصدار",
            f"يتطلب Python 3.8 أو أحدث\nالإصدار الحالي: {sys.version}"
        )
        return False
    return True

def create_required_directories():
    """إنشاء المجلدات المطلوبة"""
    required_dirs = [
        "temp",
        "output", 
        "downloads",
        "data",
        "logs"
    ]
    
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)

def show_startup_info():
    """عرض معلومات بدء التشغيل"""
    print("🚀 أداة تحليل البثوث المحسنة")
    print("=" * 40)
    print("✨ الميزات الجديدة:")
    print("  • دعم Gemini 2.5 Pro")
    print("  • تحليل محسن للبثوث الطويلة")
    print("  • دعم المقاطع العادية")
    print("  • تحليل متقدم بالذكاء الاصطناعي")
    print("  • فحص تلقائي للمتطلبات")
    print("  • إدارة محسنة للمفاتيح")
    print("=" * 40)

def run_quick_test():
    """اختبار سريع للنظام"""
    try:
        print("🔍 فحص سريع للنظام...")
        
        # اختبار الاستيراد الأساسي
        from config.settings import AppSettings
        from utils.startup_validator import validate_startup
        
        # فحص المتطلبات
        report = validate_startup()
        
        if not report['overall_status']:
            print("⚠️ توجد مشاكل في متطلبات النظام:")
            for error in report['critical_errors'][:3]:
                print(f"  - {error}")
            
            response = input("\nهل تريد المتابعة رغم المشاكل؟ (y/n): ")
            if response.lower() not in ['y', 'yes', 'نعم']:
                return False
        else:
            print("✅ النظام جاهز!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الفحص السريع: {e}")
        response = input("هل تريد المتابعة رغم الخطأ؟ (y/n): ")
        return response.lower() in ['y', 'yes', 'نعم']

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من إصدار Python
        if not check_python_version():
            return False
        
        # إعداد التسجيل
        setup_logging()
        logger = logging.getLogger(__name__)
        
        # إنشاء المجلدات المطلوبة
        create_required_directories()
        
        # عرض معلومات بدء التشغيل
        show_startup_info()
        
        # اختبار سريع
        if not run_quick_test():
            print("❌ تم إلغاء التشغيل")
            return False
        
        print("\n🎬 بدء تشغيل التطبيق...")
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # إخفاء النافذة مؤقتاً أثناء التحميل
        root.withdraw()
        
        try:
            # استيراد وتشغيل التطبيق
            from gui.main_window import VideoEditorApp
            
            # إظهار النافذة
            root.deiconify()
            
            # إنشاء التطبيق
            app = VideoEditorApp(root)
            
            logger.info("تم تشغيل التطبيق بنجاح")
            
            # تشغيل حلقة الأحداث
            root.mainloop()
            
        except ImportError as e:
            logger.error(f"خطأ في استيراد التطبيق: {e}")
            messagebox.showerror(
                "خطأ في التشغيل",
                f"لا يمكن تشغيل التطبيق:\n{e}\n\nتأكد من تثبيت جميع المتطلبات"
            )
            return False
            
        except Exception as e:
            logger.error(f"خطأ في تشغيل التطبيق: {e}")
            messagebox.showerror(
                "خطأ غير متوقع",
                f"حدث خطأ غير متوقع:\n{e}"
            )
            return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
        
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)
