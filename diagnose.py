#!/usr/bin/env python3
"""
تشخيص سريع للنظام
Quick system diagnosis
"""

import sys
import os
import traceback
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def safe_import_test():
    """اختبار الاستيراد الآمن"""
    
    print("🔍 اختبار استيراد المكتبات...")
    print("-" * 40)
    
    imports_status = {}
    
    # اختبار الاستيرادات الأساسية
    basic_imports = [
        ("json", "json"),
        ("os", "os"),
        ("sys", "sys"),
        ("pathlib", "pathlib"),
        ("logging", "logging")
    ]
    
    for name, module in basic_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
            imports_status[name] = True
        except Exception as e:
            print(f"❌ {name}: {e}")
            imports_status[name] = False
    
    # اختبار مكتبات الفيديو
    video_imports = [
        ("OpenCV", "cv2"),
        ("NumPy", "numpy"),
        ("PIL", "PIL"),
        ("MoviePy", "moviepy"),
        ("PyDub", "pydub")
    ]
    
    print(f"\n🎬 مكتبات الفيديو:")
    for name, module in video_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
            imports_status[name] = True
        except Exception as e:
            print(f"❌ {name}: {str(e)[:50]}...")
            imports_status[name] = False
    
    # اختبار مكتبات AI
    ai_imports = [
        ("Requests", "requests"),
        ("Transformers", "transformers")
    ]
    
    print(f"\n🤖 مكتبات AI:")
    for name, module in ai_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
            imports_status[name] = True
        except Exception as e:
            print(f"❌ {name}: {str(e)[:50]}...")
            imports_status[name] = False
    
    return imports_status

def test_gemini_manager():
    """اختبار مدير مفاتيح Gemini"""
    
    print(f"\n🔑 اختبار مدير مفاتيح Gemini...")
    print("-" * 40)
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        print(f"✅ استيراد مدير المفاتيح")
        
        # اختبار الوظائف الأساسية
        try:
            total_keys = len(gemini_key_manager.api_keys)
            print(f"✅ إجمالي المفاتيح: {total_keys}")
        except Exception as e:
            print(f"❌ خطأ في عدد المفاتيح: {e}")
            return False
        
        try:
            status_summary = gemini_key_manager.get_keys_status_summary()
            available_keys = status_summary['available_keys']
            print(f"✅ المفاتيح المتاحة: {available_keys}")
        except Exception as e:
            print(f"❌ خطأ في حالة المفاتيح: {str(e)[:50]}...")
            return False
        
        try:
            current_key = gemini_key_manager.get_current_key()
            if current_key:
                print(f"✅ المفتاح الحالي: ...{current_key[-10:]}")
            else:
                print(f"⚠️  لا يوجد مفتاح حالي")
        except Exception as e:
            print(f"❌ خطأ في المفتاح الحالي: {str(e)[:50]}...")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ فشل استيراد مدير المفاتيح: {str(e)[:50]}...")
        print(f"📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    
    print(f"\n📁 اختبار هيكل الملفات...")
    print("-" * 40)
    
    required_dirs = [
        "src",
        "src/ai",
        "src/gui",
        "src/config",
        "src/utils",
        "data"
    ]
    
    required_files = [
        "src/ai/gemini_key_manager.py",
        "src/ai/gemini_client.py",
        "src/config/settings.py",
        "main.py"
    ]
    
    # فحص المجلدات
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ (مفقود)")
    
    # فحص الملفات
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (مفقود)")

def test_data_files():
    """اختبار ملفات البيانات"""
    
    print(f"\n💾 اختبار ملفات البيانات...")
    print("-" * 40)
    
    data_files = [
        "data/gemini_keys_status.json"
    ]
    
    for file_path in data_files:
        path = Path(file_path)
        if path.exists():
            try:
                # اختبار قراءة الملف
                if file_path.endswith('.json'):
                    import json
                    with open(path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"✅ {file_path} (صالح)")
                else:
                    print(f"✅ {file_path}")
            except Exception as e:
                print(f"⚠️  {file_path} (تالف): {str(e)[:30]}...")
        else:
            print(f"ℹ️  {file_path} (سيتم إنشاؤه)")

def run_quick_fix():
    """تشغيل إصلاح سريع"""
    
    print(f"\n🔧 تشغيل إصلاح سريع...")
    print("-" * 40)
    
    try:
        # إنشاء المجلدات المطلوبة
        required_dirs = ["data", "output", "temp"]
        for dir_name in required_dirs:
            Path(dir_name).mkdir(exist_ok=True)
            print(f"✅ تم إنشاء/التحقق من {dir_name}/")
        
        # محاولة إصلاح مدير المفاتيح
        try:
            from ai.gemini_key_manager import gemini_key_manager
            
            # إعادة تعيين المفاتيح الجديدة
            reset_count = gemini_key_manager.prioritize_new_keys()
            if reset_count > 0:
                print(f"✅ تم إعادة تعيين {reset_count} مفتاح جديد")
            else:
                print(f"ℹ️  المفاتيح مُعدة مسبقاً")
            
            return True
            
        except Exception as e:
            print(f"❌ فشل إصلاح المفاتيح: {str(e)[:50]}...")
            return False
            
    except Exception as e:
        print(f"❌ فشل الإصلاح السريع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🩺 تشخيص سريع للنظام")
    print("=" * 60)
    
    # 1. اختبار الاستيرادات
    imports_ok = safe_import_test()
    
    # 2. اختبار هيكل الملفات
    test_file_structure()
    
    # 3. اختبار ملفات البيانات
    test_data_files()
    
    # 4. اختبار مدير Gemini
    gemini_ok = test_gemini_manager()
    
    # 5. إصلاح سريع إذا لزم الأمر
    if not gemini_ok:
        fix_ok = run_quick_fix()
        if fix_ok:
            # إعادة اختبار مدير Gemini
            gemini_ok = test_gemini_manager()
    
    # النتيجة النهائية
    print(f"\n" + "=" * 60)
    print(f"📊 نتائج التشخيص:")
    
    basic_imports_ok = all(imports_ok.get(name, False) for name in ["json", "os", "sys", "pathlib", "logging"])
    video_imports_ok = imports_ok.get("OpenCV", False) and imports_ok.get("NumPy", False)
    
    if basic_imports_ok:
        print(f"✅ الاستيرادات الأساسية: جيدة")
    else:
        print(f"❌ الاستيرادات الأساسية: مشاكل")
    
    if video_imports_ok:
        print(f"✅ مكتبات الفيديو: جيدة")
    else:
        print(f"⚠️  مكتبات الفيديو: مشاكل (قم بتشغيل: pip install -r requirements.txt)")
    
    if gemini_ok:
        print(f"✅ مدير مفاتيح Gemini: يعمل")
    else:
        print(f"❌ مدير مفاتيح Gemini: مشاكل")
    
    # التوصيات
    print(f"\n💡 التوصيات:")
    
    if basic_imports_ok and gemini_ok:
        print(f"🎉 النظام جاهز للاستخدام!")
        print(f"   python main.py")
        print(f"   أو python quick_start.py")
    elif basic_imports_ok:
        print(f"🔧 جرب إصلاح مفاتيح Gemini:")
        print(f"   python fix_keys.py")
        print(f"   أو python manage_gemini_keys.py")
    else:
        print(f"📦 قم بتثبيت المتطلبات:")
        print(f"   pip install -r requirements.txt")
    
    print(f"=" * 60)
    
    return 0 if (basic_imports_ok and gemini_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
