"""
عميل Clarifai AI Platform
Clarifai AI Platform Client
"""

import logging
import time
import requests
from typing import Dict, List, Optional, Any
from pathlib import Path
import json
import base64

try:
    from clarifai_grpc.channel.clarifai_channel import ClarifaiChannel
    from clarifai_grpc.grpc.api import resources_pb2, service_pb2, service_pb2_grpc
    from clarifai_grpc.grpc.api.status import status_code_pb2
    CLARIFAI_AVAILABLE = True
except ImportError:
    CLARIFAI_AVAILABLE = False

from ..config.api_keys import api_keys_manager

logger = logging.getLogger(__name__)

class ClarifaiClient:
    """عميل للتفاعل مع Clarifai AI Platform"""
    
    def __init__(self):
        self.config = api_keys_manager.get_service_config('clarifai')
        self.stub = None
        self.metadata = None
        
        if not CLARIFAI_AVAILABLE:
            logger.error("مكتبة Clarifai غير متوفرة. قم بتثبيتها: pip install clarifai-grpc")
            return
        
        if not self.config or not self.config.get('enabled'):
            logger.warning("Clarifai غير مُفعل في الإعدادات")
            return
        
        self._initialize_client()
    
    def _initialize_client(self):
        """تهيئة عميل Clarifai"""
        try:
            if not all([
                self.config.get('api_key'),
                self.config.get('user_id'),
                self.config.get('app_id')
            ]):
                logger.error("معلومات Clarifai غير مكتملة")
                return
            
            # إنشاء القناة والعميل
            channel = ClarifaiChannel.get_grpc_channel()
            self.stub = service_pb2_grpc.V2Stub(channel)
            
            # إعداد البيانات الوصفية للمصادقة
            self.metadata = (('authorization', f'Key {self.config["api_key"]}'),)
            
            # اختبار الاتصال
            self._test_connection()
            
            logger.info("تم تهيئة عميل Clarifai بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة عميل Clarifai: {e}")
            self.stub = None
    
    def _test_connection(self):
        """اختبار الاتصال مع Clarifai"""
        try:
            # طلب بسيط للتحقق من الاتصال
            request = service_pb2.GetAppRequest(
                user_app_id=resources_pb2.UserAppIDSet(
                    user_id=self.config['user_id'],
                    app_id=self.config['app_id']
                )
            )
            
            response = self.stub.GetApp(request, metadata=self.metadata)
            
            if response.status.code == status_code_pb2.SUCCESS:
                logger.info("✅ اتصال Clarifai ناجح")
            else:
                logger.warning(f"⚠️ تحذير في اختبار اتصال Clarifai: {response.status.description}")
                
        except Exception as e:
            logger.warning(f"⚠️ تحذير في اختبار اتصال Clarifai: {e}")
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return (CLARIFAI_AVAILABLE and 
                self.stub is not None and 
                self.config and 
                self.config.get('enabled', False))
    
    def _encode_image(self, image_path: str) -> Optional[str]:
        """تشفير الصورة إلى base64"""
        try:
            with open(image_path, 'rb') as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"خطأ في تشفير الصورة: {e}")
            return None
    
    def predict_image_general(self, image_path: str) -> Optional[List[Dict]]:
        """التنبؤ العام للصورة (كشف الكائنات والمفاهيم)"""
        if not self.is_available():
            logger.error("خدمة Clarifai غير متوفرة")
            return None
        
        try:
            # تشفير الصورة
            image_base64 = self._encode_image(image_path)
            if not image_base64:
                return None
            
            # إنشاء الطلب
            request = service_pb2.PostModelOutputsRequest(
                user_app_id=resources_pb2.UserAppIDSet(
                    user_id=self.config['user_id'],
                    app_id=self.config['app_id']
                ),
                model_id='aaa03c23b3724a16a56b629203edc62c',  # General model
                inputs=[
                    resources_pb2.Input(
                        data=resources_pb2.Data(
                            image=resources_pb2.Image(
                                base64=image_base64
                            )
                        )
                    )
                ]
            )
            
            # إرسال الطلب
            response = self.stub.PostModelOutputs(request, metadata=self.metadata)
            
            if response.status.code == status_code_pb2.SUCCESS:
                concepts = []
                for output in response.outputs:
                    for concept in output.data.concepts:
                        concepts.append({
                            'name': concept.name,
                            'confidence': concept.value,
                            'id': concept.id
                        })
                
                logger.info(f"تم كشف {len(concepts)} مفهوم في الصورة")
                return concepts
            else:
                logger.error(f"خطأ في التنبؤ: {response.status.description}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في التنبؤ العام: {e}")
            return None
    
    def detect_nsfw_content(self, image_path: str) -> Optional[Dict[str, Any]]:
        """كشف المحتوى غير اللائق (NSFW)"""
        if not self.is_available():
            logger.error("خدمة Clarifai غير متوفرة")
            return None
        
        try:
            image_base64 = self._encode_image(image_path)
            if not image_base64:
                return None
            
            request = service_pb2.PostModelOutputsRequest(
                user_app_id=resources_pb2.UserAppIDSet(
                    user_id=self.config['user_id'],
                    app_id=self.config['app_id']
                ),
                model_id='e9576d86d2004ed1a38ba0cf39ecb4b1',  # NSFW model
                inputs=[
                    resources_pb2.Input(
                        data=resources_pb2.Data(
                            image=resources_pb2.Image(
                                base64=image_base64
                            )
                        )
                    )
                ]
            )
            
            response = self.stub.PostModelOutputs(request, metadata=self.metadata)
            
            if response.status.code == status_code_pb2.SUCCESS:
                nsfw_results = {}
                for output in response.outputs:
                    for concept in output.data.concepts:
                        nsfw_results[concept.name] = concept.value
                
                logger.info("تم فحص المحتوى غير اللائق")
                return nsfw_results
            else:
                logger.error(f"خطأ في فحص NSFW: {response.status.description}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في فحص المحتوى غير اللائق: {e}")
            return None
    
    def detect_faces(self, image_path: str) -> Optional[List[Dict]]:
        """كشف الوجوه في الصورة"""
        if not self.is_available():
            logger.error("خدمة Clarifai غير متوفرة")
            return None
        
        try:
            image_base64 = self._encode_image(image_path)
            if not image_base64:
                return None
            
            request = service_pb2.PostModelOutputsRequest(
                user_app_id=resources_pb2.UserAppIDSet(
                    user_id=self.config['user_id'],
                    app_id=self.config['app_id']
                ),
                model_id='a403429f2ddf4b49b307e318f00e528b',  # Face detection model
                inputs=[
                    resources_pb2.Input(
                        data=resources_pb2.Data(
                            image=resources_pb2.Image(
                                base64=image_base64
                            )
                        )
                    )
                ]
            )
            
            response = self.stub.PostModelOutputs(request, metadata=self.metadata)
            
            if response.status.code == status_code_pb2.SUCCESS:
                faces = []
                for output in response.outputs:
                    for region in output.data.regions:
                        face_info = {
                            'confidence': region.value,
                            'bounding_box': {
                                'top_row': region.region_info.bounding_box.top_row,
                                'left_col': region.region_info.bounding_box.left_col,
                                'bottom_row': region.region_info.bounding_box.bottom_row,
                                'right_col': region.region_info.bounding_box.right_col
                            },
                            'concepts': []
                        }
                        
                        # إضافة المفاهيم المرتبطة بالوجه
                        for concept in region.data.concepts:
                            face_info['concepts'].append({
                                'name': concept.name,
                                'confidence': concept.value
                            })
                        
                        faces.append(face_info)
                
                logger.info(f"تم كشف {len(faces)} وجه في الصورة")
                return faces
            else:
                logger.error(f"خطأ في كشف الوجوه: {response.status.description}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في كشف الوجوه: {e}")
            return None
    
    def analyze_demographics(self, image_path: str) -> Optional[List[Dict]]:
        """تحليل الديموغرافيا (العمر والجنس)"""
        if not self.is_available():
            logger.error("خدمة Clarifai غير متوفرة")
            return None
        
        try:
            image_base64 = self._encode_image(image_path)
            if not image_base64:
                return None
            
            request = service_pb2.PostModelOutputsRequest(
                user_app_id=resources_pb2.UserAppIDSet(
                    user_id=self.config['user_id'],
                    app_id=self.config['app_id']
                ),
                model_id='c0c0ac362b03416da06ab3fa36fb58e3',  # Demographics model
                inputs=[
                    resources_pb2.Input(
                        data=resources_pb2.Data(
                            image=resources_pb2.Image(
                                base64=image_base64
                            )
                        )
                    )
                ]
            )
            
            response = self.stub.PostModelOutputs(request, metadata=self.metadata)
            
            if response.status.code == status_code_pb2.SUCCESS:
                demographics = []
                for output in response.outputs:
                    for region in output.data.regions:
                        demo_info = {
                            'confidence': region.value,
                            'bounding_box': {
                                'top_row': region.region_info.bounding_box.top_row,
                                'left_col': region.region_info.bounding_box.left_col,
                                'bottom_row': region.region_info.bounding_box.bottom_row,
                                'right_col': region.region_info.bounding_box.right_col
                            },
                            'age': None,
                            'gender': None
                        }
                        
                        # استخراج معلومات العمر والجنس
                        for concept in region.data.concepts:
                            if 'age' in concept.name.lower():
                                demo_info['age'] = {
                                    'category': concept.name,
                                    'confidence': concept.value
                                }
                            elif concept.name.lower() in ['masculine', 'feminine']:
                                demo_info['gender'] = {
                                    'category': concept.name,
                                    'confidence': concept.value
                                }
                        
                        demographics.append(demo_info)
                
                logger.info(f"تم تحليل الديموغرافيا لـ {len(demographics)} شخص")
                return demographics
            else:
                logger.error(f"خطأ في تحليل الديموغرافيا: {response.status.description}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في تحليل الديموغرافيا: {e}")
            return None
    
    def get_service_status(self) -> Dict[str, Any]:
        """الحصول على حالة الخدمة"""
        return {
            'service_name': 'Clarifai AI Platform',
            'available': self.is_available(),
            'grpc_available': CLARIFAI_AVAILABLE,
            'client_initialized': self.stub is not None,
            'config_enabled': self.config.get('enabled', False) if self.config else False,
            'user_id': self.config.get('user_id') if self.config else None,
            'app_id': self.config.get('app_id') if self.config else None,
            'free_tier': self.config.get('free_tier', False) if self.config else False,
            'monthly_limit': self.config.get('monthly_limit') if self.config else None
        }
