#!/usr/bin/env python3
"""
إصلاح وتنظيف مفاتيح Gemini
Fix and clean Gemini API keys
"""

import sys
import os
import re
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def validate_gemini_key(key):
    """التحقق من صحة مفتاح Gemini"""
    # نمط مفتاح Gemini الصحيح
    pattern = r'^AIzaSy[A-Za-z0-9_-]{33}$'
    
    if not key:
        return False, "مفتاح فارغ"
    
    if not isinstance(key, str):
        return False, "نوع البيانات غير صحيح"
    
    # إزالة المسافات والأحرف الخاصة
    key = key.strip()
    
    if not key.startswith('AIzaSy'):
        return False, "لا يبدأ بـ AIzaSy"
    
    if len(key) != 39:
        return False, f"الطول غير صحيح: {len(key)} بدلاً من 39"
    
    if not re.match(pattern, key):
        return False, "يحتوي على أحرف غير صالحة"
    
    return True, "صحيح"

def clean_and_validate_keys():
    """تنظيف والتحقق من جميع المفاتيح"""
    
    print("🔧 تنظيف والتحقق من مفاتيح Gemini...")
    print("=" * 60)
    
    # المفاتيح الجديدة كما تم إدخالها
    raw_new_keys = [
        "AIzaSyC7Z3wBae-3J47R-LaPGnJGTrC22SvDg5M",
        "AIzaSyCQJ2CwE6_oSsBvzUs3TnaiHccXkjtgcWc",
        "AIzaSyBomvlphVTvUleoxzbJM8boL1KvkPuUZVc",
        "AIzaSyBlHvj5vAYWFD_Qs2RPlu5WBUIoPi4WuYQ",  # هذا المفتاح قد يحتوي على مشكلة
        "AIzaSyArno9YCJbb7WfUG5llZgve644RIVRrntw",
        "AIzaSyBNR0YlKBy7UHf_9Gwd7hUtax41bqwAqX4",
        "AIzaSyA52Gg_lI3DN8sv0HykxPhLsmEYpCj6qlU",
        "AIzaSyBn2jVshv_jn60Yi_-UUmw9P31mDIs4w74",
        "AIzaSyAYgcYKr8ROUQjZdZwV9mn7K41gOtcsu1c",
        "AIzaSyDry4JMnQJwyGncKF9va6OC0LAX8b48WHs",
        "AIzaSyDFFCW_iKzlKrxEe4QARSzCL2nw0qsSrh8",
        "AIzaSyD8HVd2wuBDRnGDrKpobj_G2xRAHI6Z-88",
        "AIzaSyCwKaFunKbgO5FE8uEgqxbWgab8LcwBzvc",
        "AIzaSyBiMajBlgp6rC6tTvkAUa6s34EG4VeYZxk",
        "AIzaSyBSbT3AFUoZ4TulCuSgx8QLWp4N2qGe4vg",
        "AIzaSyDskR5hdZtbk8JOo4XjqZaj0F0i5bsJUYg"
    ]
    
    print(f"📊 فحص {len(raw_new_keys)} مفتاح جديد...")
    print("-" * 60)
    
    valid_keys = []
    invalid_keys = []
    
    for i, key in enumerate(raw_new_keys, 1):
        print(f"🔍 فحص المفتاح {i:2d}: ...{key[-10:] if len(key) >= 10 else key}", end=" ")
        
        is_valid, reason = validate_gemini_key(key)
        
        if is_valid:
            print("✅ صحيح")
            valid_keys.append(key)
        else:
            print(f"❌ {reason}")
            invalid_keys.append((key, reason))
    
    print("\n" + "=" * 60)
    print("📊 نتائج الفحص:")
    print(f"   ✅ مفاتيح صحيحة: {len(valid_keys)}")
    print(f"   ❌ مفاتيح غير صحيحة: {len(invalid_keys)}")
    
    if invalid_keys:
        print(f"\n❌ المفاتيح غير الصحيحة:")
        for key, reason in invalid_keys:
            print(f"   🚫 ...{key[-10:] if len(key) >= 10 else key}: {reason}")
    
    if valid_keys:
        print(f"\n✅ المفاتيح الصحيحة:")
        for key in valid_keys:
            print(f"   🔑 {key}")
    
    return valid_keys, invalid_keys

def update_key_manager():
    """تحديث مدير المفاتيح بالمفاتيح الصحيحة فقط"""
    
    print(f"\n🔄 تحديث مدير المفاتيح...")
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        
        # الحصول على المفاتيح الصحيحة
        valid_keys, invalid_keys = clean_and_validate_keys()
        
        if not valid_keys:
            print(f"❌ لا توجد مفاتيح صحيحة للتحديث")
            return False
        
        # إعادة تعيين المفاتيح الجديدة الصحيحة فقط
        for key in valid_keys:
            if key in gemini_key_manager.keys_status:
                gemini_key_manager.keys_status[key] = {
                    "blocked": False,
                    "last_used": 0,
                    "requests_today": 0,
                    "is_new": True,
                    "validated": True
                }
        
        # حفظ التغييرات
        gemini_key_manager._save_keys_status()
        
        # تحديث المفتاح الحالي
        gemini_key_manager.current_key = gemini_key_manager._get_next_available_key()
        
        print(f"✅ تم تحديث {len(valid_keys)} مفتاح صحيح")
        
        if invalid_keys:
            print(f"⚠️  تم تجاهل {len(invalid_keys)} مفتاح غير صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث مدير المفاتيح: {e}")
        return False

def test_system():
    """اختبار النظام بعد الإصلاح"""
    
    print(f"\n🧪 اختبار النظام بعد الإصلاح...")
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        
        # الحصول على حالة النظام
        status_summary = gemini_key_manager.get_keys_status_summary()
        
        print(f"📊 حالة النظام:")
        print(f"   إجمالي المفاتيح: {status_summary['total_keys']}")
        print(f"   المفاتيح المتاحة: {status_summary['available_keys']}")
        print(f"   المفتاح الحالي: {status_summary['current_key_suffix']}")
        
        # اختبار المفتاح الحالي
        current_key = gemini_key_manager.get_current_key()
        if current_key:
            print(f"✅ المفتاح الحالي متاح: ...{current_key[-10:]}")
            return True
        else:
            print(f"❌ لا يوجد مفتاح متاح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🛠️  أداة إصلاح وتنظيف مفاتيح Gemini")
    print("=" * 60)
    
    # 1. تنظيف والتحقق من المفاتيح
    valid_keys, invalid_keys = clean_and_validate_keys()
    
    if not valid_keys:
        print(f"\n❌ لا توجد مفاتيح صحيحة!")
        print(f"💡 تحقق من المفاتيح المدخلة وأعد المحاولة")
        return 1
    
    # 2. تحديث مدير المفاتيح
    update_success = update_key_manager()
    
    if not update_success:
        print(f"\n❌ فشل في تحديث مدير المفاتيح")
        return 1
    
    # 3. اختبار النظام
    test_success = test_system()
    
    print(f"\n" + "=" * 60)
    
    if test_success:
        print(f"🎉 تم إصلاح النظام بنجاح!")
        print(f"✅ {len(valid_keys)} مفتاح صحيح جاهز للاستخدام")
        
        if invalid_keys:
            print(f"⚠️  تم تجاهل {len(invalid_keys)} مفتاح غير صحيح")
        
        print(f"\n💡 يمكنك الآن تشغيل التطبيق:")
        print(f"   python main.py")
        print(f"   أو python quick_start.py")
    else:
        print(f"❌ فشل في إصلاح النظام")
        print(f"💡 جرب تشغيل: python manage_gemini_keys.py")
    
    print(f"=" * 60)
    
    return 0 if test_success else 1

if __name__ == "__main__":
    sys.exit(main())
