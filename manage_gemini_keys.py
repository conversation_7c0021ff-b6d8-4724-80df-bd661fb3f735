#!/usr/bin/env python3
"""
أداة إدارة مفاتيح Gemini
Gemini Keys Management Tool
"""

import sys
import os
import time
from datetime import datetime, timedelta

# إضافة مجلد src إلى المسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai.gemini_key_manager import gemini_key_manager

def show_keys_status():
    """عرض حالة جميع المفاتيح"""
    print("🔑 حالة مفاتيح Gemini API")
    print("=" * 60)
    
    status_summary = gemini_key_manager.get_keys_status_summary()
    
    print(f"📊 الإحصائيات العامة:")
    print(f"   إجمالي المفاتيح: {status_summary['total_keys']}")
    print(f"   المفاتيح المتاحة: {status_summary['available_keys']}")
    print(f"   المفاتيح المحظورة: {status_summary['blocked_keys']}")
    print(f"   المفتاح الحالي: {status_summary['current_key_suffix']}")
    
    print(f"\n📋 تفاصيل المفاتيح:")
    
    available_count = 0
    blocked_count = 0
    
    for key_detail in status_summary['keys_details']:
        status_icon = "✅" if "متاح" in key_detail['status'] else "❌"
        current_icon = "👉" if key_detail['is_current'] else "  "
        
        print(f"{current_icon} {status_icon} {key_detail['index']:2d}. "
              f"...{key_detail['key_suffix']} - {key_detail['status']} "
              f"({key_detail['requests_today']} طلبات)")
        
        if "متاح" in key_detail['status']:
            available_count += 1
        else:
            blocked_count += 1
    
    print(f"\n📈 الملخص:")
    print(f"   ✅ متاح: {available_count}")
    print(f"   ❌ محظور: {blocked_count}")
    
    # عرض وقت توفر المفتاح التالي
    next_available = gemini_key_manager._get_next_key_available_time()
    if next_available and blocked_count > 0:
        print(f"   ⏰ أقرب مفتاح سيتوفر خلال: {next_available}")

def reset_old_keys():
    """إعادة تفعيل المفاتيح القديمة"""
    print("🔄 إعادة تفعيل المفاتيح القديمة...")
    
    reset_count = gemini_key_manager.reset_old_keys(hours_threshold=23.5)
    
    if reset_count > 0:
        print(f"✅ تم إعادة تفعيل {reset_count} مفتاح بنجاح")
    else:
        print("ℹ️  لا توجد مفاتيح قديمة لإعادة تفعيلها")

def force_reset_next_key():
    """إجبار إعادة تفعيل أقرب مفتاح"""
    print("⚠️  إجبار إعادة تفعيل أقرب مفتاح...")
    
    if gemini_key_manager.force_reset_next_key():
        print("✅ تم إجبار إعادة تفعيل مفتاح بنجاح")
    else:
        print("❌ فشل في إعادة تفعيل أي مفتاح")

def prioritize_new_keys():
    """إعطاء أولوية للمفاتيح الجديدة"""
    print("🆕 إعطاء أولوية للمفاتيح الجديدة...")

    reset_count = gemini_key_manager.prioritize_new_keys()

    if reset_count > 0:
        print(f"✅ تم إعادة تعيين {reset_count} مفتاح جديد وإعطاؤها أولوية")

        # عرض حالة المفاتيح الجديدة
        new_status = gemini_key_manager.get_new_keys_status()
        print(f"📊 المفاتيح الجديدة: {new_status['available_new_keys']}/{new_status['total_new_keys']} متاح")
    else:
        print("ℹ️  المفاتيح الجديدة مُعدة مسبقاً")

def reset_all_keys():
    """إعادة تعيين جميع المفاتيح"""
    print("⚠️  هذا سيعيد تعيين جميع المفاتيح!")
    confirm = input("هل أنت متأكد؟ (نعم/لا): ").strip().lower()

    if confirm in ['نعم', 'yes', 'y']:
        gemini_key_manager.reset_all_keys()
        print("✅ تم إعادة تعيين جميع المفاتيح")
    else:
        print("❌ تم إلغاء العملية")

def test_current_key():
    """اختبار المفتاح الحالي"""
    print("🧪 اختبار المفتاح الحالي...")
    
    current_key = gemini_key_manager.get_current_key()
    if not current_key:
        print("❌ لا يوجد مفتاح متاح للاختبار")
        return
    
    print(f"🔑 المفتاح الحالي: ...{current_key[-10:]}")
    
    # محاولة اختبار بسيط
    try:
        from ai.gemini_client import GeminiClient
        client = GeminiClient()
        
        # اختبار بسيط
        result = client.analyze_text("مرحبا", "test")
        
        if result:
            print("✅ المفتاح يعمل بشكل صحيح")
            gemini_key_manager.record_successful_request(current_key)
        else:
            print("❌ المفتاح لا يعمل أو محظور")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المفتاح: {e}")

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "=" * 60)
    print("🛠️  أداة إدارة مفاتيح Gemini API")
    print("=" * 60)
    print("1. عرض حالة المفاتيح")
    print("2. إعادة تفعيل المفاتيح القديمة")
    print("3. إجبار إعادة تفعيل أقرب مفتاح")
    print("4. 🆕 إعطاء أولوية للمفاتيح الجديدة")
    print("5. إعادة تعيين جميع المفاتيح")
    print("6. اختبار المفتاح الحالي")
    print("7. خروج")
    print("-" * 60)

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في أداة إدارة مفاتيح Gemini")
    
    while True:
        show_menu()
        
        try:
            choice = input("اختر رقم العملية (1-7): ").strip()

            if choice == '1':
                show_keys_status()
            elif choice == '2':
                reset_old_keys()
            elif choice == '3':
                force_reset_next_key()
            elif choice == '4':
                prioritize_new_keys()
            elif choice == '5':
                reset_all_keys()
            elif choice == '6':
                test_current_key()
            elif choice == '7':
                print("👋 وداعاً!")
                break
            else:
                print("❌ خيار غير صحيح، يرجى اختيار رقم من 1-7")
                
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
