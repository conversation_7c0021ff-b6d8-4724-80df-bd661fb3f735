"""
نظام التحليل الهجين المتقدم
Hybrid Advanced Analysis System

يدمج جميع التقنيات المتقدمة (YOLO، TensorFlow، Detectron2، PyTorchVideo) 
في نظام موحد يختار أفضل تقنية حسب نوع المحتوى
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional, Union
from dataclasses import dataclass
from pathlib import Path
import time
import json

logger = logging.getLogger(__name__)

# استيراد جميع المحللات المتقدمة
try:
    from ai.yolo_detector import YOLODetector, FrameAnalysis
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    logger.warning("⚠️ YOLODetector غير متوفر")

try:
    from ai.tensorflow_detector import TensorFlowDetector, SceneAnalysis
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("⚠️ TensorFlowDetector غير متوفر")

try:
    from ai.detectron2_analyzer import Detectron2Analyzer, AdvancedAnalysis
    DETECTRON2_AVAILABLE = True
except ImportError:
    DETECTRON2_AVAILABLE = False
    logger.warning("⚠️ Detectron2Analyzer غير متوفر")

try:
    from ai.pytorchvideo_analyzer import PyTorchVideoAnalyzer, TemporalAnalysis
    PYTORCHVIDEO_AVAILABLE = True
except ImportError:
    PYTORCHVIDEO_AVAILABLE = False
    logger.warning("⚠️ PyTorchVideoAnalyzer غير متوفر")

@dataclass
class HybridAnalysisResult:
    """نتيجة التحليل الهجين"""
    timestamp: float
    analysis_method: str
    confidence_score: float
    
    # نتائج YOLO
    yolo_analysis: Optional[FrameAnalysis] = None
    
    # نتائج TensorFlow
    tensorflow_analysis: Optional[SceneAnalysis] = None
    
    # نتائج Detectron2
    detectron2_analysis: Optional[AdvancedAnalysis] = None
    
    # نتائج PyTorchVideo
    pytorchvideo_analysis: Optional[TemporalAnalysis] = None
    
    # التحليل المدمج
    combined_insights: Dict[str, Any] = None
    recommendation: str = ""

@dataclass
class ContentTypeClassification:
    """تصنيف نوع المحتوى"""
    content_type: str
    confidence: float
    recommended_analyzers: List[str]
    analysis_priority: Dict[str, int]

class HybridAdvancedAnalyzer:
    """نظام التحليل الهجين المتقدم"""
    
    def __init__(self):
        """تهيئة النظام الهجين"""
        self.available_analyzers = {}
        
        # تهيئة المحللات المتاحة
        if YOLO_AVAILABLE:
            try:
                self.yolo_detector = YOLODetector()
                if self.yolo_detector.is_available():
                    self.available_analyzers['yolo'] = self.yolo_detector
                    logger.info("✅ YOLO Detector جاهز")
            except Exception as e:
                logger.error(f"خطأ في تهيئة YOLO: {e}")
        
        if TENSORFLOW_AVAILABLE:
            try:
                self.tensorflow_detector = TensorFlowDetector()
                if self.tensorflow_detector.is_available():
                    self.available_analyzers['tensorflow'] = self.tensorflow_detector
                    logger.info("✅ TensorFlow Detector جاهز")
            except Exception as e:
                logger.error(f"خطأ في تهيئة TensorFlow: {e}")
        
        if DETECTRON2_AVAILABLE:
            try:
                self.detectron2_analyzer = Detectron2Analyzer()
                if self.detectron2_analyzer.is_available():
                    self.available_analyzers['detectron2'] = self.detectron2_analyzer
                    logger.info("✅ Detectron2 Analyzer جاهز")
            except Exception as e:
                logger.error(f"خطأ في تهيئة Detectron2: {e}")
        
        if PYTORCHVIDEO_AVAILABLE:
            try:
                self.pytorchvideo_analyzer = PyTorchVideoAnalyzer()
                if self.pytorchvideo_analyzer.is_available():
                    self.available_analyzers['pytorchvideo'] = self.pytorchvideo_analyzer
                    logger.info("✅ PyTorchVideo Analyzer جاهز")
            except Exception as e:
                logger.error(f"خطأ في تهيئة PyTorchVideo: {e}")
        
        # أنواع المحتوى وأولويات التحليل
        self.content_analysis_strategies = {
            'livestream': {
                'primary': ['yolo', 'detectron2'],
                'secondary': ['tensorflow'],
                'temporal': ['pytorchvideo']
            },
            'gaming': {
                'primary': ['yolo', 'tensorflow'],
                'secondary': ['detectron2'],
                'temporal': ['pytorchvideo']
            },
            'social': {
                'primary': ['detectron2', 'yolo'],
                'secondary': ['tensorflow'],
                'temporal': ['pytorchvideo']
            },
            'sports': {
                'primary': ['yolo', 'pytorchvideo'],
                'secondary': ['detectron2', 'tensorflow'],
                'temporal': []
            },
            'reaction': {
                'primary': ['detectron2'],
                'secondary': ['yolo', 'tensorflow'],
                'temporal': ['pytorchvideo']
            }
        }
        
        logger.info(f"تم تهيئة النظام الهجين - المحللات المتاحة: {list(self.available_analyzers.keys())}")
    
    def get_available_analyzers(self) -> List[str]:
        """الحصول على قائمة المحللات المتاحة"""
        return list(self.available_analyzers.keys())
    
    def classify_content_type(self, video_path: str, sample_frames: int = 5) -> ContentTypeClassification:
        """تصنيف نوع المحتوى لاختيار أفضل استراتيجية تحليل
        
        Args:
            video_path: مسار الفيديو
            sample_frames: عدد الإطارات للعينة
            
        Returns:
            تصنيف نوع المحتوى
        """
        try:
            cap = cv2.VideoCapture(video_path)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # اختيار إطارات عشوائية
            frame_indices = np.linspace(0, total_frames - 1, sample_frames, dtype=int)
            
            content_indicators = {
                'person_count': 0,
                'object_diversity': 0,
                'movement_level': 0,
                'scene_complexity': 0
            }
            
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    # تحليل سريع باستخدام YOLO إذا كان متاحاً
                    if 'yolo' in self.available_analyzers:
                        analysis = self.available_analyzers['yolo'].detect_frame(frame)
                        content_indicators['person_count'] += analysis.person_count
                        content_indicators['object_diversity'] += len(set(d.class_name for d in analysis.detections))
                        content_indicators['scene_complexity'] += analysis.activity_score
            
            cap.release()
            
            # تحديد نوع المحتوى بناءً على المؤشرات
            avg_person_count = content_indicators['person_count'] / sample_frames
            avg_complexity = content_indicators['scene_complexity'] / sample_frames
            
            if avg_person_count > 2 and avg_complexity > 50:
                content_type = 'social'
                confidence = 0.8
            elif avg_person_count >= 1 and avg_complexity > 70:
                content_type = 'reaction'
                confidence = 0.7
            elif avg_complexity > 60:
                content_type = 'gaming'
                confidence = 0.6
            elif avg_person_count >= 1:
                content_type = 'livestream'
                confidence = 0.7
            else:
                content_type = 'general'
                confidence = 0.5
            
            # الحصول على الاستراتيجية المناسبة
            strategy = self.content_analysis_strategies.get(content_type, {
                'primary': ['yolo'],
                'secondary': ['tensorflow'],
                'temporal': []
            })
            
            recommended_analyzers = strategy['primary'] + strategy['secondary']
            
            # إنشاء أولويات التحليل
            analysis_priority = {}
            for i, analyzer in enumerate(strategy['primary']):
                analysis_priority[analyzer] = 3 - i  # أولوية عالية
            for i, analyzer in enumerate(strategy['secondary']):
                analysis_priority[analyzer] = 1  # أولوية منخفضة
            
            return ContentTypeClassification(
                content_type=content_type,
                confidence=confidence,
                recommended_analyzers=recommended_analyzers,
                analysis_priority=analysis_priority
            )
            
        except Exception as e:
            logger.error(f"خطأ في تصنيف نوع المحتوى: {e}")
            return ContentTypeClassification(
                content_type='unknown',
                confidence=0.0,
                recommended_analyzers=['yolo'],
                analysis_priority={'yolo': 1}
            )
    
    def analyze_frame_hybrid(self, frame: np.ndarray, timestamp: float = 0.0,
                           content_type: str = None) -> HybridAnalysisResult:
        """تحليل إطار باستخدام النهج الهجين
        
        Args:
            frame: الإطار المراد تحليله
            timestamp: الوقت بالثواني
            content_type: نوع المحتوى (اختياري)
            
        Returns:
            نتيجة التحليل الهجين
        """
        try:
            # تحديد استراتيجية التحليل
            if content_type and content_type in self.content_analysis_strategies:
                strategy = self.content_analysis_strategies[content_type]
            else:
                # استراتيجية افتراضية
                strategy = {
                    'primary': ['yolo'],
                    'secondary': ['tensorflow', 'detectron2'],
                    'temporal': []
                }
            
            # تشغيل المحللات الأساسية
            yolo_analysis = None
            tensorflow_analysis = None
            detectron2_analysis = None
            
            analysis_method = "hybrid"
            confidence_score = 0.0
            
            # YOLO Analysis
            if 'yolo' in strategy['primary'] and 'yolo' in self.available_analyzers:
                yolo_analysis = self.available_analyzers['yolo'].detect_frame(frame, 0, timestamp)
                confidence_score += yolo_analysis.activity_score * 0.3
                analysis_method += "+yolo"
            
            # TensorFlow Analysis
            if 'tensorflow' in (strategy['primary'] + strategy['secondary']) and 'tensorflow' in self.available_analyzers:
                tensorflow_analysis = self.available_analyzers['tensorflow'].analyze_scene(frame, timestamp)
                confidence_score += tensorflow_analysis.quality_score * 0.2
                analysis_method += "+tf"
            
            # Detectron2 Analysis
            if 'detectron2' in (strategy['primary'] + strategy['secondary']) and 'detectron2' in self.available_analyzers:
                detectron2_analysis = self.available_analyzers['detectron2'].analyze_frame(frame, timestamp)
                if detectron2_analysis.scene_understanding:
                    confidence_score += detectron2_analysis.scene_understanding.get('complexity_score', 0) * 0.3
                analysis_method += "+d2"
            
            # دمج النتائج
            combined_insights = self._combine_analysis_results(
                yolo_analysis, tensorflow_analysis, detectron2_analysis
            )
            
            # توليد التوصية
            recommendation = self._generate_recommendation(combined_insights)
            
            return HybridAnalysisResult(
                timestamp=timestamp,
                analysis_method=analysis_method,
                confidence_score=min(confidence_score, 100.0),
                yolo_analysis=yolo_analysis,
                tensorflow_analysis=tensorflow_analysis,
                detectron2_analysis=detectron2_analysis,
                combined_insights=combined_insights,
                recommendation=recommendation
            )
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الهجين: {e}")
            return HybridAnalysisResult(
                timestamp=timestamp,
                analysis_method="error",
                confidence_score=0.0,
                combined_insights={'error': str(e)},
                recommendation="خطأ في التحليل"
            )
    
    def _combine_analysis_results(self, yolo_analysis, tensorflow_analysis, 
                                detectron2_analysis) -> Dict[str, Any]:
        """دمج نتائج التحليل من مختلف المحللات"""
        try:
            combined = {
                'person_count': 0,
                'object_count': 0,
                'activity_level': 'low',
                'scene_quality': 0.0,
                'dominant_elements': [],
                'detected_actions': [],
                'interaction_score': 0.0,
                'highlight_potential': 0.0
            }
            
            # دمج نتائج YOLO
            if yolo_analysis:
                combined['person_count'] = yolo_analysis.person_count
                combined['object_count'] += yolo_analysis.object_count
                combined['dominant_elements'].extend(yolo_analysis.interesting_objects)
                
                if yolo_analysis.activity_score > 70:
                    combined['activity_level'] = 'high'
                elif yolo_analysis.activity_score > 40:
                    combined['activity_level'] = 'medium'
            
            # دمج نتائج TensorFlow
            if tensorflow_analysis:
                combined['scene_quality'] = tensorflow_analysis.quality_score
                combined['dominant_elements'].extend(tensorflow_analysis.dominant_objects)
            
            # دمج نتائج Detectron2
            if detectron2_analysis:
                if detectron2_analysis.interaction_analysis:
                    combined['interaction_score'] = detectron2_analysis.interaction_analysis.get('social_score', 0)
                
                if detectron2_analysis.poses:
                    actions = [pose.action_detected for pose in detectron2_analysis.poses]
                    combined['detected_actions'] = list(set(actions))
            
            # حساب إمكانية كونها لقطة مميزة
            highlight_score = 0
            if combined['person_count'] > 0:
                highlight_score += 20
            if combined['activity_level'] == 'high':
                highlight_score += 30
            if combined['interaction_score'] > 50:
                highlight_score += 25
            if len(combined['detected_actions']) > 0:
                highlight_score += 25
            
            combined['highlight_potential'] = min(highlight_score, 100.0)
            
            # إزالة التكرارات
            combined['dominant_elements'] = list(set(combined['dominant_elements']))
            
            return combined
            
        except Exception as e:
            logger.error(f"خطأ في دمج النتائج: {e}")
            return {'error': str(e)}
    
    def _generate_recommendation(self, combined_insights: Dict[str, Any]) -> str:
        """توليد توصية بناءً على التحليل المدمج"""
        try:
            if 'error' in combined_insights:
                return "خطأ في التحليل"
            
            highlight_potential = combined_insights.get('highlight_potential', 0)
            activity_level = combined_insights.get('activity_level', 'low')
            person_count = combined_insights.get('person_count', 0)
            
            if highlight_potential > 80:
                return "لقطة ممتازة - يُنصح بقصها كمقطع مميز"
            elif highlight_potential > 60:
                return "لقطة جيدة - مناسبة للمقاطع القصيرة"
            elif activity_level == 'high' and person_count > 0:
                return "نشاط عالي - قد تحتوي على لحظات مثيرة"
            elif person_count > 1:
                return "تفاعل اجتماعي - مناسب للمحتوى الاجتماعي"
            else:
                return "محتوى عادي - مناسب كمحتوى خلفية"
                
        except Exception as e:
            logger.error(f"خطأ في توليد التوصية: {e}")
            return "غير محدد"
    
    def analyze_video_segment_hybrid(self, video_path: str, start_time: float, 
                                   end_time: float, sample_rate: int = 2) -> List[HybridAnalysisResult]:
        """تحليل مقطع فيديو باستخدام النهج الهجين
        
        Args:
            video_path: مسار الفيديو
            start_time: وقت البداية بالثواني
            end_time: وقت النهاية بالثواني
            sample_rate: معدل أخذ العينات
            
        Returns:
            قائمة نتائج التحليل الهجين
        """
        try:
            # تصنيف نوع المحتوى أولاً
            content_classification = self.classify_content_type(video_path)
            logger.info(f"نوع المحتوى المكتشف: {content_classification.content_type}")
            
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            analyses = []
            frame_number = start_frame
            
            while frame_number <= end_frame:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # أخذ عينة حسب المعدل المحدد
                if (frame_number - start_frame) % sample_rate == 0:
                    timestamp = frame_number / fps
                    
                    analysis = self.analyze_frame_hybrid(
                        frame, timestamp, content_classification.content_type
                    )
                    analyses.append(analysis)
                
                frame_number += 1
            
            cap.release()
            
            logger.info(f"تم تحليل {len(analyses)} إطار باستخدام النهج الهجين")
            return analyses
            
        except Exception as e:
            logger.error(f"خطأ في تحليل مقطع الفيديو الهجين: {e}")
            return []
    
    def find_best_highlights_hybrid(self, video_path: str, target_clips: int = 5,
                                  min_duration: float = 10.0) -> List[Dict[str, Any]]:
        """العثور على أفضل اللقطات باستخدام التحليل الهجين
        
        Args:
            video_path: مسار الفيديو
            target_clips: عدد المقاطع المطلوبة
            min_duration: الحد الأدنى لمدة المقطع
            
        Returns:
            قائمة أفضل اللقطات
        """
        try:
            # تحليل الفيديو بالكامل
            cap = cv2.VideoCapture(video_path)
            total_duration = cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS)
            cap.release()
            
            # تقسيم الفيديو إلى مقاطع للتحليل
            segment_duration = 30.0  # 30 ثانية لكل مقطع
            segments = []
            
            current_time = 0.0
            while current_time < total_duration:
                end_time = min(current_time + segment_duration, total_duration)
                
                # تحليل المقطع
                analyses = self.analyze_video_segment_hybrid(video_path, current_time, end_time)
                
                if analyses:
                    # حساب متوسط إمكانية كونها لقطة مميزة
                    avg_highlight_potential = np.mean([
                        a.combined_insights.get('highlight_potential', 0) 
                        for a in analyses if a.combined_insights
                    ])
                    
                    segment_info = {
                        'start_time': current_time,
                        'end_time': end_time,
                        'duration': end_time - current_time,
                        'highlight_potential': avg_highlight_potential,
                        'analyses': analyses,
                        'best_analysis': max(analyses, key=lambda x: x.confidence_score) if analyses else None
                    }
                    segments.append(segment_info)
                
                current_time = end_time
            
            # ترتيب المقاطع حسب إمكانية كونها لقطة مميزة
            segments.sort(key=lambda x: x['highlight_potential'], reverse=True)
            
            # اختيار أفضل المقاطع
            best_highlights = []
            for segment in segments[:target_clips]:
                if segment['duration'] >= min_duration:
                    highlight = {
                        'start_time': segment['start_time'],
                        'end_time': segment['end_time'],
                        'duration': segment['duration'],
                        'score': segment['highlight_potential'],
                        'analysis_method': segment['best_analysis'].analysis_method if segment['best_analysis'] else 'unknown',
                        'recommendation': segment['best_analysis'].recommendation if segment['best_analysis'] else '',
                        'insights': segment['best_analysis'].combined_insights if segment['best_analysis'] else {}
                    }
                    best_highlights.append(highlight)
            
            logger.info(f"تم العثور على {len(best_highlights)} لقطة مميزة باستخدام التحليل الهجين")
            return best_highlights
            
        except Exception as e:
            logger.error(f"خطأ في العثور على اللقطات المميزة: {e}")
            return []
