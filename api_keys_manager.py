#!/usr/bin/env python3
"""
أداة إدارة مفاتيح API
API Keys Management Tool

هذه الأداة تساعد في:
- فحص حالة جميع مفاتيح API
- عرض الخدمات المتاحة والمجانية
- إعطاء توصيات للاستخدام الذكي
- التحقق من صحة المفاتيح
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.api_keys import api_keys_manager
from src.utils.startup_validator import validate_startup
import logging

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_header(title: str):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

def print_service_status(service_name: str, status: dict):
    """طباعة حالة خدمة"""
    configured = "✅" if status['configured'] else "❌"
    enabled = "✅" if status['enabled'] else "❌"
    free = "🆓" if status['free_tier'] else "💳"
    
    print(f"{configured} {service_name}")
    print(f"   📝 الوصف: {status['description']}")
    print(f"   🔧 مُكوّن: {configured}")
    print(f"   ⚡ مُفعّل: {enabled}")
    print(f"   💰 مجاني: {free}")
    if status.get('monthly_limit') and isinstance(status['monthly_limit'], (int, float)):
        print(f"   📊 الحد الشهري: {status['monthly_limit']:,} طلب")
    elif status.get('monthly_limit'):
        print(f"   📊 الحد الشهري: {status['monthly_limit']}")
    print()

def show_api_keys_status():
    """عرض حالة جميع مفاتيح API"""
    print_header("🔑 حالة مفاتيح API")
    
    # الحصول على تقرير التحقق
    validation_report = api_keys_manager.validate_api_keys()
    
    print(f"📊 إجمالي الخدمات: {validation_report['total_services']}")
    print(f"✅ الخدمات المُكوّنة: {validation_report['configured_services']}")
    print(f"🆓 الخدمات المجانية المتاحة: {validation_report['free_services']}")
    print(f"⚡ الخدمات المُفعّلة: {validation_report['enabled_services']}")
    
    print_header("📋 تفاصيل الخدمات")
    
    for service_name, status in validation_report['services_status'].items():
        print_service_status(service_name, status)

def show_free_services():
    """عرض الخدمات المجانية المتاحة"""
    print_header("🆓 الخدمات المجانية المتاحة")
    
    free_services = api_keys_manager.get_free_services()
    
    if not free_services:
        print("❌ لا توجد خدمات مجانية مُكوّنة حالياً")
        print("\n💡 نصيحة: قم بتكوين مفاتيح API في ملف .env لاستخدام الخدمات المجانية")
        return
    
    for service_name, config in free_services.items():
        print(f"✅ {config['description']}")
        if config.get('monthly_limit'):
            print(f"   📊 الحد الشهري: {config['monthly_limit']:,} طلب")
        print(f"   🔧 مُفعّل: {'نعم' if config.get('enabled') else 'لا'}")
        print()

def show_smart_usage_recommendations():
    """عرض توصيات الاستخدام الذكي"""
    print_header("🧠 توصيات الاستخدام الذكي")
    
    recommendations = api_keys_manager.get_smart_usage_recommendations()
    
    if not recommendations:
        print("❌ لا توجد خدمات متاحة للحصول على توصيات")
        print("\n💡 نصيحة: قم بتكوين مفاتيح API أولاً")
        return
    
    for service_name, recommendation in recommendations.items():
        service_config = api_keys_manager.get_service_config(service_name)
        print(f"🔧 {service_config['description']}")
        print(f"   💡 {recommendation}")
        print()

def show_setup_guide():
    """عرض دليل الإعداد"""
    print_header("📚 دليل إعداد مفاتيح API")
    
    print("🔑 Hugging Face (مجاني)")
    print("   1. اذهب إلى: https://huggingface.co/settings/tokens")
    print("   2. أنشئ مفتاح API جديد")
    print("   3. أضف المفتاح في ملف .env: HUGGINGFACE_API_KEY=your_key_here")
    print("   📊 30,000 طلب مجاني شهرياً")
    print()
    
    print("🔑 Amazon Rekognition (مجاني للسنة الأولى)")
    print("   1. أنشئ حساب AWS")
    print("   2. احصل على Access Key و Secret Key")
    print("   3. أضف في ملف .env:")
    print("      AWS_ACCESS_KEY_ID=your_access_key")
    print("      AWS_SECRET_ACCESS_KEY=your_secret_key")
    print("      AWS_REKOGNITION_ENABLED=true")
    print("   📊 5,000 صورة مجانية شهرياً")
    print()
    
    print("🔑 Microsoft Azure Computer Vision (مجاني مدى الحياة)")
    print("   1. أنشئ حساب Azure")
    print("   2. أنشئ Computer Vision resource")
    print("   3. أضف في ملف .env:")
    print("      AZURE_COMPUTER_VISION_ENDPOINT=your_endpoint")
    print("      AZURE_COMPUTER_VISION_KEY=your_key")
    print("      AZURE_ENABLED=true")
    print("   📊 5,000 طلب مجاني شهرياً مدى الحياة")
    print()
    
    print("🔑 Clarifai (مجاني)")
    print("   1. أنشئ حساب في: https://clarifai.com/")
    print("   2. أنشئ تطبيق جديد")
    print("   3. أضف في ملف .env:")
    print("      CLARIFAI_API_KEY=your_api_key")
    print("      CLARIFAI_USER_ID=your_user_id")
    print("      CLARIFAI_APP_ID=your_app_id")
    print("      CLARIFAI_ENABLED=true")
    print("   📊 5,000 عملية مجانية شهرياً")

def run_comprehensive_check():
    """تشغيل فحص شامل للنظام"""
    print_header("🔍 فحص شامل للنظام")
    
    print("🔄 جاري فحص جميع المتطلبات...")
    
    # تشغيل فحص بدء التشغيل
    report = validate_startup()
    
    print(f"\n📊 النتائج النهائية:")
    print(f"✅ الحالة العامة: {'جاهز' if report['overall_status'] else 'يحتاج إعداد'}")
    print(f"📈 نسبة النجاح: {report['success_percentage']:.1f}%")
    
    if report['critical_errors']:
        print(f"\n🚨 أخطاء حرجة ({len(report['critical_errors'])}):")
        for error in report['critical_errors']:
            print(f"   ❌ {error}")
    
    if report['warnings']:
        print(f"\n⚠️ تحذيرات ({len(report['warnings'])}):")
        for warning in report['warnings']:
            print(f"   ⚠️ {warning}")

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) < 2:
        print("🔑 أداة إدارة مفاتيح API")
        print("\nالاستخدام:")
        print("  python api_keys_manager.py status      - عرض حالة جميع مفاتيح API")
        print("  python api_keys_manager.py free        - عرض الخدمات المجانية")
        print("  python api_keys_manager.py recommend    - عرض توصيات الاستخدام")
        print("  python api_keys_manager.py setup       - عرض دليل الإعداد")
        print("  python api_keys_manager.py check       - فحص شامل للنظام")
        return
    
    command = sys.argv[1].lower()
    
    if command == "status":
        show_api_keys_status()
    elif command == "free":
        show_free_services()
    elif command == "recommend":
        show_smart_usage_recommendations()
    elif command == "setup":
        show_setup_guide()
    elif command == "check":
        run_comprehensive_check()
    else:
        print(f"❌ أمر غير معروف: {command}")
        print("استخدم أحد الأوامر: status, free, recommend, setup, check")

if __name__ == "__main__":
    main()
