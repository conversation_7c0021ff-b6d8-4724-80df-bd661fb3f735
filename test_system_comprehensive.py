#!/usr/bin/env python3
"""
اختبار شامل لجميع إصلاحات النظام
Comprehensive system test for all fixes
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مجلد src للمسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_results.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_startup_validation():
    """اختبار فحص متطلبات النظام"""
    print("🔍 اختبار فحص متطلبات النظام...")
    
    try:
        from utils.startup_validator import validate_startup
        
        report = validate_startup()
        
        print(f"✅ فحص المتطلبات: {report['passed_checks']}/{report['total_checks']} نجح")
        
        if report['critical_errors']:
            print("❌ أخطاء حرجة:")
            for error in report['critical_errors']:
                print(f"  - {error}")
        
        if report['warnings']:
            print("⚠️ تحذيرات:")
            for warning in report['warnings'][:3]:
                print(f"  - {warning}")
        
        return report['overall_status']
        
    except Exception as e:
        print(f"❌ خطأ في فحص المتطلبات: {e}")
        return False

def test_gemini_api():
    """اختبار مفاتيح Gemini API"""
    print("\n🔑 اختبار مفاتيح Gemini API...")
    
    try:
        from ai.gemini_client import GeminiClient
        
        client = GeminiClient()
        
        if not client.is_available():
            print("❌ لا توجد مفاتيح Gemini متاحة")
            return False
        
        # اختبار الترجمة
        test_text = "Hello, this is a test"
        translation = client.translate_text(test_text, "ar")
        
        if translation:
            print(f"✅ اختبار الترجمة نجح: {translation}")
        else:
            print("❌ فشل اختبار الترجمة")
            return False
        
        # عرض معلومات المفاتيح
        keys_info = client.get_available_keys_info()
        print(f"📊 المفاتيح: {keys_info['available_keys']}/{keys_info['total_keys']} متاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Gemini API: {e}")
        return False

def test_google_cloud():
    """اختبار Google Cloud"""
    print("\n☁️ اختبار Google Cloud...")
    
    try:
        from ai.google_cloud_client import GoogleCloudClient
        
        client = GoogleCloudClient()
        
        if not client.is_available():
            print("⚠️ Google Cloud غير متوفر")
            return False
        
        # عرض حالة الاعتمادات
        status = client.get_credentials_status()
        print(f"📊 حالة Google Cloud:")
        print(f"  - Video Intelligence: {'✅' if status['video_client_available'] else '❌'}")
        print(f"  - Speech: {'✅' if status['speech_client_available'] else '❌'}")
        print(f"  - Translate: {'✅' if status['translate_client_available'] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Google Cloud: {e}")
        return False

def test_huggingface():
    """اختبار Hugging Face"""
    print("\n🤗 اختبار Hugging Face...")
    
    try:
        from ai.huggingface_client import HuggingFaceClient
        
        client = HuggingFaceClient()
        
        if not client.is_available():
            print("⚠️ Hugging Face API غير متوفر")
            return False
        
        # عرض حالة API
        status = client.get_api_status()
        print(f"📊 حالة Hugging Face:")
        print(f"  - المفتاح: {'✅' if status['api_key_available'] else '❌'}")
        print(f"  - الطلبات اليومية: {status['daily_requests']}/{status['daily_limit']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Hugging Face: {e}")
        return False

def test_video_processor():
    """اختبار معالج الفيديو"""
    print("\n🎬 اختبار معالج الفيديو...")
    
    try:
        from core.video_processor import VideoProcessor
        
        processor = VideoProcessor()
        
        # اختبار الطرق الجديدة
        methods_to_test = [
            'extract_audio_segments',
            'transcribe_audio', 
            'detect_highlights',
            '_detect_scene_changes'
        ]
        
        for method_name in methods_to_test:
            if hasattr(processor, method_name):
                print(f"✅ الطريقة {method_name} متوفرة")
            else:
                print(f"❌ الطريقة {method_name} غير متوفرة")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالج الفيديو: {e}")
        return False

def test_enhanced_analyzer():
    """اختبار المحلل المحسن"""
    print("\n🧠 اختبار المحلل المحسن...")
    
    try:
        from ai.enhanced_content_analyzer import EnhancedContentAnalyzer
        
        analyzer = EnhancedContentAnalyzer()
        
        # عرض الميزات المتاحة
        features = analyzer.get_available_features()
        print("📊 الميزات المتقدمة:")
        for feature, available in features.items():
            status = "✅" if available else "❌"
            print(f"  - {feature}: {status}")
        
        advanced_available = analyzer.is_advanced_analysis_available()
        print(f"🔬 التحليل المتقدم: {'✅ متاح' if advanced_available else '❌ غير متاح'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المحلل المحسن: {e}")
        return False

def test_universal_analyzer():
    """اختبار المحلل الشامل"""
    print("\n🌐 اختبار المحلل الشامل...")
    
    try:
        from ai.universal_video_analyzer import UniversalVideoAnalyzer
        
        analyzer = UniversalVideoAnalyzer()
        
        # اختبار التنسيقات المدعومة
        supported_formats = analyzer.get_supported_formats()
        print(f"📁 التنسيقات المدعومة: {len(supported_formats)} تنسيق")
        
        # اختبار بعض التنسيقات
        test_formats = ['.mp4', '.avi', '.mov', '.xyz']
        for fmt in test_formats:
            supported = analyzer.is_supported_format(f"test{fmt}")
            status = "✅" if supported else "❌"
            print(f"  - {fmt}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المحلل الشامل: {e}")
        return False

def test_youtube_downloader():
    """اختبار محمل YouTube"""
    print("\n📺 اختبار محمل YouTube...")
    
    try:
        from utils.youtube_downloader import YouTubeDownloader
        
        downloader = YouTubeDownloader()
        
        # اختبار التحقق من الروابط
        test_urls = [
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "https://example.com/video.mp4"
        ]
        
        for url in test_urls:
            is_youtube = downloader.is_youtube_url(url)
            status = "✅" if is_youtube else "❌"
            print(f"  - {url}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محمل YouTube: {e}")
        return False

def test_gui_components():
    """اختبار مكونات الواجهة"""
    print("\n🖥️ اختبار مكونات الواجهة...")
    
    try:
        # اختبار استيراد المكونات الرئيسية
        components = [
            'gui.main_window',
            'utils.startup_validator'
        ]

        # اختبار المحلل الشامل منفصلاً
        try:
            from gui.universal_analyzer_window import UniversalAnalyzerWindow
            print("✅ gui.universal_analyzer_window متوفر")
        except ImportError as e:
            print(f"⚠️ gui.universal_analyzer_window غير متوفر: {e}")
        except Exception as e:
            print(f"⚠️ مشكلة في gui.universal_analyzer_window: {e}")
        
        for component in components:
            try:
                __import__(component)
                print(f"✅ {component} متوفر")
            except ImportError as e:
                print(f"❌ {component} غير متوفر: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكونات الواجهة: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🚀 بدء الاختبار الشامل للنظام")
    print("=" * 50)
    
    tests = [
        ("فحص متطلبات النظام", test_startup_validation),
        ("مفاتيح Gemini API", test_gemini_api),
        ("Google Cloud", test_google_cloud),
        ("Hugging Face", test_huggingface),
        ("معالج الفيديو", test_video_processor),
        ("المحلل المحسن", test_enhanced_analyzer),
        ("المحلل الشامل", test_universal_analyzer),
        ("محمل YouTube", test_youtube_downloader),
        ("مكونات الواجهة", test_gui_components)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار الشامل")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    print(f"\n📈 النتيجة الإجمالية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
