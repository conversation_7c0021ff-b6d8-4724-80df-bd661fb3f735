"""
مدير مفاتيح Gemini مع التبديل التلقائي
Gemini API key manager with automatic rotation
"""

import logging
import json
import os
import time
from typing import List, Optional, Dict, Any
from pathlib import Path
import random

logger = logging.getLogger(__name__)

class GeminiKeyManager:
    """مدير مفاتيح Gemini مع التبديل التلقائي عند انتهاء الحد اليومي"""
    
    def __init__(self):
        """تهيئة مدير المفاتيح"""
        
        # قائمة جميع مفاتيح Gemini المتاحة (محدثة)
        self.api_keys = [
            # المفاتيح الجديدة (أولوية عالية)
            "AIzaSyC7Z3wBae-3J47R-LaPGnJGTrC22SvDg5M",
            "AIzaSyCQJ2CwE6_oSsBvzUs3TnaiHccXkjtgcWc",
            "AIzaSyBomvlphVTvUleoxzbJM8boL1KvkPuUZVc",
            "AIzaSyBlHvj5vAYWFD_Qs2RPlu5WBUIoPi4WuYQ",
            "AIzaSyArno9YCJbb7WfUG5llZgve644RIVRrntw",
            "AIzaSyBNR0YlKBy7UHf_9Gwd7hUtax41bqwAqX4",
            "AIzaSyA52Gg_lI3DN8sv0HykxPhLsmEYpCj6qlU",
            "AIzaSyBn2jVshv_jn60Yi_-UUmw9P31mDIs4w74",
            "AIzaSyAYgcYKr8ROUQjZdZwV9mn7K41gOtcsu1c",
            "AIzaSyDry4JMnQJwyGncKF9va6OC0LAX8b48WHs",
            "AIzaSyDFFCW_iKzlKrxEe4QARSzCL2nw0qsSrh8",
            "AIzaSyD8HVd2wuBDRnGDrKpobj_G2xRAHI6Z-88",
            "AIzaSyCwKaFunKbgO5FE8uEgqxbWgab8LcwBzvc",
            "AIzaSyBiMajBlgp6rC6tTvkAUa6s34EG4VeYZxk",
            "AIzaSyBSbT3AFUoZ4TulCuSgx8QLWp4N2qGe4vg",
            "AIzaSyDskR5hdZtbk8JOo4XjqZaj0F0i5bsJUYg",

            # المفاتيح القديمة (احتياطية)
            "AIzaSyD7E2B9iYXV9_2houLLHDWXUA-K53kUGq0",
            "AIzaSyBY58cZFvFzRQpzzYJ7m1VjwvS7af-vHhM",
            "AIzaSyBSbEdSARy5ims96kxF1om2725VZxwl6nU",
            "AIzaSyBx2D9UdlvuSCH4Z7jaT16S0MREkNIuVNc",
            "AIzaSyAuI06np4vmdKkWN1JucexLW1mO0ESEyts",
            "AIzaSyC4_fX42vOZYfuF56i_lNSJjiEs02vX3Uo",
            "AIzaSyDZfiLXBVs8yBk0CDb4hvLZ_l8P6tKy6og",
            "AIzaSyC_3oOs766IXUlWFdwUSNTVAhg_GLFfb1E",
            "AIzaSyA9sCmQsOZIPKRKX14aMJsC8Mt7IFPsYE8",
            "AIzaSyD_YyLNsLeKeFYUX7KvTw5fVJyneWMNjl8",
            "AIzaSyD1c3SzOGL4M8O1qZ6AejUy7jm567Nbf5w",
            "AIzaSyBSeMPla9eDR2VCmgS0fub-EujNsLO7EDk",
            "AIzaSyCxWJz_Cybce02eEXZrOnvZcEaD9yiFIQI",
            "AIzaSyAq6w_rvoIiZLxX191-UZDrhibbA6adDAw",
            "AIzaSyCzpJ4I958670qvycQgL9oay1Mpjp1q3zA",
            "AIzaSyCJajG5U6RH9KPMaZYXAcMGNldpOunf4nU",
            "AIzaSyDtfQhkANpIM-cHRy-UdMhHfROzSjBX5SY",
            "AIzaSyD-461tFFQsQcOxadaAQVWG1VN5ZvCxAYU",
            "AIzaSyAGoM8irNjhKPZcU8hBv1rpLpIhhy5eZ0E",
            "AIzaSyCyjFbianBAZ3eYOqIH2Yf0J70FUgWh5wg",
            "AIzaSyASQK3r61z8OKrW6WIru6DZt3T0NFwkPCI",
            "AIzaSyCxhQrKeoPrgZSQshH_Ij4sqLNxbjVuIDE",
            "AIzaSyCO9ok1iCqfFbtVyDJdVpK_JoLraNS6aHU",
            "AIzaSyAV3uTUtTKlSHgU3cge20vnQbusNeM-Wxk",
            "AIzaSyAnnYx15P89izzx_rJy9en4kaLVJ_Nuk40",
            "AIzaSyAhpl7qAaouwRb6niHi9UMNnOxQVzTZXeo",
            "AIzaSyCeSyykhVy6BXB_Do-V934k--VxwvdWAok",
            "AIzaSyDLlAe9XxHdbJ8affKNqWKD5509E0roQ2E",
            "AIzaSyC2Q_XqKuefQ0badweq13D5mjLVMhmA6BU",
            "AIzaSyA5W4wz0mpnajLAzHj3w_X_U9ACNr0Web4",
            "AIzaSyChdkhUNWaXlDDyGN7Zy-DEHOTHw79guH8",
            "AIzaSyB8ZRBRJ-ouTxBHVzOXZaCjD_z2PfZzYFE",
            "AIzaSyAMbRJtbmTa8xjGJuIX3wUcvIwSFuMB5gM",
            "AIzaSyAjHe3qCwNjcaEEie9ZC84gMIr2dy5NR28",
            "AIzaSyClKt_GqxphM-5aFYUMLzT925OgTtxxIhY",
            "AIzaSyBDASshsMMS7K17y9j6I0A7amdnOEJQ78Q",
            "AIzaSyC5F-PHaWU9yb4_mv_K7E6RbrhOJyYS5-U",
            "AIzaSyAERiSzXO9roZ49OLyd9FvhezU5u193c2g",
            "AIzaSyAQuKvRKMP48MB2RePpur8Wp6f5iYBpkaA",
            "AIzaSyBnxTW0uaIjIacKMHShqsjL_EMzk6NribQ",
            "AIzaSyDDK44eqIOF7rfz2RTscDRoAbk7IPwaKt8",
            "AIzaSyBIlkoOdvE7oCP8oJlXUrE2YcbFCp5Ioyo",
            "AIzaSyBELBROWW0EBVVmuTNvt32sD6hbj0Tq6MU",
            "AIzaSyBGYJPjJ5aycTP5i--CXPKXe53wuA1jHgE",
            "AIzaSyD9XM4KEqt_VmzBbLqCROtaydXICu7-ymw",
            "AIzaSyCulDVPsLQXChqpbYVERipHvdOGohhpMP0",
            "AIzaSyAybDJWXToHHzg9VUERbNwnGc7c6l6GNqE",
            "AIzaSyBOPeEmj0j2svyoAyqMwpFYr4Iy2qYliEk"
        ]
        
        # إزالة المفاتيح المكررة
        self.api_keys = list(set(self.api_keys))
        
        # ملف تتبع حالة المفاتيح
        self.status_file = Path("data/gemini_keys_status.json")
        self.status_file.parent.mkdir(exist_ok=True)
        
        # تحميل حالة المفاتيح
        self.keys_status = self._load_keys_status()
        
        # المفتاح الحالي
        self.current_key_index = 0
        self.current_key = self._get_next_available_key()
        
        logger.info(f"تم تهيئة مدير مفاتيح Gemini مع {len(self.api_keys)} مفتاح")
    
    def _load_keys_status(self) -> Dict[str, Any]:
        """تحميل حالة المفاتيح من الملف"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    loaded_status = json.load(f)

                    # التأكد من وجود جميع المفاتيح في الحالة المحملة
                    for key in self.api_keys:
                        if key not in loaded_status:
                            loaded_status[key] = {"blocked": False, "last_used": 0, "requests_today": 0}

                    return loaded_status
            else:
                # إنشاء حالة افتراضية
                return {key: {"blocked": False, "last_used": 0, "requests_today": 0}
                       for key in self.api_keys}
        except Exception as e:
            logger.error(f"خطأ في تحميل حالة المفاتيح: {str(e)[:100]}")
            return {key: {"blocked": False, "last_used": 0, "requests_today": 0}
                   for key in self.api_keys}
    
    def _save_keys_status(self):
        """حفظ حالة المفاتيح في الملف"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.keys_status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"خطأ في حفظ حالة المفاتيح: {e}")
    
    def _get_next_available_key(self) -> Optional[str]:
        """الحصول على المفتاح التالي المتاح"""
        current_time = time.time()

        # فحص جميع المفاتيح للعثور على مفتاح متاح
        available_keys = []
        recently_unblocked = []

        for i, key in enumerate(self.api_keys):
            key_status = self.keys_status.get(key, {})

            # إعادة تعيين المفاتيح المحظورة بعد 24 ساعة
            if key_status.get("blocked", False):
                last_used = key_status.get("last_used", 0)
                hours_passed = (current_time - last_used) / 3600

                if hours_passed > 24:  # 24 ساعة
                    self.keys_status[key]["blocked"] = False
                    self.keys_status[key]["requests_today"] = 0
                    recently_unblocked.append((key, i))
                    logger.info(f"تم إعادة تفعيل المفتاح: {key[-10:]} (بعد {hours_passed:.1f} ساعة)")
                else:
                    # حساب الوقت المتبقي
                    hours_left = 24 - hours_passed
                    logger.debug(f"المفتاح {key[-10:]} محظور لـ {hours_left:.1f} ساعة أخرى")

            # إذا كان المفتاح غير محظور
            if not self.keys_status[key].get("blocked", False):
                available_keys.append((key, i))

        # إعطاء أولوية للمفاتيح المُعاد تفعيلها حديثاً
        if recently_unblocked:
            key, index = recently_unblocked[0]
            self.current_key_index = index
            self._save_keys_status()  # حفظ التغييرات
            return key

        # إذا كان هناك مفاتيح متاحة أخرى
        if available_keys:
            key, index = available_keys[0]
            self.current_key_index = index
            return key

        # إذا لم يتم العثور على مفتاح متاح
        next_available_time = self._get_next_key_available_time()
        if next_available_time:
            logger.warning(f"جميع مفاتيح Gemini محظورة مؤقتاً. أقرب مفتاح سيتوفر خلال {next_available_time}")
        else:
            logger.warning("جميع مفاتيح Gemini محظورة مؤقتاً")

        return None

    def _get_next_key_available_time(self) -> Optional[str]:
        """حساب وقت توفر المفتاح التالي"""
        try:
            current_time = time.time()
            min_wait_time = float('inf')

            for key in self.api_keys:
                key_status = self.keys_status.get(key, {})

                if key_status.get("blocked", False):
                    last_used = key_status.get("last_used", 0)
                    hours_passed = (current_time - last_used) / 3600
                    hours_left = 24 - hours_passed

                    if hours_left > 0:
                        min_wait_time = min(min_wait_time, hours_left)

            if min_wait_time != float('inf'):
                if min_wait_time < 1:
                    return f"{min_wait_time * 60:.0f} دقيقة"
                else:
                    return f"{min_wait_time:.1f} ساعة"

            return None

        except Exception as e:
            logger.error(f"خطأ في حساب وقت توفر المفتاح: {e}")
            return None
    
    def get_current_key(self) -> Optional[str]:
        """الحصول على المفتاح الحالي"""
        return self.current_key
    
    def mark_key_as_blocked(self, key: str, error_message: str = ""):
        """تمييز مفتاح كمحظور"""
        if key in self.keys_status:
            self.keys_status[key]["blocked"] = True
            self.keys_status[key]["last_used"] = time.time()
            self.keys_status[key]["error"] = error_message
            self._save_keys_status()
            
            logger.warning(f"تم حظر المفتاح: {key[-10:]} - {error_message}")
            
            # الانتقال للمفتاح التالي
            self.current_key = self._get_next_available_key()
            if self.current_key:
                logger.info(f"تم التبديل للمفتاح: {self.current_key[-10:]}")
    
    def record_successful_request(self, key: str):
        """تسجيل طلب ناجح"""
        if key in self.keys_status:
            self.keys_status[key]["requests_today"] = self.keys_status[key].get("requests_today", 0) + 1
            self.keys_status[key]["last_used"] = time.time()
            self._save_keys_status()
    
    def is_quota_exceeded_error(self, error_message: str) -> bool:
        """فحص ما إذا كان الخطأ بسبب تجاوز الحد اليومي"""
        quota_errors = [
            "quota",
            "rate limit",
            "exceeded",
            "RESOURCE_EXHAUSTED",
            "429",
            "too many requests"
        ]
        
        error_lower = error_message.lower()
        return any(error in error_lower for error in quota_errors)
    
    def handle_api_error(self, key: str, error_message: str) -> bool:
        """التعامل مع خطأ API وإرجاع True إذا تم التبديل للمفتاح التالي"""
        if self.is_quota_exceeded_error(error_message):
            self.mark_key_as_blocked(key, error_message)
            return self.current_key is not None
        
        return False
    
    def get_available_keys_count(self) -> int:
        """الحصول على عدد المفاتيح المتاحة"""
        current_time = time.time()
        available_count = 0
        
        for key in self.api_keys:
            key_status = self.keys_status.get(key, {})
            
            # فحص ما إذا كان المفتاح متاح
            if not key_status.get("blocked", False):
                available_count += 1
            else:
                # فحص ما إذا كان يمكن إعادة تفعيله
                last_used = key_status.get("last_used", 0)
                if current_time - last_used > 24 * 3600:
                    available_count += 1
        
        return available_count
    
    def get_keys_status_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص حالة المفاتيح"""
        total_keys = len(self.api_keys)
        available_keys = self.get_available_keys_count()
        blocked_keys = total_keys - available_keys
        
        current_time = time.time()
        keys_details = []
        
        for i, key in enumerate(self.api_keys):
            key_status = self.keys_status.get(key, {})
            
            status = "متاح"
            if key_status.get("blocked", False):
                last_used = key_status.get("last_used", 0)
                hours_left = max(0, 24 - (current_time - last_used) / 3600)
                if hours_left > 0:
                    status = f"محظور ({hours_left:.1f}h متبقية)"
                else:
                    status = "جاهز للإعادة تفعيل"
            
            keys_details.append({
                "index": i,
                "key_suffix": key[-10:],
                "status": status,
                "requests_today": key_status.get("requests_today", 0),
                "is_current": key == self.current_key
            })
        
        return {
            "total_keys": total_keys,
            "available_keys": available_keys,
            "blocked_keys": blocked_keys,
            "current_key_suffix": self.current_key[-10:] if self.current_key else "لا يوجد",
            "keys_details": keys_details
        }
    
    def reset_all_keys(self):
        """إعادة تعيين جميع المفاتيح (للطوارئ)"""
        self.keys_status = {key: {"blocked": False, "last_used": 0, "requests_today": 0}
                           for key in self.api_keys}
        self._save_keys_status()
        self.current_key = self._get_next_available_key()
        logger.info("تم إعادة تعيين جميع مفاتيح Gemini")

    def reset_old_keys(self, hours_threshold: float = 24.0):
        """إعادة تعيين المفاتيح القديمة التي تجاوزت المدة المحددة"""
        current_time = time.time()
        reset_count = 0

        for key in self.api_keys:
            key_status = self.keys_status.get(key, {})

            if key_status.get("blocked", False):
                last_used = key_status.get("last_used", 0)
                hours_passed = (current_time - last_used) / 3600

                if hours_passed >= hours_threshold:
                    self.keys_status[key]["blocked"] = False
                    self.keys_status[key]["requests_today"] = 0
                    reset_count += 1
                    logger.info(f"تم إعادة تفعيل المفتاح: {key[-10:]} (بعد {hours_passed:.1f} ساعة)")

        if reset_count > 0:
            self._save_keys_status()
            self.current_key = self._get_next_available_key()
            logger.info(f"تم إعادة تفعيل {reset_count} مفتاح")

        return reset_count

    def force_reset_next_key(self):
        """إجبار إعادة تفعيل أقرب مفتاح للتوفر (للطوارئ)"""
        current_time = time.time()
        best_key = None
        min_wait_time = float('inf')

        for key in self.api_keys:
            key_status = self.keys_status.get(key, {})

            if key_status.get("blocked", False):
                last_used = key_status.get("last_used", 0)
                hours_passed = (current_time - last_used) / 3600
                hours_left = 24 - hours_passed

                if hours_left < min_wait_time:
                    min_wait_time = hours_left
                    best_key = key

        if best_key:
            self.keys_status[best_key]["blocked"] = False
            self.keys_status[best_key]["requests_today"] = 0
            self._save_keys_status()
            self.current_key = best_key
            logger.warning(f"تم إجبار إعادة تفعيل المفتاح: {best_key[-10:]} (كان محظور لـ {24 - min_wait_time:.1f} ساعة)")
            return True

        return False

    def prioritize_new_keys(self):
        """إعطاء أولوية للمفاتيح الجديدة وإعادة تعيينها"""
        # قائمة المفاتيح الجديدة
        new_keys = [
            "AIzaSyC7Z3wBae-3J47R-LaPGnJGTrC22SvDg5M",
            "AIzaSyCQJ2CwE6_oSsBvzUs3TnaiHccXkjtgcWc",
            "AIzaSyBomvlphVTvUleoxzbJM8boL1KvkPuUZVc",
            "AIzaSyBlHvj5vAYWFD_Qs2RPlu5WBUIoPi4WuYQ",
            "AIzaSyArno9YCJbb7WfUG5llZgve644RIVRrntw",
            "AIzaSyBNR0YlKBy7UHf_9Gwd7hUtax41bqwAqX4",
            "AIzaSyA52Gg_lI3DN8sv0HykxPhLsmEYpCj6qlU",
            "AIzaSyBn2jVshv_jn60Yi_-UUmw9P31mDIs4w74",
            "AIzaSyAYgcYKr8ROUQjZdZwV9mn7K41gOtcsu1c",
            "AIzaSyDry4JMnQJwyGncKF9va6OC0LAX8b48WHs",
            "AIzaSyDFFCW_iKzlKrxEe4QARSzCL2nw0qsSrh8",
            "AIzaSyD8HVd2wuBDRnGDrKpobj_G2xRAHI6Z-88",
            "AIzaSyCwKaFunKbgO5FE8uEgqxbWgab8LcwBzvc",
            "AIzaSyBiMajBlgp6rC6tTvkAUa6s34EG4VeYZxk",
            "AIzaSyBSbT3AFUoZ4TulCuSgx8QLWp4N2qGe4vg",
            "AIzaSyDskR5hdZtbk8JOo4XjqZaj0F0i5bsJUYg"
        ]

        reset_count = 0

        # إعادة تعيين المفاتيح الجديدة
        for key in new_keys:
            if key in self.keys_status:
                self.keys_status[key] = {
                    "blocked": False,
                    "last_used": 0,
                    "requests_today": 0,
                    "is_new": True
                }
                reset_count += 1
                logger.info(f"تم إعادة تعيين المفتاح الجديد: {key[-10:]}")

        if reset_count > 0:
            self._save_keys_status()
            self.current_key = self._get_next_available_key()
            logger.info(f"تم إعادة تعيين {reset_count} مفتاح جديد وإعطاؤها أولوية")

        return reset_count

    def get_new_keys_status(self):
        """الحصول على حالة المفاتيح الجديدة"""
        new_keys = [
            "AIzaSyC7Z3wBae-3J47R-LaPGnJGTrC22SvDg5M",
            "AIzaSyCQJ2CwE6_oSsBvzUs3TnaiHccXkjtgcWc",
            "AIzaSyBomvlphVTvUleoxzbJM8boL1KvkPuUZVc",
            "AIzaSyBlHvj5vAYWFD_Qs2RPlu5WBUIoPi4WuYQ",
            "AIzaSyArno9YCJbb7WfUG5llZgve644RIVRrntw",
            "AIzaSyBNR0YlKBy7UHf_9Gwd7hUtax41bqwAqX4",
            "AIzaSyA52Gg_lI3DN8sv0HykxPhLsmEYpCj6qlU",
            "AIzaSyBn2jVshv_jn60Yi_-UUmw9P31mDIs4w74",
            "AIzaSyAYgcYKr8ROUQjZdZwV9mn7K41gOtcsu1c",
            "AIzaSyDry4JMnQJwyGncKF9va6OC0LAX8b48WHs",
            "AIzaSyDFFCW_iKzlKrxEe4QARSzCL2nw0qsSrh8",
            "AIzaSyD8HVd2wuBDRnGDrKpobj_G2xRAHI6Z-88",
            "AIzaSyCwKaFunKbgO5FE8uEgqxbWgab8LcwBzvc",
            "AIzaSyBiMajBlgp6rC6tTvkAUa6s34EG4VeYZxk",
            "AIzaSyBSbT3AFUoZ4TulCuSgx8QLWp4N2qGe4vg",
            "AIzaSyDskR5hdZtbk8JOo4XjqZaj0F0i5bsJUYg"
        ]

        total_new = len(new_keys)
        available_new = 0
        blocked_new = 0

        for key in new_keys:
            key_status = self.keys_status.get(key, {})
            if not key_status.get("blocked", False):
                available_new += 1
            else:
                blocked_new += 1

        return {
            "total_new_keys": total_new,
            "available_new_keys": available_new,
            "blocked_new_keys": blocked_new
        }
    
    def add_new_key(self, new_key: str):
        """إضافة مفتاح جديد"""
        if new_key not in self.api_keys:
            self.api_keys.append(new_key)
            self.keys_status[new_key] = {"blocked": False, "last_used": 0, "requests_today": 0}
            self._save_keys_status()
            logger.info(f"تم إضافة مفتاح جديد: {new_key[-10:]}")
    
    def remove_key(self, key_to_remove: str):
        """إزالة مفتاح"""
        if key_to_remove in self.api_keys:
            self.api_keys.remove(key_to_remove)
            if key_to_remove in self.keys_status:
                del self.keys_status[key_to_remove]
            self._save_keys_status()
            
            # إذا كان المفتاح المحذوف هو المفتاح الحالي
            if self.current_key == key_to_remove:
                self.current_key = self._get_next_available_key()
            
            logger.info(f"تم حذف المفتاح: {key_to_remove[-10:]}")

# إنشاء مثيل عام لمدير المفاتيح
gemini_key_manager = GeminiKeyManager()
