"""
محلل احتياطي عندما تكون خدمات AI غير متاحة
Fallback analyzer when AI services are unavailable
"""

import logging
import numpy as np
import cv2
import os
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import json
import random

logger = logging.getLogger(__name__)

class FallbackAnalyzer:
    """محلل احتياطي يعتمد على التحليل البصري والصوتي البسيط"""
    
    def __init__(self):
        """تهيئة المحلل الاحتياطي"""
        
        # أنماط الملفات الشائعة
        self.filename_patterns = {
            'speed': {'type': 'gaming', 'excitement': 0.9},
            'ishowspeed': {'type': 'gaming', 'excitement': 0.9},
            'reaction': {'type': 'reaction', 'excitement': 0.8},
            'funny': {'type': 'comedy', 'excitement': 0.7},
            'challenge': {'type': 'challenge', 'excitement': 0.8},
            'gaming': {'type': 'gaming', 'excitement': 0.7},
            'live': {'type': 'live', 'excitement': 0.6},
            'highlight': {'type': 'compilation', 'excitement': 0.8},
            'compilation': {'type': 'compilation', 'excitement': 0.7},
            'best': {'type': 'compilation', 'excitement': 0.8},
            'moments': {'type': 'compilation', 'excitement': 0.7}
        }
        
        # قوالب المحتوى الافتراضية
        self.content_templates = {
            'gaming': {
                'events': ['introduction', 'setup', 'gameplay', 'climax', 'reaction'],
                'durations': [8, 12, 20, 15, 5],  # مدد افتراضية بالثواني
                'excitement_curve': [0.3, 0.4, 0.7, 0.9, 0.8]
            },
            'reaction': {
                'events': ['introduction', 'buildup', 'climax', 'reaction'],
                'durations': [10, 15, 20, 15],
                'excitement_curve': [0.3, 0.6, 0.9, 0.8]
            },
            'challenge': {
                'events': ['introduction', 'setup', 'attempt', 'climax', 'result'],
                'durations': [8, 10, 18, 12, 12],
                'excitement_curve': [0.4, 0.5, 0.7, 0.9, 0.7]
            },
            'comedy': {
                'events': ['setup', 'buildup', 'punchline', 'reaction'],
                'durations': [12, 18, 15, 15],
                'excitement_curve': [0.4, 0.6, 0.9, 0.8]
            },
            'compilation': {
                'events': ['moment1', 'moment2', 'moment3', 'moment4'],
                'durations': [15, 15, 15, 15],
                'excitement_curve': [0.8, 0.7, 0.9, 0.8]
            }
        }
        
        logger.info("تم تهيئة المحلل الاحتياطي")

    def analyze_video_fallback(self, video_path: str, target_clips: int = 3) -> List[Dict[str, Any]]:
        """تحليل الفيديو باستخدام الطرق الاحتياطية"""
        try:
            logger.info(f"بدء التحليل الاحتياطي: {video_path}")
            
            # الحصول على معلومات الفيديو
            video_info = self._get_video_info(video_path)
            if not video_info:
                return []
            
            # تحليل اسم الملف
            content_analysis = self._analyze_filename(video_path)
            
            # تحليل بصري بسيط
            visual_analysis = self._simple_visual_analysis(video_path, video_info['duration'])
            
            # دمج التحليلات
            combined_analysis = self._combine_analyses(content_analysis, visual_analysis, video_info)
            
            # إنشاء مقاطع الشورتس
            shorts = self._create_fallback_shorts(combined_analysis, target_clips, video_info['duration'])
            
            logger.info(f"تم إنشاء {len(shorts)} مقطع باستخدام التحليل الاحتياطي")
            return shorts
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الاحتياطي: {e}")
            return []

    def _get_video_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الفيديو الأساسية"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            duration = frame_count / fps if fps > 0 else 0
            
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            cap.release()
            
            return {
                'duration': duration,
                'fps': fps,
                'frame_count': frame_count,
                'width': width,
                'height': height,
                'file_size': os.path.getsize(video_path)
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الفيديو: {e}")
            return None

    def _analyze_filename(self, video_path: str) -> Dict[str, Any]:
        """تحليل اسم الملف لاستخراج معلومات"""
        try:
            filename = os.path.basename(video_path).lower()
            
            # البحث عن أنماط معروفة
            detected_type = 'general'
            excitement_level = 0.5
            
            for pattern, info in self.filename_patterns.items():
                if pattern in filename:
                    detected_type = info['type']
                    excitement_level = max(excitement_level, info['excitement'])
            
            # تحليل إضافي للكلمات المفتاحية
            keywords = []
            if 'speed' in filename or 'ishowspeed' in filename:
                keywords.extend(['speed', 'gaming', 'reaction', 'energy'])
            if 'funny' in filename or 'laugh' in filename:
                keywords.extend(['funny', 'comedy', 'humor'])
            if 'epic' in filename or 'insane' in filename:
                keywords.extend(['epic', 'amazing', 'incredible'])
            
            return {
                'content_type': detected_type,
                'excitement_level': excitement_level,
                'keywords': keywords,
                'filename': filename
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل اسم الملف: {e}")
            return {'content_type': 'general', 'excitement_level': 0.5, 'keywords': []}

    def _simple_visual_analysis(self, video_path: str, duration: float) -> Dict[str, Any]:
        """تحليل بصري بسيط للفيديو"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {}
            
            # أخذ عينات من الفيديو
            sample_count = min(10, int(duration))  # 10 عينات كحد أقصى
            frame_interval = int(cap.get(cv2.CAP_PROP_FRAME_COUNT) / sample_count) if sample_count > 0 else 1
            
            brightness_values = []
            motion_values = []
            prev_frame = None
            
            for i in range(sample_count):
                cap.set(cv2.CAP_PROP_POS_FRAMES, i * frame_interval)
                ret, frame = cap.read()
                
                if ret:
                    # حساب السطوع
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    brightness = np.mean(gray)
                    brightness_values.append(brightness)
                    
                    # حساب الحركة (إذا كان هناك إطار سابق)
                    if prev_frame is not None:
                        diff = cv2.absdiff(prev_frame, gray)
                        motion = np.mean(diff)
                        motion_values.append(motion)
                    
                    prev_frame = gray
            
            cap.release()
            
            # تحليل النتائج
            avg_brightness = np.mean(brightness_values) if brightness_values else 128
            avg_motion = np.mean(motion_values) if motion_values else 0
            
            # تحديد مستوى الإثارة بناءً على الحركة
            motion_excitement = min(avg_motion / 50.0, 1.0)  # تطبيع الحركة
            
            return {
                'avg_brightness': avg_brightness,
                'avg_motion': avg_motion,
                'motion_excitement': motion_excitement,
                'sample_count': len(brightness_values)
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل البصري: {e}")
            return {}

    def _combine_analyses(self, content_analysis: Dict, visual_analysis: Dict, video_info: Dict) -> Dict[str, Any]:
        """دمج نتائج التحليلات المختلفة"""
        try:
            # دمج مستويات الإثارة
            content_excitement = content_analysis.get('excitement_level', 0.5)
            visual_excitement = visual_analysis.get('motion_excitement', 0.5)
            
            # متوسط مرجح
            combined_excitement = (content_excitement * 0.7 + visual_excitement * 0.3)
            
            # تحديد نوع المحتوى النهائي
            content_type = content_analysis.get('content_type', 'general')
            
            # إضافة معلومات إضافية
            return {
                'content_type': content_type,
                'excitement_level': combined_excitement,
                'keywords': content_analysis.get('keywords', []),
                'duration': video_info.get('duration', 0),
                'visual_motion': visual_analysis.get('avg_motion', 0),
                'file_analysis': content_analysis,
                'visual_analysis': visual_analysis,
                'video_info': video_info
            }
            
        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {e}")
            return {'content_type': 'general', 'excitement_level': 0.5}

    def _create_fallback_shorts(self, analysis: Dict, target_clips: int, total_duration: float) -> List[Dict[str, Any]]:
        """إنشاء مقاطع شورتس بناءً على التحليل الاحتياطي"""
        try:
            content_type = analysis.get('content_type', 'general')
            excitement_level = analysis.get('excitement_level', 0.5)
            
            # الحصول على القالب المناسب
            template = self.content_templates.get(content_type, self.content_templates['gaming'])
            
            shorts = []
            max_short_duration = 60.0  # 60 ثانية كحد أقصى
            
            # إنشاء مقاطع بناءً على القالب
            for i in range(target_clips):
                # تحديد موقع المقطع في الفيديو
                if target_clips == 1:
                    start_ratio = 0.2  # البدء من 20% من الفيديو
                else:
                    start_ratio = i / (target_clips - 1) * 0.6 + 0.2  # توزيع بين 20% و 80%
                
                start_time = total_duration * start_ratio
                
                # تحديد مدة المقطع
                template_duration = sum(template['durations'])
                actual_duration = min(template_duration, max_short_duration)
                
                # التأكد من عدم تجاوز نهاية الفيديو
                end_time = min(start_time + actual_duration, total_duration)
                actual_duration = end_time - start_time
                
                if actual_duration < 10:  # تجاهل المقاطع القصيرة جداً
                    continue
                
                # إنشاء معلومات المقطع
                short_info = {
                    'type': f"{content_type}_fallback",
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': actual_duration,
                    'confidence': 0.6,  # ثقة متوسطة للتحليل الاحتياطي
                    'viral_potential': excitement_level,
                    'title': self._generate_fallback_title(content_type, i + 1),
                    'description': self._generate_fallback_description(content_type, analysis),
                    'hashtags': self._generate_fallback_hashtags(content_type, analysis),
                    'is_fallback': True,
                    'analysis_method': 'fallback_analyzer',
                    'template_used': content_type
                }
                
                shorts.append(short_info)
            
            return shorts
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء مقاطع الشورتس الاحتياطية: {e}")
            return []

    def _generate_fallback_title(self, content_type: str, index: int) -> str:
        """توليد عنوان احتياطي"""
        titles = {
            'gaming': [
                f"أفضل لحظات الألعاب #{index} 🎮",
                f"لحظات ملحمية من الألعاب #{index} 🔥",
                f"ردود أفعال مجنونة #{index} 😱"
            ],
            'reaction': [
                f"ردة فعل مذهلة #{index} 😮",
                f"لحظة صدمة #{index} 🤯",
                f"ردة فعل لا تصدق #{index} 😱"
            ],
            'challenge': [
                f"تحدي مجنون #{index} 💪",
                f"محاولة ملحمية #{index} 🚀",
                f"تحدي لا يصدق #{index} 🔥"
            ],
            'comedy': [
                f"لحظات مضحكة #{index} 😂",
                f"كوميديا خالصة #{index} 🤣",
                f"ضحك بلا حدود #{index} 😆"
            ]
        }
        
        type_titles = titles.get(content_type, titles['gaming'])
        return random.choice(type_titles)

    def _generate_fallback_description(self, content_type: str, analysis: Dict) -> str:
        """توليد وصف احتياطي"""
        keywords = analysis.get('keywords', [])
        excitement = analysis.get('excitement_level', 0.5)
        
        base_desc = f"مقطع رائع من نوع {content_type}"
        
        if keywords:
            base_desc += f" يحتوي على: {', '.join(keywords[:3])}"
        
        if excitement > 0.7:
            base_desc += " - مستوى إثارة عالي! 🔥"
        elif excitement > 0.5:
            base_desc += " - مستوى إثارة جيد ⚡"
        
        base_desc += "\n\n📝 تم إنشاؤه بواسطة التحليل الاحتياطي"
        
        return base_desc

    def _generate_fallback_hashtags(self, content_type: str, analysis: Dict) -> List[str]:
        """توليد هاشتاغات احتياطية"""
        base_tags = ["#shorts", "#viral", "#fallback"]
        
        type_tags = {
            'gaming': ["#gaming", "#games", "#gamer"],
            'reaction': ["#reaction", "#react", "#surprise"],
            'challenge': ["#challenge", "#attempt", "#try"],
            'comedy': ["#funny", "#laugh", "#comedy"],
            'compilation': ["#compilation", "#best", "#highlights"]
        }
        
        base_tags.extend(type_tags.get(content_type, []))
        
        # إضافة هاشتاغات من الكلمات المفتاحية
        keywords = analysis.get('keywords', [])
        for keyword in keywords[:3]:
            base_tags.append(f"#{keyword}")
        
        return base_tags[:10]  # حد أقصى 10 هاشتاغات
