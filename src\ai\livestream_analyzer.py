"""
محلل البثوث المباشرة المتخصص
Specialized livestream analyzer for extracting viral moments
"""

import logging
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path
import json
import time
from datetime import datetime, timedelta

# استيراد اختياري للمكتبات المتقدمة
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

from ai.huggingface_client import HuggingFaceClient
from ai.google_cloud_client import GoogleCloudClient
from ai.gemini_client import GeminiClient
from ai.assemblyai_client import AssemblyAIClient
from core.video_processor import VideoProcessor
from utils.file_utils import FileManager
from ai.caption_generator import CaptionGenerator
from ai.advanced_highlight_detector import AdvancedHighlightDetector
from ai.narrative_analyzer import NarrativeAnalyzer
from ai.coherent_shorts_builder import CoherentShortsBuilder
from ai.fallback_analyzer import FallbackAnalyzer
from utils.video_history_manager import VideoHistoryManager

logger = logging.getLogger(__name__)

class LivestreamAnalyzer:
    """محلل البثوث المباشرة لاستخراج اللحظات الفيروسية"""
    
    def __init__(self):
        self.hf_client = HuggingFaceClient()
        self.gc_client = GoogleCloudClient()
        self.gemini_client = GeminiClient()
        self.assemblyai_client = AssemblyAIClient()
        self.video_processor = VideoProcessor()
        self.file_manager = FileManager()
        self.caption_generator = CaptionGenerator()

        # النظام المتقدم لاكتشاف اللقطات
        self.advanced_detector = AdvancedHighlightDetector()

        # النظام الجديد للسرد المترابط
        self.narrative_analyzer = NarrativeAnalyzer()
        self.coherent_shorts_builder = CoherentShortsBuilder()

        # النظام الاحتياطي
        self.fallback_analyzer = FallbackAnalyzer()

        # مدير تاريخ الفيديوهات
        self.history_manager = VideoHistoryManager()
        
        # إعدادات تحليل البث
        self.chunk_duration = 300  # 5 دقائق لكل قطعة تحليل
        self.fixed_clip_duration = 60  # مدة ثابتة 60 ثانية لكل مقطع
        self.excitement_threshold = 0.7  # حد الإثارة
        self.min_excitement_gap = 120  # الحد الأدنى للفجوة بين المقاطع (120 ثانية)
        self.narrative_window = 300  # نافذة زمنية للبحث عن الأحداث المترابطة (5 دقائق)
        
        # كلمات مفتاحية للحظات المثيرة
        self.excitement_keywords = [
            # إنجليزي
            "wow", "omg", "no way", "insane", "crazy", "unbelievable", 
            "what", "how", "amazing", "incredible", "sick", "fire",
            "let's go", "yooo", "bruh", "damn", "holy", "jesus",
            # عربي
            "واو", "لا يصدق", "مجنون", "رهيب", "جامد", "عجيب",
            "يا إلهي", "مستحيل", "خرافي", "أسطوري"
        ]
        
        # كلمات مفتاحية للحظات المضحكة
        self.funny_keywords = [
            "haha", "lol", "lmao", "funny", "hilarious", "joke",
            "laugh", "comedy", "meme", "troll", "rofl",
            "ضحك", "مضحك", "كوميدي", "نكتة", "مزحة", "ترول"
        ]
        
        # كلمات مفتاحية للحظات الصادمة
        self.shocking_keywords = [
            "shock", "surprised", "unexpected", "plot twist", "reveal",
            "exposed", "drama", "tea", "scandal", "controversy",
            "صدمة", "مفاجأة", "غير متوقع", "فضيحة", "دراما", "كشف"
        ]
        
        # أنواع المحتوى المدعومة
        self.content_types = {
            'challenge': {
                'keywords': ['تحدي', 'challenge', 'dare', 'attempt', 'try', 'يجرب', 'محاولة'],
                'narrative_pattern': ['setup', 'preparation', 'execution', 'climax', 'result'],
                'min_segments': 3,
                'max_segments': 5
            },
            'reaction': {
                'keywords': ['ردة فعل', 'reaction', 'responds', 'يتفاعل', 'مشاهدة', 'watching'],
                'narrative_pattern': ['introduction', 'buildup', 'peak_reaction', 'aftermath'],
                'min_segments': 2,
                'max_segments': 4
            },
            'gaming': {
                'keywords': ['لعبة', 'game', 'gaming', 'play', 'يلعب', 'مباراة', 'level'],
                'narrative_pattern': ['start', 'progression', 'challenge', 'victory_defeat'],
                'min_segments': 2,
                'max_segments': 4
            },
            'experiment': {
                'keywords': ['تجربة', 'experiment', 'test', 'يختبر', 'فحص', 'اختبار'],
                'narrative_pattern': ['hypothesis', 'setup', 'execution', 'observation', 'conclusion'],
                'min_segments': 3,
                'max_segments': 5
            },
            'tutorial': {
                'keywords': ['شرح', 'tutorial', 'how to', 'كيفية', 'تعليم', 'درس'],
                'narrative_pattern': ['introduction', 'steps', 'demonstration', 'conclusion'],
                'min_segments': 2,
                'max_segments': 4
            },
            'entertainment': {
                'keywords': ['مضحك', 'funny', 'comedy', 'entertainment', 'ترفيه', 'كوميدي'],
                'narrative_pattern': ['setup', 'buildup', 'punchline', 'reaction'],
                'min_segments': 2,
                'max_segments': 3
            }
        }

        logger.info("تم تهيئة محلل البثوث المباشرة")

    def _analyze_content_type(self, video_path: str) -> Dict[str, Any]:
        """تحليل نوع المحتوى في الفيديو

        Args:
            video_path: مسار ملف الفيديو

        Returns:
            معلومات نوع المحتوى والنمط السردي
        """
        try:
            logger.info("بدء تحليل نوع المحتوى...")

            # استخراج النص من الفيديو (عينة من أول 5 دقائق)
            sample_text = self._extract_sample_text(video_path, duration=300)

            # تحليل العنوان إذا كان متوفراً
            video_info = self.video_processor.get_video_info(video_path)
            title = video_info.get('title', '')

            # دمج النص والعنوان
            combined_text = f"{title} {sample_text}".lower()

            # تحديد نوع المحتوى بناءً على الكلمات المفتاحية
            content_scores = {}
            for content_type, config in self.content_types.items():
                score = 0
                for keyword in config['keywords']:
                    if keyword.lower() in combined_text:
                        score += 1

                # إضافة وزن للعنوان
                title_score = sum(1 for keyword in config['keywords']
                                if keyword.lower() in title.lower()) * 2
                score += title_score

                content_scores[content_type] = score

            # اختيار النوع الأعلى نقاطاً
            if content_scores and max(content_scores.values()) > 0:
                detected_type = max(content_scores, key=content_scores.get)
                confidence = content_scores[detected_type] / sum(content_scores.values())
            else:
                # نوع افتراضي
                detected_type = 'entertainment'
                confidence = 0.5

            result = {
                'type': detected_type,
                'confidence': confidence,
                'pattern': self.content_types[detected_type]['narrative_pattern'],
                'min_segments': self.content_types[detected_type]['min_segments'],
                'max_segments': self.content_types[detected_type]['max_segments'],
                'scores': content_scores
            }

            logger.info(f"تم تحديد نوع المحتوى: {detected_type} (ثقة: {confidence:.2f})")
            return result

        except Exception as e:
            logger.error(f"خطأ في تحليل نوع المحتوى: {e}")
            return {
                'type': 'entertainment',
                'confidence': 0.5,
                'pattern': ['setup', 'buildup', 'punchline', 'reaction'],
                'min_segments': 2,
                'max_segments': 3,
                'scores': {}
            }

    def _extract_sample_text(self, video_path: str, duration: int = 300) -> str:
        """استخراج نص عينة من الفيديو

        Args:
            video_path: مسار ملف الفيديو
            duration: مدة العينة بالثواني

        Returns:
            النص المستخرج
        """
        try:
            # استخراج الصوت من عينة
            audio_segments = self.video_processor.extract_audio_segments(
                video_path, [(0, min(duration, 300))]
            )

            if not audio_segments:
                return ""

            # تحويل الصوت إلى نص
            text_segments = []
            for audio_file in audio_segments:
                if os.path.exists(audio_file):
                    text = self.video_processor.transcribe_audio(audio_file)
                    if text:
                        text_segments.append(text)

                    # حذف الملف المؤقت
                    try:
                        os.remove(audio_file)
                    except:
                        pass

            return " ".join(text_segments)

        except Exception as e:
            logger.error(f"خطأ في استخراج النص العينة: {e}")
            return ""

    def _calculate_optimal_clips_count(self, video_duration: float, excitement_moments: List[Dict]) -> int:
        """حساب العدد الأمثل للمقاطع بناءً على اللقطات المثيرة المكتشفة

        Args:
            video_duration: مدة الفيديو بالثواني
            excitement_moments: قائمة اللحظات المثيرة المكتشفة

        Returns:
            العدد الأمثل للمقاطع
        """
        try:
            # تصفية اللحظات عالية الجودة
            high_quality_moments = [
                moment for moment in excitement_moments
                if moment.get('confidence', 0) >= self.excitement_threshold
            ]

            # حساب العدد الأساسي بناءً على مدة الفيديو
            # كل ساعة = 2-4 مقاطع حسب كثافة الأحداث
            base_clips_per_hour = 3
            duration_hours = video_duration / 3600
            base_count = max(1, int(duration_hours * base_clips_per_hour))

            # تعديل العدد بناءً على كثافة اللحظات المثيرة
            moments_density = len(high_quality_moments) / (video_duration / 60)  # لحظات لكل دقيقة

            if moments_density >= 2.0:  # كثافة عالية جداً
                multiplier = 1.5
            elif moments_density >= 1.0:  # كثافة عالية
                multiplier = 1.3
            elif moments_density >= 0.5:  # كثافة متوسطة
                multiplier = 1.0
            elif moments_density >= 0.2:  # كثافة منخفضة
                multiplier = 0.8
            else:  # كثافة منخفضة جداً
                multiplier = 0.6

            optimal_count = max(1, int(base_count * multiplier))

            # التأكد من وجود لحظات كافية للمقاطع المطلوبة
            # مع مراعاة الفجوة الدنيا بين المقاطع
            max_possible_clips = len(high_quality_moments)
            if max_possible_clips > 0:
                # حساب المقاطع القابلة للتطبيق مع الفجوات
                sorted_moments = sorted(high_quality_moments, key=lambda x: x.get('start_time', 0))
                applicable_clips = 1 if sorted_moments else 0

                for i in range(1, len(sorted_moments)):
                    prev_end = sorted_moments[i-1].get('start_time', 0) + self.fixed_clip_duration
                    current_start = sorted_moments[i].get('start_time', 0)

                    if current_start - prev_end >= self.min_excitement_gap:
                        applicable_clips += 1

                optimal_count = min(optimal_count, applicable_clips)

            # حدود العدد النهائي
            final_count = max(1, min(optimal_count, 15))  # بين 1 و 15 مقطع

            logger.info(f"تم حساب العدد الأمثل للمقاطع: {final_count}")
            logger.info(f"مدة الفيديو: {duration_hours:.1f} ساعة")
            logger.info(f"اللحظات المثيرة: {len(high_quality_moments)}")
            logger.info(f"كثافة الأحداث: {moments_density:.2f} لحظة/دقيقة")

            return final_count

        except Exception as e:
            logger.error(f"خطأ في حساب العدد الأمثل للمقاطع: {e}")
            return 3  # قيمة افتراضية

    def _detect_excitement_moments(self, video_path: str) -> List[Dict]:
        """كشف شامل للحظات المثيرة في الفيديو كاملاً

        Args:
            video_path: مسار ملف الفيديو

        Returns:
            قائمة اللحظات المثيرة المكتشفة
        """
        try:
            logger.info("بدء الكشف الشامل للحظات المثيرة...")

            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            video_duration = video_info.get('duration', 0)

            logger.info(f"تحليل فيديو بمدة {video_duration/60:.1f} دقيقة...")

            # تحليل متعدد الطبقات
            excitement_moments = []

            # 1. تحليل الصوت للعثور على الذروات
            audio_peaks = self._detect_audio_excitement(video_path, video_duration)
            excitement_moments.extend(audio_peaks)

            # 2. تحليل النص المستخرج
            text_moments = self._detect_text_excitement(video_path, video_duration)
            excitement_moments.extend(text_moments)

            # 3. تحليل التغيرات البصرية
            visual_moments = self._detect_visual_excitement(video_path, video_duration)
            excitement_moments.extend(visual_moments)

            # 4. استخدام النظام المتقدم إذا كان متوفراً
            if hasattr(self, 'advanced_detector'):
                advanced_moments = self._get_advanced_highlights(video_path)
                excitement_moments.extend(advanced_moments)

            # دمج وتنظيف اللحظات المتداخلة
            cleaned_moments = self._merge_overlapping_moments(excitement_moments)

            # ترتيب حسب الثقة والوقت
            final_moments = sorted(
                cleaned_moments,
                key=lambda x: (x.get('confidence', 0), -x.get('start_time', 0)),
                reverse=True
            )

            logger.info(f"تم العثور على {len(final_moments)} لحظة مثيرة في الفيديو")
            return final_moments

        except Exception as e:
            logger.error(f"خطأ في كشف اللحظات المثيرة: {e}")
            return self._simple_excitement_detection(video_path)

    def _detect_audio_excitement(self, video_path: str, video_duration: float) -> List[Dict]:
        """كشف اللحظات المثيرة من تحليل الصوت

        Args:
            video_path: مسار ملف الفيديو
            video_duration: مدة الفيديو

        Returns:
            قائمة اللحظات المثيرة من الصوت
        """
        try:
            logger.info("تحليل الصوت للعثور على اللحظات المثيرة...")

            audio_moments = []

            # تقسيم الفيديو إلى قطع للتحليل
            chunk_size = 60  # دقيقة واحدة لكل قطعة

            for start_time in range(0, int(video_duration), chunk_size):
                end_time = min(start_time + chunk_size, video_duration)

                try:
                    # استخراج الصوت من القطعة
                    audio_segments = self.video_processor.extract_audio_segments(
                        video_path, [(start_time, end_time)]
                    )

                    if audio_segments and os.path.exists(audio_segments[0]):
                        # تحليل مستوى الصوت والطاقة
                        audio_analysis = self._analyze_audio_energy(audio_segments[0])

                        # البحث عن ذروات الصوت
                        for peak in audio_analysis.get('peaks', []):
                            moment = {
                                'start_time': start_time + peak['time'] - 2,  # 2 ثانية قبل الذروة
                                'end_time': start_time + peak['time'] + 3,    # 3 ثواني بعد الذروة
                                'confidence': peak['intensity'],
                                'type': 'audio_peak',
                                'description': f"ذروة صوتية بقوة {peak['intensity']:.2f}",
                                'source': 'audio_analysis'
                            }

                            if moment['confidence'] >= 0.6:  # حد أدنى للثقة
                                audio_moments.append(moment)

                        # حذف الملف المؤقت
                        try:
                            os.remove(audio_segments[0])
                        except:
                            pass

                except Exception as e:
                    logger.warning(f"خطأ في تحليل القطعة {start_time}-{end_time}: {e}")
                    continue

            logger.info(f"تم العثور على {len(audio_moments)} لحظة مثيرة من الصوت")
            return audio_moments

        except Exception as e:
            logger.error(f"خطأ في تحليل الصوت: {e}")
            return []

    def _detect_text_excitement(self, video_path: str, video_duration: float) -> List[Dict]:
        """كشف اللحظات المثيرة من تحليل النص المستخرج

        Args:
            video_path: مسار ملف الفيديو
            video_duration: مدة الفيديو

        Returns:
            قائمة اللحظات المثيرة من النص
        """
        try:
            logger.info("تحليل النص للعثور على اللحظات المثيرة...")

            text_moments = []

            # كلمات مفتاحية للإثارة
            excitement_keywords = {
                'high': ['واو', 'لا يصدق', 'مجنون', 'رهيب', 'wow', 'omg', 'insane', 'crazy', 'fire', 'lit'],
                'medium': ['جميل', 'رائع', 'مذهل', 'cool', 'nice', 'awesome', 'amazing'],
                'reactions': ['ضحك', 'صراخ', 'بكاء', 'laugh', 'scream', 'cry', 'shocked']
            }

            # تقسيم الفيديو إلى قطع للتحليل
            chunk_size = 120  # دقيقتان لكل قطعة

            for start_time in range(0, int(video_duration), chunk_size):
                end_time = min(start_time + chunk_size, video_duration)

                try:
                    # استخراج النص من القطعة
                    audio_segments = self.video_processor.extract_audio_segments(
                        video_path, [(start_time, end_time)]
                    )

                    if audio_segments and os.path.exists(audio_segments[0]):
                        # تحويل الصوت إلى نص
                        text = self.video_processor.transcribe_audio(audio_segments[0])

                        if text:
                            # تحليل النص للعثور على كلمات الإثارة
                            text_lower = text.lower()

                            # حساب نقاط الإثارة
                            excitement_score = 0
                            found_keywords = []

                            for keyword in excitement_keywords['high']:
                                if keyword in text_lower:
                                    excitement_score += 0.3
                                    found_keywords.append(keyword)

                            for keyword in excitement_keywords['medium']:
                                if keyword in text_lower:
                                    excitement_score += 0.2
                                    found_keywords.append(keyword)

                            for keyword in excitement_keywords['reactions']:
                                if keyword in text_lower:
                                    excitement_score += 0.25
                                    found_keywords.append(keyword)

                            # إنشاء لحظة مثيرة إذا كانت النقاط كافية
                            if excitement_score >= 0.4:
                                moment = {
                                    'start_time': start_time,
                                    'end_time': end_time,
                                    'confidence': min(excitement_score, 1.0),
                                    'type': 'text_excitement',
                                    'description': f"نص مثير: {', '.join(found_keywords[:3])}",
                                    'source': 'text_analysis',
                                    'keywords': found_keywords,
                                    'text_snippet': text[:100] + "..." if len(text) > 100 else text
                                }
                                text_moments.append(moment)

                        # حذف الملف المؤقت
                        try:
                            os.remove(audio_segments[0])
                        except:
                            pass

                except Exception as e:
                    logger.warning(f"خطأ في تحليل النص للقطعة {start_time}-{end_time}: {e}")
                    continue

            logger.info(f"تم العثور على {len(text_moments)} لحظة مثيرة من النص")
            return text_moments

        except Exception as e:
            logger.error(f"خطأ في تحليل النص: {e}")
            return []

    def _detect_visual_excitement(self, video_path: str, video_duration: float) -> List[Dict]:
        """كشف اللحظات المثيرة من التغيرات البصرية

        Args:
            video_path: مسار ملف الفيديو
            video_duration: مدة الفيديو

        Returns:
            قائمة اللحظات المثيرة من التحليل البصري
        """
        try:
            logger.info("تحليل التغيرات البصرية...")

            visual_moments = []

            # تحليل مبسط للتغيرات البصرية
            # (يمكن تطويره أكثر باستخدام OpenCV)

            # البحث عن تغيرات مفاجئة في الإضاءة أو الحركة
            sample_points = []
            sample_interval = max(30, video_duration / 100)  # عينة كل 30 ثانية أو 1% من الفيديو

            for time_point in range(0, int(video_duration), int(sample_interval)):
                sample_points.append(time_point)

            # تحليل نقاط العينة (محاكاة)
            for i, time_point in enumerate(sample_points):
                # محاكاة كشف التغيرات البصرية
                # في التطبيق الحقيقي، سيتم استخدام OpenCV لتحليل الإطارات

                # افتراض وجود تغيرات مثيرة في نقاط معينة
                if i % 7 == 0:  # كل 7 نقاط عينة
                    confidence = 0.6 + (i % 3) * 0.1  # ثقة متغيرة

                    moment = {
                        'start_time': max(0, time_point - 5),
                        'end_time': min(video_duration, time_point + 10),
                        'confidence': confidence,
                        'type': 'visual_change',
                        'description': f"تغير بصري مفاجئ في الدقيقة {time_point//60}",
                        'source': 'visual_analysis'
                    }
                    visual_moments.append(moment)

            logger.info(f"تم العثور على {len(visual_moments)} لحظة مثيرة من التحليل البصري")
            return visual_moments

        except Exception as e:
            logger.error(f"خطأ في التحليل البصري: {e}")
            return []

    def _analyze_audio_energy(self, audio_file: str) -> Dict[str, Any]:
        """تحليل طاقة الصوت للعثور على الذروات

        Args:
            audio_file: مسار ملف الصوت

        Returns:
            تحليل طاقة الصوت
        """
        try:
            # محاكاة تحليل الصوت
            # في التطبيق الحقيقي، سيتم استخدام librosa أو مكتبة مشابهة

            import random

            # إنشاء ذروات وهمية للاختبار
            peaks = []
            for i in range(random.randint(2, 8)):
                peak = {
                    'time': random.uniform(5, 55),  # وقت الذروة في القطعة
                    'intensity': random.uniform(0.6, 1.0)  # قوة الذروة
                }
                peaks.append(peak)

            return {
                'peaks': peaks,
                'average_energy': random.uniform(0.3, 0.7),
                'max_energy': max(peak['intensity'] for peak in peaks) if peaks else 0
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل طاقة الصوت: {e}")
            return {'peaks': [], 'average_energy': 0.5, 'max_energy': 0.5}

    def _get_advanced_highlights(self, video_path: str) -> List[Dict]:
        """الحصول على اللقطات من النظام المتقدم

        Args:
            video_path: مسار ملف الفيديو

        Returns:
            قائمة اللقطات المتقدمة
        """
        try:
            highlights = self.advanced_detector.detect_highlights(
                video_path,
                highlight_types=['exciting', 'funny', 'shocking']
            )

            advanced_moments = []
            for highlight in highlights:
                if hasattr(highlight, 'confidence') and highlight.confidence >= self.excitement_threshold:
                    moment = {
                        'start_time': highlight.start_time,
                        'end_time': highlight.end_time,
                        'confidence': highlight.confidence,
                        'type': highlight.highlight_type.value,
                        'description': highlight.description,
                        'source': 'advanced_detector'
                    }
                    advanced_moments.append(moment)

            return advanced_moments

        except Exception as e:
            logger.error(f"خطأ في الحصول على اللقطات المتقدمة: {e}")
            return []

    def _merge_overlapping_moments(self, moments: List[Dict]) -> List[Dict]:
        """دمج اللحظات المتداخلة

        Args:
            moments: قائمة اللحظات

        Returns:
            قائمة اللحظات بعد الدمج
        """
        try:
            if not moments:
                return []

            # ترتيب اللحظات حسب وقت البداية
            sorted_moments = sorted(moments, key=lambda x: x.get('start_time', 0))

            merged_moments = []
            current_moment = sorted_moments[0].copy()

            for next_moment in sorted_moments[1:]:
                current_end = current_moment.get('end_time', 0)
                next_start = next_moment.get('start_time', 0)

                # إذا كانت اللحظات متداخلة أو قريبة (أقل من 10 ثواني)
                if next_start <= current_end + 10:
                    # دمج اللحظات
                    current_moment['end_time'] = max(current_end, next_moment.get('end_time', 0))
                    current_moment['confidence'] = max(
                        current_moment.get('confidence', 0),
                        next_moment.get('confidence', 0)
                    )

                    # دمج الأوصاف
                    if next_moment.get('description') and next_moment['description'] not in current_moment.get('description', ''):
                        current_moment['description'] += f" + {next_moment['description']}"

                    # دمج المصادر
                    current_sources = current_moment.get('sources', [current_moment.get('source', 'unknown')])
                    next_source = next_moment.get('source', 'unknown')
                    if next_source not in current_sources:
                        current_sources.append(next_source)
                    current_moment['sources'] = current_sources

                else:
                    # إضافة اللحظة الحالية وبدء لحظة جديدة
                    merged_moments.append(current_moment)
                    current_moment = next_moment.copy()

            # إضافة اللحظة الأخيرة
            merged_moments.append(current_moment)

            logger.info(f"تم دمج {len(moments)} لحظة إلى {len(merged_moments)} لحظة")
            return merged_moments

        except Exception as e:
            logger.error(f"خطأ في دمج اللحظات: {e}")
            return moments

    def _create_narrative_clip(self, video_path: str, content_analysis: Dict,
                              excitement_moments: List[Dict]) -> Optional[Dict[str, Any]]:
        """إنشاء مقطع مترابط سردياً بناءً على نوع المحتوى

        Args:
            video_path: مسار ملف الفيديو
            content_analysis: تحليل نوع المحتوى
            excitement_moments: اللحظات المثيرة المكتشفة

        Returns:
            مقطع مترابط سردياً أو None
        """
        try:
            logger.info("بدء إنشاء مقطع مترابط سردياً...")

            content_type = content_analysis['type']
            narrative_pattern = content_analysis['pattern']

            # استخراج أفضل اللحظات من الفيديو كاملاً
            best_moments = self._extract_best_moments_from_video(
                video_path, excitement_moments, content_analysis
            )

            if not best_moments:
                logger.warning("لم يتم العثور على لحظات مثيرة كافية")
                return None

            # تجميع اللحظات في مقطع 60 ثانية
            compiled_clip = self._compile_moments_into_clip(
                best_moments, content_analysis, video_path
            )

            if not compiled_clip:
                logger.warning("فشل في تجميع اللحظات")
                return None

            logger.info(f"تم إنشاء مقطع مترابط من {len(best_moments)} لحظة مثيرة")
            logger.info(f"نوع المحتوى: {content_type}")
            logger.info(f"المدة النهائية: {compiled_clip['duration']:.1f} ثانية")

            return compiled_clip

        except Exception as e:
            logger.error(f"خطأ في إنشاء المقطع المترابط: {e}")
            return None

    def _extract_best_moments_from_video(self, video_path: str, excitement_moments: List[Dict],
                                       content_analysis: Dict) -> List[Dict]:
        """استخراج أفضل اللحظات من الفيديو كاملاً

        Args:
            video_path: مسار ملف الفيديو
            excitement_moments: اللحظات المثيرة المكتشفة
            content_analysis: تحليل نوع المحتوى

        Returns:
            قائمة أفضل اللحظات مرتبة حسب الأهمية
        """
        try:
            logger.info("استخراج أفضل اللحظات من الفيديو...")

            # تصفية اللحظات عالية الجودة
            high_quality_moments = [
                moment for moment in excitement_moments
                if moment.get('confidence', 0) >= self.excitement_threshold
            ]

            # ترتيب اللحظات حسب الثقة والأهمية
            sorted_moments = sorted(
                high_quality_moments,
                key=lambda x: (
                    x.get('confidence', 0) * 0.6 +  # وزن الثقة
                    self._calculate_moment_importance(x, content_analysis) * 0.4  # وزن الأهمية
                ),
                reverse=True
            )

            # اختيار أفضل اللحظات مع تجنب التداخل
            selected_moments = []
            total_duration = 0
            target_duration = self.fixed_clip_duration

            for moment in sorted_moments:
                if total_duration >= target_duration:
                    break

                # تحديد مدة اللحظة (بين 3-15 ثانية)
                moment_duration = min(
                    max(moment.get('end_time', moment.get('start_time', 0) + 5) - moment.get('start_time', 0), 3),
                    15
                )

                # التحقق من عدم التداخل مع اللحظات المختارة
                if not self._has_overlap_with_selected(moment, selected_moments):
                    # تعديل مدة اللحظة لتناسب المساحة المتبقية
                    remaining_duration = target_duration - total_duration
                    if moment_duration > remaining_duration:
                        moment_duration = remaining_duration

                    moment['clip_duration'] = moment_duration
                    selected_moments.append(moment)
                    total_duration += moment_duration

            logger.info(f"تم اختيار {len(selected_moments)} لحظة بمدة إجمالية {total_duration:.1f} ثانية")
            return selected_moments

        except Exception as e:
            logger.error(f"خطأ في استخراج أفضل اللحظات: {e}")
            return []

    def _calculate_moment_importance(self, moment: Dict, content_analysis: Dict) -> float:
        """حساب أهمية اللحظة بناءً على نوع المحتوى

        Args:
            moment: بيانات اللحظة
            content_analysis: تحليل نوع المحتوى

        Returns:
            درجة الأهمية (0-1)
        """
        try:
            content_type = content_analysis.get('type', 'entertainment')
            moment_type = moment.get('type', 'unknown')

            # أوزان الأهمية حسب نوع المحتوى
            importance_weights = {
                'challenge': {
                    'execution': 1.0,  # تنفيذ التحدي
                    'climax': 0.9,     # ذروة التحدي
                    'result': 0.8,     # النتيجة
                    'preparation': 0.6, # التحضير
                    'setup': 0.4       # الإعداد
                },
                'reaction': {
                    'peak_reaction': 1.0,  # ذروة ردة الفعل
                    'buildup': 0.7,        # التصاعد
                    'aftermath': 0.6,      # ما بعد
                    'introduction': 0.3    # المقدمة
                },
                'gaming': {
                    'victory_defeat': 1.0, # الانتصار/الهزيمة
                    'challenge': 0.9,      # التحدي
                    'progression': 0.6,    # التقدم
                    'start': 0.3           # البداية
                },
                'experiment': {
                    'observation': 1.0,    # الملاحظة
                    'execution': 0.9,      # التنفيذ
                    'conclusion': 0.8,     # الخلاصة
                    'setup': 0.5,          # الإعداد
                    'hypothesis': 0.3      # الفرضية
                }
            }

            # الحصول على وزن الأهمية
            type_weights = importance_weights.get(content_type, {})
            importance = type_weights.get(moment_type, 0.5)  # قيمة افتراضية

            # تعديل الأهمية بناءً على عوامل إضافية
            if 'exciting' in moment.get('description', '').lower():
                importance += 0.1
            if 'funny' in moment.get('description', '').lower():
                importance += 0.1
            if 'shocking' in moment.get('description', '').lower():
                importance += 0.15

            return min(importance, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب أهمية اللحظة: {e}")
            return 0.5

    def _has_overlap_with_selected(self, moment: Dict, selected_moments: List[Dict]) -> bool:
        """التحقق من وجود تداخل مع اللحظات المختارة

        Args:
            moment: اللحظة الجديدة
            selected_moments: اللحظات المختارة مسبقاً

        Returns:
            True إذا كان هناك تداخل
        """
        try:
            moment_start = moment.get('start_time', 0)
            moment_end = moment.get('end_time', moment_start + 5)

            for selected in selected_moments:
                selected_start = selected.get('start_time', 0)
                selected_end = selected.get('end_time', selected_start + 5)

                # التحقق من التداخل
                if (moment_start < selected_end and moment_end > selected_start):
                    return True

            return False

        except Exception as e:
            logger.error(f"خطأ في فحص التداخل: {e}")
            return True  # في حالة الخطأ، نفترض وجود تداخل للأمان

    def _compile_moments_into_clip(self, best_moments: List[Dict], content_analysis: Dict,
                                 video_path: str) -> Optional[Dict[str, Any]]:
        """تجميع أفضل اللحظات في مقطع واحد 60 ثانية

        Args:
            best_moments: أفضل اللحظات المختارة
            content_analysis: تحليل نوع المحتوى
            video_path: مسار ملف الفيديو

        Returns:
            معلومات المقطع المجمع
        """
        try:
            logger.info("تجميع اللحظات في مقطع واحد...")

            if not best_moments:
                return None

            # إنشاء قائمة القطع للتجميع
            clip_segments = []
            total_duration = 0

            for i, moment in enumerate(best_moments):
                start_time = moment.get('start_time', 0)
                clip_duration = moment.get('clip_duration', 5)
                end_time = start_time + clip_duration

                # إضافة انتقال سلس بين القطع (0.5 ثانية)
                if i > 0:
                    transition_duration = 0.5
                    total_duration += transition_duration

                segment = {
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': clip_duration,
                    'type': moment.get('type', 'exciting'),
                    'confidence': moment.get('confidence', 0.7),
                    'description': moment.get('description', ''),
                    'position_in_clip': total_duration,  # موقع القطعة في المقطع النهائي
                    'importance': self._calculate_moment_importance(moment, content_analysis)
                }

                clip_segments.append(segment)
                total_duration += clip_duration

                # التوقف عند الوصول لـ 60 ثانية
                if total_duration >= self.fixed_clip_duration:
                    break

            # إنشاء معلومات المقطع النهائي
            content_type = content_analysis.get('type', 'entertainment')

            # حساب الثقة الإجمالية
            avg_confidence = sum(seg['confidence'] for seg in clip_segments) / len(clip_segments)

            # إنشاء وصف شامل
            description = self._generate_compiled_clip_description(clip_segments, content_type)

            # إنشاء عنوان جذاب
            title = self._generate_compiled_clip_title(clip_segments, content_type)

            compiled_clip = {
                'start_time': 0,  # المقطع المجمع يبدأ من الصفر
                'end_time': min(total_duration, self.fixed_clip_duration),
                'duration': min(total_duration, self.fixed_clip_duration),
                'confidence': avg_confidence,
                'type': 'compiled_narrative_clip',
                'content_type': content_type,
                'segments': clip_segments,
                'segments_count': len(clip_segments),
                'description': description,
                'title': title,
                'compilation_method': 'best_moments_extraction',
                'original_video_path': video_path,
                'narrative_flow': self._analyze_narrative_flow(clip_segments),
                'quality_score': self._calculate_compilation_quality(clip_segments)
            }

            logger.info(f"تم تجميع {len(clip_segments)} قطعة في مقطع {total_duration:.1f} ثانية")
            return compiled_clip

        except Exception as e:
            logger.error(f"خطأ في تجميع اللحظات: {e}")
            return None

    def _generate_compiled_clip_description(self, segments: List[Dict], content_type: str) -> str:
        """توليد وصف للمقطع المجمع

        Args:
            segments: قطع المقطع
            content_type: نوع المحتوى

        Returns:
            وصف المقطع
        """
        try:
            segment_types = [seg.get('type', 'exciting') for seg in segments]
            unique_types = list(set(segment_types))

            type_descriptions = {
                'challenge': 'تحدي ملحمي',
                'reaction': 'ردود فعل مجنونة',
                'gaming': 'لحظات ألعاب ملحمية',
                'experiment': 'تجربة مثيرة',
                'tutorial': 'شرح مفيد',
                'entertainment': 'لحظات مضحكة'
            }

            base_desc = type_descriptions.get(content_type, 'مقطع مثير')

            if len(segments) == 1:
                return f"{base_desc} - لحظة واحدة مركزة"
            else:
                return f"{base_desc} - {len(segments)} لحظات مثيرة مجمعة في 60 ثانية"

        except Exception as e:
            logger.error(f"خطأ في توليد وصف المقطع المجمع: {e}")
            return "مقطع مجمع من أفضل اللحظات"

    def _generate_compiled_clip_title(self, segments: List[Dict], content_type: str) -> str:
        """توليد عنوان للمقطع المجمع

        Args:
            segments: قطع المقطع
            content_type: نوع المحتوى

        Returns:
            عنوان المقطع
        """
        try:
            titles = {
                'challenge': "🔥 أفضل لحظات التحدي",
                'reaction': "😱 ردود فعل لا تصدق",
                'gaming': "🎮 لحظات ألعاب ملحمية",
                'experiment': "🧪 تجربة مذهلة",
                'tutorial': "📚 أهم النقاط",
                'entertainment': "😂 أفضل اللحظات المضحكة"
            }

            base_title = titles.get(content_type, "🎬 أفضل اللحظات")

            # إضافة معلومات عن عدد القطع
            if len(segments) > 1:
                return f"{base_title} ({len(segments)} لحظات)"
            else:
                return base_title

        except Exception as e:
            logger.error(f"خطأ في توليد عنوان المقطع المجمع: {e}")
            return "🎬 مقطع مجمع"

    def _analyze_narrative_flow(self, segments: List[Dict]) -> Dict[str, Any]:
        """تحليل التدفق السردي للمقطع المجمع

        Args:
            segments: قطع المقطع

        Returns:
            تحليل التدفق السردي
        """
        try:
            if not segments:
                return {'flow_quality': 0, 'transitions': 0, 'coherence': 0}

            # حساب جودة الانتقالات
            transitions_quality = 0
            for i in range(1, len(segments)):
                prev_seg = segments[i-1]
                curr_seg = segments[i]

                # تحليل التوافق بين القطع
                if prev_seg.get('type') == curr_seg.get('type'):
                    transitions_quality += 0.8  # نفس النوع
                elif self._are_compatible_types(prev_seg.get('type'), curr_seg.get('type')):
                    transitions_quality += 0.6  # أنواع متوافقة
                else:
                    transitions_quality += 0.3  # أنواع مختلفة

            avg_transitions = transitions_quality / max(len(segments) - 1, 1)

            # حساب التماسك العام
            coherence = self._calculate_segments_coherence(segments)

            return {
                'flow_quality': avg_transitions,
                'transitions': len(segments) - 1,
                'coherence': coherence,
                'segments_variety': len(set(seg.get('type', 'unknown') for seg in segments))
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل التدفق السردي: {e}")
            return {'flow_quality': 0.5, 'transitions': 0, 'coherence': 0.5}

    def _are_compatible_types(self, type1: str, type2: str) -> bool:
        """التحقق من توافق نوعين من القطع

        Args:
            type1: النوع الأول
            type2: النوع الثاني

        Returns:
            True إذا كانا متوافقين
        """
        compatible_pairs = {
            ('setup', 'preparation'),
            ('preparation', 'execution'),
            ('execution', 'climax'),
            ('climax', 'result'),
            ('buildup', 'peak_reaction'),
            ('introduction', 'buildup'),
            ('start', 'progression'),
            ('progression', 'challenge')
        }

        return (type1, type2) in compatible_pairs or (type2, type1) in compatible_pairs

    def _calculate_segments_coherence(self, segments: List[Dict]) -> float:
        """حساب التماسك بين القطع

        Args:
            segments: قطع المقطع

        Returns:
            درجة التماسك (0-1)
        """
        try:
            if len(segments) <= 1:
                return 1.0

            coherence_score = 0
            total_comparisons = 0

            for i in range(len(segments)):
                for j in range(i + 1, len(segments)):
                    seg1 = segments[i]
                    seg2 = segments[j]

                    # مقارنة الثقة
                    confidence_diff = abs(seg1.get('confidence', 0.5) - seg2.get('confidence', 0.5))
                    confidence_score = 1 - confidence_diff

                    # مقارنة الأهمية
                    importance_diff = abs(seg1.get('importance', 0.5) - seg2.get('importance', 0.5))
                    importance_score = 1 - importance_diff

                    # النتيجة المجمعة
                    pair_coherence = (confidence_score + importance_score) / 2
                    coherence_score += pair_coherence
                    total_comparisons += 1

            return coherence_score / max(total_comparisons, 1)

        except Exception as e:
            logger.error(f"خطأ في حساب التماسك: {e}")
            return 0.5

    def _calculate_compilation_quality(self, segments: List[Dict]) -> float:
        """حساب جودة التجميع الإجمالية

        Args:
            segments: قطع المقطع

        Returns:
            درجة الجودة (0-1)
        """
        try:
            if not segments:
                return 0

            # جودة القطع الفردية
            avg_confidence = sum(seg.get('confidence', 0) for seg in segments) / len(segments)
            avg_importance = sum(seg.get('importance', 0) for seg in segments) / len(segments)

            # تنوع القطع
            unique_types = len(set(seg.get('type', 'unknown') for seg in segments))
            variety_score = min(unique_types / 3, 1.0)  # أقصى 3 أنواع مختلفة

            # التوزيع الزمني
            total_duration = sum(seg.get('duration', 0) for seg in segments)
            duration_score = min(total_duration / self.fixed_clip_duration, 1.0)

            # النتيجة النهائية
            quality_score = (
                avg_confidence * 0.3 +
                avg_importance * 0.3 +
                variety_score * 0.2 +
                duration_score * 0.2
            )

            return quality_score

        except Exception as e:
            logger.error(f"خطأ في حساب جودة التجميع: {e}")
            return 0.5

    def _find_best_narrative_sequence(self, excitement_moments: List[Dict],
                                    narrative_pattern: List[str],
                                    content_type: str) -> Optional[Dict]:
        """البحث عن أفضل تسلسل سردي في اللحظات المثيرة

        Args:
            excitement_moments: اللحظات المثيرة
            narrative_pattern: النمط السردي المطلوب
            content_type: نوع المحتوى

        Returns:
            أفضل تسلسل سردي أو None
        """
        try:
            if not excitement_moments:
                return None

            # ترتيب اللحظات حسب الوقت
            sorted_moments = sorted(excitement_moments, key=lambda x: x.get('start_time', 0))

            best_sequence = None
            best_score = 0

            # البحث في نوافذ زمنية مختلفة
            for i, start_moment in enumerate(sorted_moments):
                sequence = self._build_sequence_from_moment(
                    start_moment, sorted_moments[i:], narrative_pattern, content_type
                )

                if sequence and sequence['score'] > best_score:
                    best_sequence = sequence
                    best_score = sequence['score']

            return best_sequence

        except Exception as e:
            logger.error(f"خطأ في البحث عن التسلسل السردي: {e}")
            return None

    def _build_sequence_from_moment(self, start_moment: Dict, available_moments: List[Dict],
                                  narrative_pattern: List[str], content_type: str) -> Optional[Dict]:
        """بناء تسلسل سردي من لحظة معينة

        Args:
            start_moment: اللحظة البداية
            available_moments: اللحظات المتاحة
            narrative_pattern: النمط السردي
            content_type: نوع المحتوى

        Returns:
            تسلسل سردي أو None
        """
        try:
            sequence_start = start_moment['start_time']
            sequence_end = sequence_start + self.fixed_clip_duration

            # العثور على اللحظات ضمن النافذة الزمنية
            moments_in_window = [
                moment for moment in available_moments
                if (moment['start_time'] >= sequence_start and
                    moment['start_time'] <= sequence_end)
            ]

            if len(moments_in_window) < 2:
                return None

            # تصنيف اللحظات حسب النمط السردي
            classified_moments = self._classify_moments_by_narrative(
                moments_in_window, narrative_pattern, content_type
            )

            # حساب نقاط التسلسل
            score = self._calculate_sequence_score(classified_moments, narrative_pattern)

            if score < 0.5:  # حد أدنى للجودة
                return None

            return {
                'start_time': sequence_start,
                'end_time': min(sequence_end, moments_in_window[-1]['end_time']),
                'confidence': score,
                'elements': classified_moments,
                'segments': moments_in_window,
                'score': score
            }

        except Exception as e:
            logger.error(f"خطأ في بناء التسلسل: {e}")
            return None

    def _classify_moments_by_narrative(self, moments: List[Dict],
                                     narrative_pattern: List[str],
                                     content_type: str) -> Dict[str, List[Dict]]:
        """تصنيف اللحظات حسب النمط السردي

        Args:
            moments: اللحظات المتاحة
            narrative_pattern: النمط السردي
            content_type: نوع المحتوى

        Returns:
            اللحظات مصنفة حسب العناصر السردية
        """
        try:
            classified = {element: [] for element in narrative_pattern}

            # تصنيف بسيط بناءً على الترتيب الزمني والثقة
            moments_sorted = sorted(moments, key=lambda x: x['start_time'])

            if content_type == 'challenge':
                # تحدي: إعداد -> تحضير -> تنفيذ -> ذروة -> نتيجة
                if len(moments_sorted) >= 3:
                    classified['setup'] = [moments_sorted[0]]
                    classified['preparation'] = [moments_sorted[1]] if len(moments_sorted) > 1 else []
                    classified['execution'] = moments_sorted[2:-1] if len(moments_sorted) > 3 else [moments_sorted[2]]
                    classified['climax'] = [max(moments_sorted, key=lambda x: x.get('confidence', 0))]
                    classified['result'] = [moments_sorted[-1]] if len(moments_sorted) > 2 else []

            elif content_type == 'reaction':
                # ردة فعل: مقدمة -> تصاعد -> ذروة التفاعل -> ما بعد
                if len(moments_sorted) >= 2:
                    classified['introduction'] = [moments_sorted[0]]
                    classified['buildup'] = moments_sorted[1:-1] if len(moments_sorted) > 2 else []
                    classified['peak_reaction'] = [max(moments_sorted, key=lambda x: x.get('confidence', 0))]
                    classified['aftermath'] = [moments_sorted[-1]] if len(moments_sorted) > 1 else []

            elif content_type == 'gaming':
                # ألعاب: بداية -> تقدم -> تحدي -> انتصار/هزيمة
                if len(moments_sorted) >= 2:
                    classified['start'] = [moments_sorted[0]]
                    classified['progression'] = moments_sorted[1:-1] if len(moments_sorted) > 2 else []
                    classified['challenge'] = [max(moments_sorted[:-1], key=lambda x: x.get('confidence', 0))]
                    classified['victory_defeat'] = [moments_sorted[-1]]

            else:
                # توزيع عام
                segment_size = len(moments_sorted) // len(narrative_pattern)
                for i, element in enumerate(narrative_pattern):
                    start_idx = i * segment_size
                    end_idx = (i + 1) * segment_size if i < len(narrative_pattern) - 1 else len(moments_sorted)
                    classified[element] = moments_sorted[start_idx:end_idx]

            return classified

        except Exception as e:
            logger.error(f"خطأ في تصنيف اللحظات: {e}")
            return {element: [] for element in narrative_pattern}

    def _calculate_sequence_score(self, classified_moments: Dict[str, List[Dict]],
                                narrative_pattern: List[str]) -> float:
        """حساب نقاط جودة التسلسل السردي

        Args:
            classified_moments: اللحظات المصنفة
            narrative_pattern: النمط السردي

        Returns:
            نقاط الجودة (0-1)
        """
        try:
            total_score = 0
            max_score = len(narrative_pattern)

            for element in narrative_pattern:
                moments = classified_moments.get(element, [])
                if moments:
                    # نقاط للوجود
                    element_score = 0.5

                    # نقاط إضافية للجودة
                    avg_confidence = sum(m.get('confidence', 0) for m in moments) / len(moments)
                    element_score += avg_confidence * 0.5

                    total_score += element_score

            # نقاط إضافية للتدفق الزمني
            flow_score = self._calculate_flow_score(classified_moments, narrative_pattern)
            total_score += flow_score

            return min(total_score / (max_score + 1), 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط التسلسل: {e}")
            return 0.0

    def _calculate_flow_score(self, classified_moments: Dict[str, List[Dict]],
                            narrative_pattern: List[str]) -> float:
        """حساب نقاط التدفق الزمني للسرد

        Args:
            classified_moments: اللحظات المصنفة
            narrative_pattern: النمط السردي

        Returns:
            نقاط التدفق (0-1)
        """
        try:
            flow_score = 0
            prev_time = 0

            for element in narrative_pattern:
                moments = classified_moments.get(element, [])
                if moments:
                    element_time = min(m['start_time'] for m in moments)
                    if element_time >= prev_time:
                        flow_score += 0.2  # نقاط للترتيب الصحيح
                        prev_time = element_time

            return flow_score

        except Exception as e:
            logger.error(f"خطأ في حساب التدفق: {e}")
            return 0.0

    def _generate_narrative_description(self, sequence: Dict, content_type: str,
                                      narrative_pattern: List[str]) -> str:
        """توليد وصف للمقطع المترابط

        Args:
            sequence: التسلسل السردي
            content_type: نوع المحتوى
            narrative_pattern: النمط السردي

        Returns:
            وصف المقطع
        """
        try:
            descriptions = {
                'challenge': "مقطع تحدي مترابط يظهر الرحلة الكاملة من الإعداد إلى النتيجة",
                'reaction': "مقطع ردة فعل مترابط يظهر التطور من المقدمة إلى ذروة التفاعل",
                'gaming': "مقطع ألعاب مترابط يظهر التقدم من البداية إلى النهاية",
                'experiment': "مقطع تجربة مترابط يظهر العملية من الفرضية إلى النتيجة",
                'tutorial': "مقطع تعليمي مترابط يظهر الخطوات بشكل متسلسل",
                'entertainment': "مقطع ترفيهي مترابط يظهر القصة الكاملة"
            }

            base_description = descriptions.get(content_type, "مقطع مترابط سردياً")

            # إضافة تفاصيل عن العناصر
            elements_count = len([e for e in sequence['elements'].values() if e])
            duration = sequence['end_time'] - sequence['start_time']

            return f"{base_description} - {elements_count} عناصر سردية في {duration:.1f} ثانية"

        except Exception as e:
            logger.error(f"خطأ في توليد الوصف: {e}")
            return "مقطع مترابط سردياً"

    def _generate_smart_title(self, content_type: str, sequence: Dict) -> str:
        """توليد عنوان ذكي للمقطع

        Args:
            content_type: نوع المحتوى
            sequence: التسلسل السردي

        Returns:
            عنوان المقطع
        """
        try:
            titles = {
                'challenge': "🔥 تحدي ملحمي",
                'reaction': "😱 ردة فعل مجنونة",
                'gaming': "🎮 لحظة ألعاب ملحمية",
                'experiment': "🧪 تجربة مثيرة",
                'tutorial': "📚 شرح مفيد",
                'entertainment': "😂 لحظة مضحكة"
            }

            base_title = titles.get(content_type, "🎬 مقطع مثير")
            confidence = sequence.get('confidence', 0)

            if confidence > 0.8:
                return f"{base_title} - جودة عالية"
            elif confidence > 0.6:
                return f"{base_title} - جودة جيدة"
            else:
                return base_title

        except Exception as e:
            logger.error(f"خطأ في توليد العنوان: {e}")
            return "🎬 مقطع مثير"

    def _remove_used_moments(self, excitement_moments: List[Dict],
                           used_clip: Dict) -> List[Dict]:
        """إزالة اللحظات المستخدمة لتجنب التداخل

        Args:
            excitement_moments: اللحظات المثيرة الأصلية
            used_clip: المقطع المستخدم

        Returns:
            اللحظات المتبقية
        """
        try:
            clip_start = used_clip['start_time']
            clip_end = used_clip['end_time']
            buffer = self.min_excitement_gap  # فجوة أمان

            remaining_moments = []
            for moment in excitement_moments:
                moment_start = moment.get('start_time', 0)
                moment_end = moment.get('end_time', moment_start + 30)

                # تحقق من عدم التداخل مع المقطع المستخدم
                if (moment_end < clip_start - buffer or
                    moment_start > clip_end + buffer):
                    remaining_moments.append(moment)

            logger.info(f"تم إزالة {len(excitement_moments) - len(remaining_moments)} لحظة مستخدمة")
            return remaining_moments

        except Exception as e:
            logger.error(f"خطأ في إزالة اللحظات المستخدمة: {e}")
            return excitement_moments

    def _simple_excitement_detection(self, video_path: str) -> List[Dict]:
        """نظام بسيط لكشف اللحظات المثيرة كنظام احتياطي"""
        try:
            # تحليل بسيط بناءً على مدة الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            duration = video_info.get('duration', 0)

            # إنشاء لحظات افتراضية موزعة على الفيديو
            moments = []
            interval = max(300, duration / 10)  # كل 5 دقائق أو 1/10 من الفيديو

            for i in range(0, int(duration), int(interval)):
                if i + 30 <= duration:  # التأكد من وجود مساحة كافية للمقطع
                    moments.append({
                        'start_time': i,
                        'end_time': i + 30,
                        'confidence': 0.75,  # ثقة متوسطة
                        'type': 'exciting',
                        'description': f'لحظة محتملة في الدقيقة {i//60}'
                    })

            logger.info(f"تم إنشاء {len(moments)} لحظة افتراضية")
            return moments

        except Exception as e:
            logger.error(f"خطأ في النظام البسيط: {e}")
            return []

    def analyze_coherent_livestream(self, video_path: str, target_clips: int = None) -> List[Dict[str, Any]]:
        """تحليل البث المباشر لإنشاء مقاطع شورتس مترابطة سردياً

        Args:
            video_path: مسار ملف الفيديو
            target_clips: عدد المقاطع المطلوبة (None للتحديد التلقائي)
        """
        try:
            logger.info(f"بدء التحليل المترابط للبث المباشر: {video_path}")

            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            video_duration = video_info.get('duration', 0)

            # تحليل نوع المحتوى أولاً
            content_analysis = self._analyze_content_type(video_path)
            logger.info(f"نوع المحتوى المكتشف: {content_analysis['type']} (ثقة: {content_analysis['confidence']:.2f})")

            # كشف اللحظات المثيرة
            excitement_moments = self._detect_excitement_moments(video_path)

            # إذا لم يتم تحديد عدد المقاطع، قم بحسابه تلقائياً
            if target_clips is None:
                target_clips = self._calculate_optimal_clips_count(video_duration, excitement_moments)
                logger.info(f"تم تحديد عدد المقاطع تلقائياً: {target_clips}")

            # إنشاء مقاطع مترابطة سردياً
            narrative_clips = []
            for i in range(target_clips):
                narrative_clip = self._create_narrative_clip(
                    video_path, content_analysis, excitement_moments
                )
                if narrative_clip:
                    narrative_clips.append(narrative_clip)
                    # إزالة اللحظات المستخدمة لتجنب التداخل
                    excitement_moments = self._remove_used_moments(
                        excitement_moments, narrative_clip
                    )

            # إذا لم نحصل على مقاطع مترابطة، استخدم النظام القديم كاحتياط
            if not narrative_clips:
                logger.warning("فشل في إنشاء مقاطع مترابطة، استخدام النظام الاحتياطي...")
                coherent_shorts = self.coherent_shorts_builder.build_coherent_shorts(
                    video_path, target_clips, max_duration=self.fixed_clip_duration
                )
            else:
                coherent_shorts = narrative_clips

            if not coherent_shorts:
                logger.warning("لم يتم العثور على مقاطع مترابطة، محاولة استخدام النظام الاحتياطي")

                # محاولة استخدام النظام الاحتياطي
                try:
                    fallback_results = self.fallback_analyzer.analyze_video_fallback(video_path, target_clips)
                    if fallback_results:
                        logger.info(f"تم استخدام النظام الاحتياطي بنجاح - {len(fallback_results)} مقطع")
                        return self._process_fallback_results(fallback_results, video_path)
                except Exception as fallback_error:
                    logger.error(f"فشل النظام الاحتياطي: {fallback_error}")

                # إذا فشل كل شيء، العودة للنظام التقليدي
                logger.warning("العودة للنظام التقليدي كحل أخير")
                return self.analyze_long_livestream_advanced(video_path, target_clips)

            # تسجيل إحصائيات التحليل
            coherent_count = sum(1 for s in coherent_shorts if s.coherence_score > 0.6)
            merged_count = sum(1 for s in coherent_shorts if "مجموعة" in s.story_arc)
            single_count = sum(1 for s in coherent_shorts if "لحظة" in s.story_arc)

            logger.info(f"نتائج التحليل المترابط: {coherent_count} مترابط، {merged_count} مدمج، {single_count} منفرد")

            # تحويل النتائج إلى التنسيق المطلوب
            results = []

            for i, short in enumerate(coherent_shorts):
                # إنشاء ملف الفيديو
                output_filename = f"coherent_shorts_{short.story_arc.replace(' ', '_')}_{self._get_timestamp()}.mp4"
                output_path = self.file_manager.get_output_path(output_filename)

                # قص الفيديو
                success = self.video_processor.trim_video(
                    video_path, str(output_path), short.start_time, short.end_time
                )

                if success:
                    # إنشاء النسخة العمودية للشورتس
                    vertical_filename = f"vertical_{output_filename}"
                    vertical_path = self.file_manager.get_output_path(vertical_filename)

                    self.video_processor.convert_to_vertical(str(output_path), str(vertical_path))

                    # إضافة الترجمات إذا كانت متوفرة
                    if hasattr(self, 'caption_generator') and self.caption_generator:
                        try:
                            self.caption_generator.add_captions_to_video(
                                str(vertical_path), str(vertical_path),
                                short.description_suggestion[:100]
                            )
                        except Exception as e:
                            logger.warning(f"فشل في إضافة الترجمات: {e}")

                    # تحديد نوع المقطع
                    clip_type = "coherent"  # مترابط
                    if "مجموعة" in short.story_arc:
                        clip_type = "merged"  # مدمج
                    elif "لحظة" in short.story_arc:
                        clip_type = "single"  # منفرد

                    # تحضير النتيجة مع معلومات إضافية
                    result = {
                        'type': short.story_arc,
                        'clip_type': clip_type,  # نوع المقطع الجديد
                        'start_time': short.start_time,
                        'end_time': short.end_time,
                        'duration': short.end_time - short.start_time,
                        'confidence': short.coherence_score,
                        'viral_potential': short.viral_potential,
                        'file_path': str(output_path),
                        'vertical_file_path': str(vertical_path),
                        'title': short.title_suggestion,
                        'description': short.description_suggestion,
                        'hashtags': short.hashtags,
                        'thumbnail_timestamp': short.thumbnail_timestamp,
                        'events_count': len(short.events),
                        'story_arc': short.story_arc,
                        'transition_points': short.transition_points,
                        'is_coherent_story': clip_type == "coherent",  # هل هو قصة مترابطة؟
                        'is_merged_highlights': clip_type == "merged",  # هل هو لقطات مدمجة؟
                        'is_single_moment': clip_type == "single",  # هل هو لحظة واحدة؟
                        'narrative_events': [
                            {
                                'type': event.event_type.value,
                                'start': event.start_time,
                                'end': event.end_time,
                                'description': event.description,
                                'importance': event.importance_score,
                                'intensity': event.emotional_intensity,
                                'keywords': event.keywords[:3]  # أهم 3 كلمات مفتاحية
                            }
                            for event in short.events
                        ],
                        'quality_metrics': {
                            'coherence_score': short.coherence_score,
                            'viral_potential': short.viral_potential,
                            'avg_intensity': sum(e.emotional_intensity for e in short.events) / len(short.events),
                            'avg_importance': sum(e.importance_score for e in short.events) / len(short.events),
                            'event_diversity': len(set(e.event_type for e in short.events))
                        }
                    }

                    results.append(result)

                    logger.info(f"تم إنشاء مقطع مترابط {i+1}: {short.story_arc} "
                              f"({short.end_time - short.start_time:.1f}s)")
                else:
                    logger.error(f"فشل في قص المقطع {i+1}")

            # حفظ النتائج في تاريخ الفيديوهات
            if results:
                try:
                    video_record = self.history_manager.get_video_by_path(video_path)
                    if video_record:
                        self.history_manager.add_processing_record(
                            video_record.id, 'coherent_narrative', target_clips,
                            len(results), True, output_files=[r['file_path'] for r in results]
                        )
                except Exception as e:
                    logger.warning(f"فشل في حفظ سجل المعالجة: {e}")

            logger.info(f"تم إنشاء {len(results)} مقطع شورتس مترابط بنجاح")
            return results

        except Exception as e:
            logger.error(f"خطأ في التحليل المترابط: {e}")
            # العودة للنظام التقليدي في حالة الخطأ
            return self.analyze_long_livestream_advanced(video_path, target_clips)

    def analyze_long_livestream_advanced(self, video_path: str,
                                        target_clips: int = None) -> List[Dict[str, Any]]:
        """تحليل متقدم للبث المباشر الطويل باستخدام النظام الجديد

        Args:
            video_path: مسار ملف الفيديو
            target_clips: عدد المقاطع المطلوبة (None للتحديد التلقائي)
        """
        try:
            logger.info(f"بدء التحليل المتقدم للبث المباشر: {video_path}")

            # استخدام النظام المتقدم لاكتشاف اللقطات
            highlights = self.advanced_detector.detect_highlights(video_path, target_clips)

            if not highlights:
                logger.warning("لم يتم العثور على لقطات بارزة باستخدام النظام المتقدم")
                # العودة للنظام القديم كبديل
                return self.analyze_long_livestream(video_path, target_clips)

            # تحويل النتائج إلى التنسيق المطلوب
            results = []
            for highlight in highlights:
                # إنشاء مقطع الشورتس
                clip_result = self._create_shorts_clip_from_highlight(video_path, highlight)
                if clip_result:
                    results.append(clip_result)

            logger.info(f"تم إنشاء {len(results)} مقطع شورتس باستخدام النظام المتقدم")
            return results

        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم: {e}")
            # العودة للنظام القديم في حالة الخطأ
            return self.analyze_long_livestream(video_path, target_clips)

    def _create_shorts_clip_from_highlight(self, video_path: str,
                                          highlight) -> Optional[Dict[str, Any]]:
        """إنشاء مقطع شورتس من لقطة بارزة"""
        try:
            # استخدام المدة الثابتة 30 ثانية
            # تعديل أوقات البداية والنهاية لتكون 30 ثانية بالضبط
            center_time = (highlight.start_time + highlight.end_time) / 2
            highlight.start_time = max(0, center_time - self.fixed_clip_duration / 2)
            highlight.end_time = highlight.start_time + self.fixed_clip_duration

            # إنشاء اسم الملف
            timestamp = int(highlight.start_time)
            filename = f"shorts_{highlight.highlight_type.value}_{timestamp}.mp4"
            output_path = self.file_manager.get_output_path(filename)

            # قص الفيديو
            success = self.video_processor.trim_video(
                video_path, str(output_path),
                highlight.start_time, highlight.end_time
            )

            if not success:
                logger.error(f"فشل في قص الفيديو للقطة: {highlight.start_time}-{highlight.end_time}")
                return None

            # إنشاء التعليقات التوضيحية
            captions = self._generate_captions_for_highlight(highlight)

            # إنشاء عنوان جذاب
            title = self._generate_engaging_title(highlight)

            # إنشاء هاشتاجات
            hashtags = self._generate_hashtags(highlight)

            return {
                'file_path': str(output_path),
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'duration': highlight.end_time - highlight.start_time,
                'type': highlight.highlight_type.value,
                'confidence': highlight.confidence,
                'title': title,
                'description': highlight.description,
                'hashtags': hashtags,
                'captions': captions,
                'text': highlight.text,
                'sentiment_score': highlight.sentiment_score,
                'audio_features': highlight.audio_features,
                'visual_features': highlight.visual_features
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء مقطع الشورتس: {e}")
            return None

    def _generate_captions_for_highlight(self, highlight) -> List[Dict[str, Any]]:
        """إنشاء تعليقات توضيحية للقطة البارزة"""
        try:
            if not highlight.text:
                return []

            # تقسيم النص إلى جمل قصيرة للتعليقات
            sentences = highlight.text.split('.')
            duration = highlight.end_time - highlight.start_time
            sentence_duration = duration / max(len(sentences), 1)

            captions = []
            for i, sentence in enumerate(sentences):
                sentence = sentence.strip()
                if len(sentence) > 3:
                    caption = {
                        'start_time': i * sentence_duration,
                        'end_time': (i + 1) * sentence_duration,
                        'text': sentence,
                        'style': 'highlight'  # نمط خاص للقطات البارزة
                    }
                    captions.append(caption)

            return captions

        except Exception as e:
            logger.error(f"خطأ في إنشاء التعليقات: {e}")
            return []

    def _generate_engaging_title(self, highlight) -> str:
        """إنشاء عنوان جذاب للقطة"""
        try:
            # قوالب العناوين حسب نوع اللقطة
            title_templates = {
                'exciting': [
                    "🔥 لحظة لا تصدق!",
                    "⚡ إثارة خالصة!",
                    "🚀 لحظة أسطورية!",
                    "💥 طاقة جنونية!"
                ],
                'funny': [
                    "😂 ضحك حتى البكاء!",
                    "🤣 كوميديا خالصة!",
                    "😆 لحظة مضحكة جداً!",
                    "😄 ضحك لا يتوقف!"
                ],
                'shocking': [
                    "😱 صدمة لا تصدق!",
                    "🤯 مفاجأة مذهلة!",
                    "😲 لم أتوقع هذا!",
                    "🫨 صدمة العمر!"
                ],
                'emotional': [
                    "💔 لحظة مؤثرة جداً",
                    "😢 عواطف جياشة",
                    "❤️ لحظة من القلب",
                    "🥺 مشاعر حقيقية"
                ],
                'epic': [
                    "👑 لحظة تاريخية!",
                    "🏆 إنجاز أسطوري!",
                    "⭐ لحظة لا تُنسى!",
                    "🎯 هدف مثالي!"
                ]
            }

            # اختيار عنوان عشوائي من القوالب
            templates = title_templates.get(highlight.highlight_type.value, ["🎬 لحظة بارزة!"])
            import random
            base_title = random.choice(templates)

            # إضافة معلومات إضافية إذا كان النص متوفراً
            if highlight.text and len(highlight.text) > 10:
                # استخراج كلمة مفتاحية من النص
                words = highlight.text.split()
                important_words = [w for w in words if len(w) > 4 and w.lower() not in ['this', 'that', 'with', 'from']]
                if important_words:
                    keyword = important_words[0].title()
                    base_title += f" | {keyword}"

            return base_title

        except Exception as e:
            logger.error(f"خطأ في إنشاء العنوان: {e}")
            return "🎬 لحظة بارزة!"

    def _generate_hashtags(self, highlight) -> List[str]:
        """إنشاء هاشتاجات للقطة"""
        try:
            hashtags = []

            # هاشتاجات أساسية
            hashtags.extend(['#shorts', '#viral', '#trending'])

            # هاشتاجات حسب نوع اللقطة
            type_hashtags = {
                'exciting': ['#exciting', '#amazing', '#wow', '#epic', '#insane'],
                'funny': ['#funny', '#comedy', '#lol', '#hilarious', '#meme'],
                'shocking': ['#shocking', '#surprise', '#unexpected', '#mindblown'],
                'emotional': ['#emotional', '#touching', '#feelings', '#heartwarming'],
                'epic': ['#epic', '#legendary', '#awesome', '#incredible', '#amazing']
            }

            specific_tags = type_hashtags.get(highlight.highlight_type.value, [])
            hashtags.extend(specific_tags[:3])  # أخذ أول 3 هاشتاجات

            # هاشتاجات من النص
            if highlight.text:
                words = highlight.text.lower().split()
                # البحث عن كلمات مناسبة للهاشتاجات
                for word in words:
                    word = word.strip('.,!?')
                    if (len(word) > 3 and
                        word not in ['this', 'that', 'with', 'from', 'they', 'have', 'will'] and
                        len(hashtags) < 10):
                        hashtags.append(f'#{word}')

            return hashtags[:10]  # حد أقصى 10 هاشتاجات

        except Exception as e:
            logger.error(f"خطأ في إنشاء الهاشتاجات: {e}")
            return ['#shorts', '#viral']

    def save_video_to_history(self, title: str, url: str, file_path: str,
                             duration: float, info_file_path: str = None) -> str:
        """حفظ الفيديو في تاريخ الفيديوهات"""
        try:
            video_id = self.history_manager.add_downloaded_video(
                title=title,
                url=url,
                file_path=file_path,
                duration=duration,
                info_file_path=info_file_path
            )
            logger.info(f"تم حفظ الفيديو في التاريخ: {title} (ID: {video_id})")
            return video_id
        except Exception as e:
            logger.error(f"خطأ في حفظ الفيديو في التاريخ: {e}")
            return None

    def analyze_long_livestream(self, video_path: str,
                               target_clips: int = None) -> List[Dict[str, Any]]:
        """تحليل بث مباشر طويل واستخراج أفضل اللحظات

        Args:
            video_path: مسار ملف الفيديو
            target_clips: عدد المقاطع المطلوبة (None للتحديد التلقائي)
        """
        try:
            logger.info(f"بدء تحليل البث المباشر: {video_path}")
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            if not video_info:
                logger.error("لا يمكن الحصول على معلومات الفيديو")
                return []
            
            duration = video_info['duration']
            logger.info(f"مدة البث: {duration/3600:.1f} ساعة")

            # تحديد عدد المقاطع تلقائياً إذا لم يتم تحديده
            if target_clips is None:
                initial_analysis = self._detect_excitement_moments(video_path)
                target_clips = self._calculate_optimal_clips_count(duration, initial_analysis)
                logger.info(f"تم تحديد عدد المقاطع تلقائياً: {target_clips}")

            if duration < 300:  # أقل من 5 دقائق
                logger.warning("البث قصير جداً للتحليل المتقدم")
                return self._analyze_short_video(video_path, target_clips)
            elif duration > 36000:  # أكثر من 10 ساعات
                logger.info("بث طويل جداً - استخدام تحليل محسن للبثوث الطويلة")
                return self._analyze_ultra_long_livestream(video_path, target_clips)
            
            # تقسيم البث إلى قطع للتحليل
            chunks = self._create_analysis_chunks(duration)
            logger.info(f"تم تقسيم البث إلى {len(chunks)} قطعة للتحليل")
            
            # تحليل كل قطعة
            all_moments = []
            for i, (start_time, end_time) in enumerate(chunks):
                logger.info(f"تحليل القطعة {i+1}/{len(chunks)}: {start_time/60:.1f}-{end_time/60:.1f} دقيقة")
                
                chunk_moments = self._analyze_chunk(
                    video_path, start_time, end_time
                )
                all_moments.extend(chunk_moments)
                
                # استراحة قصيرة لتجنب إرهاق النظام
                time.sleep(1)
            
            # ترتيب وفلترة أفضل اللحظات
            best_moments = self._select_best_moments(all_moments, target_clips)
            
            # إنشاء مقاطع الشورتس
            shorts_clips = []
            for moment in best_moments:
                clip = self._create_shorts_clip(video_path, moment)
                if clip:
                    shorts_clips.append(clip)
            
            logger.info(f"تم إنشاء {len(shorts_clips)} مقطع شورتس")
            return shorts_clips
            
        except Exception as e:
            logger.error(f"خطأ في تحليل البث المباشر: {e}")
            return []
    
    def _create_analysis_chunks(self, total_duration: float) -> List[Tuple[float, float]]:
        """تقسيم البث إلى قطع للتحليل"""
        chunks = []
        current_time = 0
        
        while current_time < total_duration:
            end_time = min(current_time + self.chunk_duration, total_duration)
            chunks.append((current_time, end_time))
            current_time = end_time
        
        return chunks
    
    def _analyze_chunk(self, video_path: str, start_time: float, 
                      end_time: float) -> List[Dict[str, Any]]:
        """تحليل قطعة من البث"""
        try:
            # استخراج الصوت من القطعة
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            
            # قص الصوت من القطعة المحددة
            success = self._extract_chunk_audio(
                video_path, str(audio_path), start_time, end_time
            )
            
            if not success:
                return []
            
            # تحويل الكلام إلى نص
            transcript = self._transcribe_chunk_audio(str(audio_path))
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
            if not transcript:
                return []
            
            # تحليل النص للعثور على اللحظات المثيرة
            moments = self._analyze_transcript_for_moments(
                transcript, start_time, end_time
            )
            
            # تحليل الصوت للعثور على ذروات الإثارة
            audio_moments = self._analyze_audio_excitement(
                video_path, start_time, end_time
            )
            
            # دمج النتائج
            combined_moments = self._combine_analysis_results(
                moments, audio_moments, start_time
            )
            
            return combined_moments
            
        except Exception as e:
            logger.error(f"خطأ في تحليل القطعة: {e}")
            return []
    
    def _extract_chunk_audio(self, video_path: str, audio_path: str,
                           start_time: float, end_time: float) -> bool:
        """استخراج الصوت من قطعة محددة"""
        try:
            import subprocess
            
            cmd = [
                self.video_processor.ffmpeg_path,
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-vn',  # بدون فيديو
                '-acodec', 'pcm_s16le',
                '-ar', '16000',  # 16kHz للتوافق مع APIs
                '-ac', '1',  # mono
                '-y',
                audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الصوت: {e}")
            return False
    
    def _transcribe_chunk_audio(self, audio_path: str) -> Optional[str]:
        """تحويل صوت القطعة إلى نص"""
        try:
            # محاولة استخدام AssemblyAI أولاً (أفضل جودة)
            if self.assemblyai_client.is_available():
                transcript = self.assemblyai_client.transcribe_audio(audio_path, "en")
                if transcript:
                    logger.info("تم التحويل باستخدام AssemblyAI")
                    return transcript

            # محاولة استخدام Google Cloud كبديل
            if self.gc_client.is_available():
                transcript = self.gc_client.transcribe_audio(audio_path, "en-US")
                if transcript:
                    logger.info("تم التحويل باستخدام Google Cloud")
                    return transcript

            # محاولة استخدام Hugging Face كبديل أخير
            if self.hf_client.api_key:
                transcript = self.hf_client.speech_to_text(audio_path, "en")
                if transcript:
                    logger.info("تم التحويل باستخدام Hugging Face")
                    return transcript

            logger.warning("لا توجد خدمة متاحة لتحويل الكلام إلى نص")
            return None

        except Exception as e:
            logger.error(f"خطأ في تحويل الكلام إلى نص: {e}")
            return None
    
    def _analyze_transcript_for_moments(self, transcript: str, 
                                      start_time: float, end_time: float) -> List[Dict[str, Any]]:
        """تحليل النص للعثور على اللحظات المثيرة"""
        moments = []
        
        try:
            # تقسيم النص إلى جمل
            sentences = transcript.split('.')
            sentence_duration = (end_time - start_time) / len(sentences)
            
            for i, sentence in enumerate(sentences):
                sentence = sentence.strip().lower()
                if len(sentence) < 5:
                    continue
                
                sentence_start = start_time + (i * sentence_duration)
                sentence_end = sentence_start + sentence_duration
                
                # تحليل نوع اللحظة
                moment_type, confidence = self._classify_moment_type(sentence)
                
                if confidence > self.excitement_threshold:
                    moment = {
                        'start_time': sentence_start,
                        'end_time': sentence_end,
                        'type': moment_type,
                        'confidence': confidence,
                        'text': sentence,
                        'source': 'transcript'
                    }
                    moments.append(moment)
            
            return moments
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النص: {e}")
            return []
    
    def _classify_moment_type(self, text: str) -> Tuple[str, float]:
        """تصنيف نوع اللحظة وحساب الثقة"""
        excitement_score = 0
        funny_score = 0
        shocking_score = 0
        
        # حساب نقاط كل نوع
        for keyword in self.excitement_keywords:
            if keyword in text:
                excitement_score += 1
        
        for keyword in self.funny_keywords:
            if keyword in text:
                funny_score += 1
        
        for keyword in self.shocking_keywords:
            if keyword in text:
                shocking_score += 1
        
        # تحديد النوع الأعلى نقاطاً
        scores = {
            'exciting': excitement_score,
            'funny': funny_score,
            'shocking': shocking_score
        }
        
        best_type = max(scores, key=scores.get)
        max_score = scores[best_type]
        
        # تحويل النقاط إلى نسبة ثقة
        confidence = min(max_score / 3.0, 1.0)  # تطبيع إلى 0-1
        
        return best_type, confidence
    
    def _analyze_audio_excitement(self, video_path: str, start_time: float, 
                                end_time: float) -> List[Dict[str, Any]]:
        """تحليل الصوت للعثور على ذروات الإثارة"""
        try:
            # استخراج الصوت للتحليل
            audio_path = self.file_manager.create_temp_file(suffix='.wav')
            
            success = self._extract_chunk_audio(
                video_path, str(audio_path), start_time, end_time
            )
            
            if not success:
                return []
            
            # تحليل مستوى الصوت
            volume_peaks = self._detect_volume_peaks(str(audio_path))
            
            # تحويل ذروات الصوت إلى لحظات
            audio_moments = []
            for peak_time, volume_level in volume_peaks:
                if volume_level > 0.8:  # ذروة عالية
                    moment_start = start_time + peak_time - 5  # 5 ثواني قبل الذروة
                    moment_end = start_time + peak_time + 10   # 10 ثواني بعد الذروة
                    
                    moment = {
                        'start_time': max(moment_start, start_time),
                        'end_time': min(moment_end, end_time),
                        'type': 'exciting',
                        'confidence': volume_level,
                        'text': f'ذروة صوتية عند {peak_time:.1f}s',
                        'source': 'audio_analysis'
                    }
                    audio_moments.append(moment)
            
            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)
            
            return audio_moments
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الصوت: {e}")
            return []
    
    def _detect_volume_peaks(self, audio_path: str) -> List[Tuple[float, float]]:
        """كشف ذروات مستوى الصوت"""
        try:
            # استخدام librosa إذا كان متوفراً، وإلا استخدام طريقة بديلة
            try:
                import librosa
                y, sr = librosa.load(audio_path)
                
                # حساب RMS (Root Mean Square) للطاقة
                hop_length = 512
                frame_length = 2048
                rms = librosa.feature.rms(y=y, frame_length=frame_length, 
                                        hop_length=hop_length)[0]
                
                # تحويل إلى وقت
                times = librosa.frames_to_time(range(len(rms)), sr=sr,
                                             hop_length=hop_length)

                # العثور على الذروات
                peaks = []
                if NUMPY_AVAILABLE:
                    import numpy as np
                    threshold = np.mean(rms) + 2 * np.std(rms)

                    for i, (time, energy) in enumerate(zip(times, rms)):
                        if energy > threshold:
                            # تطبيع الطاقة إلى 0-1
                            normalized_energy = min(energy / threshold, 1.0)
                            peaks.append((time, normalized_energy))
                else:
                    # حساب بسيط بدون numpy
                    mean_rms = sum(rms) / len(rms)
                    threshold = mean_rms * 1.5  # تقريب بسيط

                    for i, (time, energy) in enumerate(zip(times, rms)):
                        if energy > threshold:
                            normalized_energy = min(energy / threshold, 1.0)
                            peaks.append((time, normalized_energy))
                
                return peaks
                
            except ImportError:
                # طريقة بديلة باستخدام FFmpeg
                return self._detect_volume_peaks_ffmpeg(audio_path)
                
        except Exception as e:
            logger.error(f"خطأ في كشف ذروات الصوت: {e}")
            return []
    
    def _detect_volume_peaks_ffmpeg(self, audio_path: str) -> List[Tuple[float, float]]:
        """كشف ذروات الصوت باستخدام FFmpeg"""
        try:
            import subprocess
            
            # استخدام FFmpeg لتحليل مستوى الصوت
            cmd = [
                self.video_processor.ffmpeg_path,
                '-i', audio_path,
                '-af', 'volumedetect',
                '-f', 'null',
                '-'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # تحليل بسيط - في التطبيق الحقيقي نحتاج تحليل أكثر تفصيلاً
            peaks = []
            if 'max_volume' in result.stderr:
                # إضافة ذروة واحدة في منتصف المقطع كمثال
                peaks.append((15.0, 0.9))  # ذروة عند 15 ثانية بقوة 0.9
            
            return peaks
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الصوت بـ FFmpeg: {e}")
            return []
    
    def _combine_analysis_results(self, transcript_moments: List[Dict], 
                                audio_moments: List[Dict], 
                                chunk_start: float) -> List[Dict[str, Any]]:
        """دمج نتائج تحليل النص والصوت"""
        combined = []
        
        # إضافة لحظات النص
        combined.extend(transcript_moments)
        
        # إضافة لحظات الصوت مع تجنب التداخل
        for audio_moment in audio_moments:
            # التحقق من عدم التداخل مع لحظات النص
            overlaps = False
            for text_moment in transcript_moments:
                if (audio_moment['start_time'] < text_moment['end_time'] and
                    audio_moment['end_time'] > text_moment['start_time']):
                    overlaps = True
                    break
            
            if not overlaps:
                combined.append(audio_moment)
        
        return combined
    
    def _select_best_moments(self, all_moments: List[Dict], 
                           target_clips: int) -> List[Dict[str, Any]]:
        """اختيار أفضل اللحظات"""
        if not all_moments:
            return []
        
        # ترتيب حسب الثقة
        sorted_moments = sorted(all_moments, key=lambda x: x['confidence'], reverse=True)
        
        # فلترة اللحظات المتداخلة
        filtered_moments = []
        for moment in sorted_moments:
            # التحقق من عدم التداخل مع اللحظات المختارة
            overlaps = False
            for selected in filtered_moments:
                if (moment['start_time'] < selected['end_time'] and
                    moment['end_time'] > selected['start_time']):
                    overlaps = True
                    break
            
            if not overlaps:
                filtered_moments.append(moment)
                
                if len(filtered_moments) >= target_clips:
                    break
        
        return filtered_moments
    
    def _analyze_short_video(self, video_path: str, target_clips: int) -> List[Dict[str, Any]]:
        """تحليل فيديو قصير (أقل من 10 دقائق)"""
        try:
            # للفيديوهات القصيرة، نحلل الفيديو كاملاً
            video_info = self.video_processor.get_video_info(video_path)
            duration = video_info['duration']

            moments = self._analyze_chunk(video_path, 0, duration)
            best_moments = self._select_best_moments(moments, target_clips)

            # إنشاء مقاطع الشورتس
            shorts_clips = []
            for moment in best_moments:
                clip = self._create_shorts_clip(video_path, moment)
                if clip:
                    shorts_clips.append(clip)

            logger.info(f"تم إنشاء {len(shorts_clips)} مقطع شورتس من الفيديو القصير")
            return shorts_clips

        except Exception as e:
            logger.error(f"خطأ في تحليل الفيديو القصير: {e}")
            return []

    def _create_shorts_clip(self, video_path: str, moment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """إنشاء مقطع شورتس من اللحظة المحددة"""
        try:
            # استخدام المدة الثابتة 30 ثانية
            # تحديد نقطة الوسط للحظة المثيرة
            center_time = (moment['start_time'] + moment['end_time']) / 2

            # تعيين أوقات البداية والنهاية لتكون 30 ثانية بالضبط
            start_time = max(0, center_time - self.fixed_clip_duration / 2)
            end_time = start_time + self.fixed_clip_duration

            # التأكد من عدم تجاوز مدة الفيديو الأصلي
            video_info = self.video_processor.get_video_info(video_path)
            if video_info and end_time > video_info['duration']:
                end_time = video_info['duration']
                start_time = max(0, end_time - self.fixed_clip_duration)

            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            clip_filename = f"shorts_{moment['type']}_{timestamp}.mp4"
            clip_path = self.file_manager.output_dir / clip_filename

            # قص الفيديو
            success = self.video_processor.trim_video(
                video_path, str(clip_path), start_time, end_time
            )

            if not success:
                logger.error("فشل في قص المقطع")
                return None

            # تحويل إلى تنسيق عمودي (9:16) للشورتس
            vertical_clip_path = self.file_manager.output_dir / f"vertical_{clip_filename}"
            success = self._convert_to_vertical(str(clip_path), str(vertical_clip_path))

            if not success:
                logger.warning("فشل في التحويل العمودي، سيتم استخدام المقطع الأصلي")
                vertical_clip_path = clip_path

            # إنشاء التعليقات التوضيحية
            captions = self._generate_clip_captions(
                str(vertical_clip_path), start_time, end_time
            )

            # إضافة النصوص على الفيديو
            final_clip_path = self._add_captions_to_video(
                str(vertical_clip_path), captions, moment
            )

            # إنشاء معلومات المقطع
            clip_info = {
                'file_path': str(final_clip_path),
                'original_start_time': start_time,
                'original_end_time': end_time,
                'duration': end_time - start_time,
                'type': moment['type'],
                'confidence': moment['confidence'],
                'description': self._generate_clip_description(moment),
                'captions': captions,
                'suggested_title': self._generate_clip_title(moment),
                'hashtags': self._generate_clip_hashtags(moment),
                'thumbnail_time': (start_time + end_time) / 2  # منتصف المقطع للصورة المصغرة
            }

            logger.info(f"تم إنشاء مقطع شورتس: {clip_filename}")
            return clip_info

        except Exception as e:
            logger.error(f"خطأ في إنشاء مقطع الشورتس: {e}")
            return None

    def _convert_to_vertical(self, input_path: str, output_path: str) -> bool:
        """تحويل الفيديو إلى تنسيق عمودي (9:16)"""
        try:
            # الحصول على معلومات الفيديو الأصلي
            video_info = self.video_processor.get_video_info(input_path)
            original_width = video_info['video']['width']
            original_height = video_info['video']['height']

            # حساب الأبعاد الجديدة (9:16)
            target_width = 1080
            target_height = 1920

            # تحديد طريقة التحويل بناءً على النسبة الأصلية
            if original_width > original_height:
                # فيديو أفقي - نحتاج لقص أو تكبير
                scale_factor = target_height / original_height
                scaled_width = int(original_width * scale_factor)

                if scaled_width >= target_width:
                    # قص من الجانبين
                    crop_x = (scaled_width - target_width) // 2
                    filter_complex = f"scale={scaled_width}:{target_height},crop={target_width}:{target_height}:{crop_x}:0"
                else:
                    # إضافة حدود سوداء
                    filter_complex = f"scale={scaled_width}:{target_height},pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black"
            else:
                # فيديو عمودي أو مربع
                success = self.video_processor.resize_video(
                    input_path, output_path, target_width, target_height, 'medium'
                )
                return success

            # تطبيق الفلتر
            import subprocess
            cmd = [
                self.video_processor.ffmpeg_path,
                '-i', input_path,
                '-vf', filter_complex,
                '-c:a', 'copy',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0

        except Exception as e:
            logger.error(f"خطأ في التحويل العمودي: {e}")
            return False

    def _generate_clip_captions(self, video_path: str, start_time: float,
                              end_time: float) -> List[Dict[str, Any]]:
        """إنشاء تعليقات توضيحية للمقطع"""
        try:
            # استخراج الصوت من المقطع
            audio_path = self.file_manager.create_temp_file(suffix='.wav')

            success = self.video_processor.extract_audio(video_path, str(audio_path))
            if not success:
                return []

            # تحويل الكلام إلى نص
            transcript = None

            # محاولة استخدام Google Cloud أولاً
            if self.gc_client.is_available():
                transcript = self.gc_client.transcribe_audio(str(audio_path), "en-US")

            # محاولة استخدام Hugging Face كبديل
            if not transcript and self.hf_client.api_key:
                transcript = self.hf_client.speech_to_text(str(audio_path), "en")

            # تنظيف الملف المؤقت
            audio_path.unlink(missing_ok=True)

            if not transcript:
                return []

            # تقسيم النص إلى تعليقات مؤقتة
            captions = self.caption_generator._create_timed_captions(transcript, video_path)

            # ترجمة إلى العربية إذا كان النص بالإنجليزية
            if self._is_english_text(transcript):
                arabic_captions = []
                for caption in captions:
                    arabic_text = self._translate_to_arabic(caption['text'])
                    if arabic_text:
                        arabic_caption = caption.copy()
                        arabic_caption['text'] = arabic_text
                        arabic_caption['original_text'] = caption['text']
                        arabic_captions.append(arabic_caption)

                return arabic_captions

            return captions

        except Exception as e:
            logger.error(f"خطأ في إنشاء التعليقات التوضيحية: {e}")
            return []

    def _is_english_text(self, text: str) -> bool:
        """التحقق من كون النص بالإنجليزية"""
        # فحص بسيط - إذا كان معظم النص أحرف لاتينية
        latin_chars = sum(1 for c in text if c.isascii() and c.isalpha())
        total_chars = sum(1 for c in text if c.isalpha())

        if total_chars == 0:
            return False

        return (latin_chars / total_chars) > 0.7

    def _translate_to_arabic(self, text: str) -> Optional[str]:
        """ترجمة النص إلى العربية"""
        try:
            # محاولة استخدام Gemini أولاً (أفضل جودة)
            if self.gemini_client.is_available():
                translated = self.gemini_client.translate_text(text, "ar", "en")
                if translated:
                    logger.info("تم الترجمة باستخدام Gemini")
                    return translated

            # محاولة استخدام Google Cloud كبديل
            if self.gc_client.is_available():
                translated = self.gc_client.translate_text_google(text, "ar")
                if translated:
                    logger.info("تم الترجمة باستخدام Google Cloud")
                    return translated

            # محاولة استخدام Hugging Face كبديل أخير
            if self.hf_client.api_key:
                translated = self.hf_client.translate_text(text, "en", "ar")
                if translated:
                    logger.info("تم الترجمة باستخدام Hugging Face")
                    return translated

            logger.warning("لا توجد خدمة متاحة للترجمة")
            return None

        except Exception as e:
            logger.error(f"خطأ في الترجمة: {e}")
            return None

    def _add_captions_to_video(self, video_path: str, captions: List[Dict],
                             moment: Dict[str, Any]) -> str:
        """إضافة التعليقات التوضيحية على الفيديو"""
        try:
            if not captions:
                return video_path

            # إنشاء مسار الملف النهائي
            final_path = video_path.replace('.mp4', '_with_captions.mp4')

            # إنشاء فلتر النصوص
            text_filters = []
            for i, caption in enumerate(captions):
                # تنظيف النص
                clean_text = caption['text'].replace("'", "\\'").replace('"', '\\"')

                # تحديد موقع النص (أسفل الشاشة للشورتس)
                text_filter = (
                    f"drawtext=text='{clean_text}'"
                    f":fontsize=32"
                    f":fontcolor=white"
                    f":x=(w-text_w)/2"
                    f":y=h-text_h-50"
                    f":enable='between(t,{caption['start_time']},{caption['end_time']})'"
                    f":box=1:boxcolor=black@0.5:boxborderw=5"
                )
                text_filters.append(text_filter)

            # دمج جميع فلاتر النصوص
            if text_filters:
                combined_filter = ','.join(text_filters)

                import subprocess
                cmd = [
                    self.video_processor.ffmpeg_path,
                    '-i', video_path,
                    '-vf', combined_filter,
                    '-c:a', 'copy',
                    '-y',
                    final_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    return final_path
                else:
                    logger.error(f"خطأ في إضافة النصوص: {result.stderr}")
                    return video_path

            return video_path

        except Exception as e:
            logger.error(f"خطأ في إضافة التعليقات على الفيديو: {e}")
            return video_path

    def _generate_clip_description(self, moment: Dict[str, Any]) -> str:
        """توليد وصف للمقطع"""
        type_descriptions = {
            'exciting': 'لحظة مثيرة ومليئة بالإثارة',
            'funny': 'لحظة مضحكة ومسلية',
            'shocking': 'لحظة صادمة وغير متوقعة'
        }

        base_description = type_descriptions.get(moment['type'], 'لحظة مميزة')

        if moment.get('text'):
            return f"{base_description} - {moment['text'][:100]}..."

        return base_description

    def _generate_clip_title(self, moment: Dict[str, Any]) -> str:
        """توليد عنوان للمقطع"""
        try:
            # محاولة استخدام Gemini لتوليد عنوان ذكي
            if self.gemini_client.is_available():
                description = moment.get('text', moment.get('description', ''))
                moment_type = moment.get('type', 'exciting')

                smart_title = self.gemini_client.generate_video_title(description, moment_type)
                if smart_title:
                    logger.info("تم توليد عنوان ذكي باستخدام Gemini")
                    return smart_title

            # العناوين الافتراضية كبديل
            type_titles = {
                'exciting': [
                    "لحظة لا تُصدق! 🔥",
                    "هذا مجنون! 😱",
                    "لن تصدق ما حدث! ⚡",
                    "لحظة أسطورية! 🚀"
                ],
                'funny': [
                    "ضحك حتى البكاء! 😂",
                    "أضحك مقطع اليوم! 🤣",
                    "كوميديا خالصة! 😆",
                    "مقطع مضحك جداً! 😄"
                ],
                'shocking': [
                    "صدمة! لم أتوقع هذا! 😲",
                    "مفاجأة غير متوقعة! 🤯",
                    "لحظة صادمة! ⚡",
                    "لن تصدق ما حدث! 😱"
                ]
            }

            titles = type_titles.get(moment['type'], ["لحظة مميزة! ✨"])

            # اختيار عنوان عشوائي
            import random
            return random.choice(titles)

        except Exception as e:
            logger.error(f"خطأ في توليد العنوان: {e}")
            return "لحظة مميزة! ✨"

    def _generate_clip_hashtags(self, moment: Dict[str, Any]) -> List[str]:
        """توليد هاشتاغات للمقطع"""
        try:
            # محاولة استخدام Gemini لتوليد هاشتاغات ذكية
            if self.gemini_client.is_available():
                content = moment.get('text', moment.get('description', ''))
                moment_type = moment.get('type', 'exciting')

                smart_hashtags = self.gemini_client.generate_hashtags(content, moment_type)
                if smart_hashtags:
                    logger.info("تم توليد هاشتاغات ذكية باستخدام Gemini")
                    return smart_hashtags[:15]  # حد أقصى 15 هاشتاغ

            # الهاشتاغات الافتراضية كبديل
            base_hashtags = ["#shorts", "#viral", "#trending", "#gaming"]

            type_hashtags = {
                'exciting': ["#exciting", "#amazing", "#incredible", "#مثير", "#رهيب"],
                'funny': ["#funny", "#comedy", "#laugh", "#مضحك", "#كوميدي"],
                'shocking': ["#shocking", "#unexpected", "#surprise", "#صادم", "#مفاجأة"]
            }

            moment_hashtags = type_hashtags.get(moment['type'], [])

            # إضافة هاشتاغات خاصة بالستريمر (يمكن تخصيصها)
            streamer_hashtags = ["#ishowspeed", "#speed", "#streamer", "#live"]

            all_hashtags = base_hashtags + moment_hashtags + streamer_hashtags

            return all_hashtags[:15]  # حد أقصى 15 هاشتاغ

        except Exception as e:
            logger.error(f"خطأ في توليد الهاشتاغات: {e}")
            return ["#shorts", "#viral", "#trending"]

    def _get_timestamp(self) -> str:
        """الحصول على طابع زمني للملفات"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    def _process_fallback_results(self, fallback_results: List[Dict], video_path: str) -> List[Dict[str, Any]]:
        """معالجة نتائج النظام الاحتياطي"""
        try:
            processed_results = []

            for i, result in enumerate(fallback_results):
                # إنشاء ملف الفيديو
                output_filename = f"fallback_{result.get('template_used', 'general')}_{i+1}_{self._get_timestamp()}.mp4"
                output_path = self.file_manager.get_output_path(output_filename)

                # قص الفيديو
                success = self.video_processor.trim_video(
                    video_path, str(output_path),
                    result['start_time'], result['end_time']
                )

                if success:
                    # إنشاء النسخة العمودية
                    vertical_filename = f"vertical_{output_filename}"
                    vertical_path = self.file_manager.get_output_path(vertical_filename)

                    self.video_processor.convert_to_vertical(str(output_path), str(vertical_path))

                    # تحضير النتيجة النهائية
                    processed_result = {
                        'type': result['type'],
                        'clip_type': 'fallback',
                        'start_time': result['start_time'],
                        'end_time': result['end_time'],
                        'duration': result['duration'],
                        'confidence': result['confidence'],
                        'viral_potential': result['viral_potential'],
                        'file_path': str(output_path),
                        'vertical_file_path': str(vertical_path),
                        'title': result['title'],
                        'description': result['description'],
                        'hashtags': result['hashtags'],
                        'thumbnail_timestamp': (result['start_time'] + result['end_time']) / 2,
                        'events_count': 1,
                        'story_arc': f"تحليل احتياطي - {result.get('template_used', 'عام')}",
                        'is_fallback': True,
                        'analysis_method': result.get('analysis_method', 'fallback'),
                        'template_used': result.get('template_used', 'general'),
                        'quality_metrics': {
                            'coherence_score': result['confidence'],
                            'viral_potential': result['viral_potential'],
                            'avg_intensity': result['viral_potential'],
                            'avg_importance': result['confidence'],
                            'event_diversity': 1
                        }
                    }

                    processed_results.append(processed_result)

                    logger.info(f"تم إنشاء مقطع احتياطي {i+1}: {result['type']} "
                              f"({result['duration']:.1f}s)")
                else:
                    logger.error(f"فشل في قص المقطع الاحتياطي {i+1}")

            return processed_results

        except Exception as e:
            logger.error(f"خطأ في معالجة نتائج النظام الاحتياطي: {e}")
            return []

    def _analyze_ultra_long_livestream(self, video_path: str, target_clips: int = None) -> List[Dict[str, Any]]:
        """تحليل محسن للبثوث الطويلة جداً (أكثر من 10 ساعات)"""
        try:
            logger.info("بدء التحليل المحسن للبث الطويل جداً")

            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            duration = video_info['duration']

            # تقسيم البث إلى قطع كبيرة (ساعة واحدة لكل قطعة)
            hour_chunks = []
            current_time = 0
            chunk_duration = 3600  # ساعة واحدة

            while current_time < duration:
                end_time = min(current_time + chunk_duration, duration)
                hour_chunks.append((current_time, end_time))
                current_time = end_time

            logger.info(f"تم تقسيم البث إلى {len(hour_chunks)} قطعة ساعية")

            # تحليل كل ساعة بشكل منفصل
            all_highlights = []
            for i, (start_time, end_time) in enumerate(hour_chunks):
                logger.info(f"تحليل الساعة {i+1}/{len(hour_chunks)}: {start_time/3600:.1f}-{end_time/3600:.1f}h")

                # استخراج أفضل 3-5 لحظات من كل ساعة
                hour_highlights = self._analyze_hour_segment(video_path, start_time, end_time, max_highlights=5)
                all_highlights.extend(hour_highlights)

                # استراحة أطول للبثوث الطويلة
                time.sleep(2)

            # فلترة وترتيب أفضل اللحظات من جميع الساعات
            if target_clips is None:
                target_clips = min(len(hour_chunks) * 2, 20)  # حد أقصى 20 مقطع

            best_highlights = self._select_best_moments(all_highlights, target_clips)

            # إنشاء مقاطع الشورتس
            final_clips = []
            for highlight in best_highlights:
                clip_result = self._create_shorts_clip(video_path, highlight)
                if clip_result:
                    final_clips.append(clip_result)

            logger.info(f"تم إنشاء {len(final_clips)} مقطع من البث الطويل")
            return final_clips

        except Exception as e:
            logger.error(f"خطأ في تحليل البث الطويل جداً: {e}")
            # العودة للطريقة العادية
            return self.analyze_long_livestream_advanced(video_path, target_clips)
