#!/usr/bin/env python3
"""
تشغيل سريع للتطبيق مع فحص شامل
Quick start with comprehensive checks
"""

import sys
import os
from pathlib import Path

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_system_status():
    """فحص شامل لحالة النظام"""
    
    print("🚀 فحص حالة النظام...")
    print("=" * 50)
    
    # 1. فحص المتطلبات الأساسية
    print("1️⃣ فحص المتطلبات الأساسية...")
    
    missing_deps = []
    required_modules = ['PIL', 'cv2', 'numpy', 'requests', 'moviepy', 'pydub']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            missing_deps.append(module)
    
    if missing_deps:
        print(f"\n❌ مكتبات مفقودة: {', '.join(missing_deps)}")
        print(f"💡 قم بتثبيتها: pip install -r requirements.txt")
        return False
    
    # 2. فحص FFmpeg
    print(f"\n2️⃣ فحص FFmpeg...")
    import subprocess
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print(f"   ✅ FFmpeg متوفر")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print(f"   ❌ FFmpeg غير متوفر")
        print(f"💡 قم بتثبيت FFmpeg من: https://ffmpeg.org/download.html")
        return False
    
    # 3. فحص مفاتيح Gemini
    print(f"\n3️⃣ فحص مفاتيح Gemini...")
    
    try:
        from ai.gemini_key_manager import gemini_key_manager
        
        status_summary = gemini_key_manager.get_keys_status_summary()
        total_keys = status_summary['total_keys']
        available_keys = status_summary['available_keys']
        
        print(f"   📊 إجمالي المفاتيح: {total_keys}")
        print(f"   📊 المفاتيح المتاحة: {available_keys}")
        
        if available_keys == 0 and total_keys > 0:
            print(f"   ⚠️  جميع المفاتيح محظورة مؤقتاً")

            # أولاً: إعطاء أولوية للمفاتيح الجديدة
            new_reset_count = gemini_key_manager.prioritize_new_keys()
            if new_reset_count > 0:
                print(f"   🆕 تم إعادة تعيين {new_reset_count} مفتاح جديد")
                status_summary = gemini_key_manager.get_keys_status_summary()
                available_keys = status_summary['available_keys']

                if available_keys > 0:
                    print(f"   ✅ Gemini جاهز للاستخدام مع المفاتيح الجديدة")
                    return True

            # ثانياً: محاولة إعادة تفعيل المفاتيح القديمة
            reset_count = gemini_key_manager.reset_old_keys(hours_threshold=23.5)
            if reset_count > 0:
                print(f"   🔄 تم إعادة تفعيل {reset_count} مفتاح")
                status_summary = gemini_key_manager.get_keys_status_summary()
                available_keys = status_summary['available_keys']
                
                if available_keys > 0:
                    print(f"   ✅ Gemini جاهز للاستخدام")
                else:
                    print(f"   🛡️  سيتم استخدام النظام الاحتياطي")
            else:
                next_available = gemini_key_manager._get_next_key_available_time()
                if next_available:
                    print(f"   ⏰ أقرب مفتاح سيتوفر خلال: {next_available}")
                print(f"   🛡️  سيتم استخدام النظام الاحتياطي")
                
        elif available_keys > 0:
            print(f"   ✅ Gemini جاهز للاستخدام")
        else:
            print(f"   ⚠️  لا توجد مفاتيح Gemini")
            print(f"   🛡️  سيتم استخدام النظام الاحتياطي")
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص Gemini: {e}")
        print(f"   🛡️  سيتم استخدام النظام الاحتياطي")
    
    # 4. فحص مجلدات البيانات
    print(f"\n4️⃣ فحص مجلدات البيانات...")
    
    required_dirs = ['data', 'output', 'temp']
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   🔧 إنشاء {dir_name}/")
            dir_path.mkdir(exist_ok=True)
    
    print(f"\n✅ فحص النظام مكتمل!")
    return True

def show_system_info():
    """عرض معلومات النظام"""
    
    print(f"\n📋 معلومات النظام:")
    print("-" * 30)
    
    try:
        # معلومات Python
        print(f"🐍 Python: {sys.version.split()[0]}")
        
        # معلومات النظام
        import platform
        print(f"💻 النظام: {platform.system()} {platform.release()}")
        
        # معلومات التطبيق
        try:
            from config.settings import AppSettings
            print(f"🎬 التطبيق: {AppSettings.APP_NAME} v{AppSettings.APP_VERSION}")
        except:
            print(f"🎬 التطبيق: Video Editor Pro")
        
        # حالة مفاتيح Gemini
        try:
            from ai.gemini_key_manager import gemini_key_manager
            status_summary = gemini_key_manager.get_keys_status_summary()
            available_keys = status_summary['available_keys']
            total_keys = status_summary['total_keys']
            
            if available_keys > 0:
                print(f"🔑 Gemini: {available_keys}/{total_keys} مفتاح متاح")
            else:
                print(f"🛡️  Gemini: نظام احتياطي")
        except:
            print(f"🛡️  Gemini: نظام احتياطي")
            
    except Exception as e:
        print(f"❌ خطأ في عرض معلومات النظام: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🎬 Video Editor Pro - التشغيل السريع")
    print("=" * 60)
    
    # فحص حالة النظام
    system_ok = check_system_status()
    
    # عرض معلومات النظام
    show_system_info()
    
    print(f"\n" + "=" * 60)
    
    if system_ok:
        print(f"🎉 النظام جاهز للاستخدام!")
        print(f"💡 لتشغيل التطبيق: python main.py")
        
        # سؤال المستخدم إذا كان يريد تشغيل التطبيق
        try:
            choice = input(f"\nهل تريد تشغيل التطبيق الآن؟ (y/n): ").strip().lower()
            if choice in ['y', 'yes', 'نعم']:
                print(f"🚀 تشغيل التطبيق...")
                
                # تشغيل التطبيق الرئيسي
                import main
                return main.main()
            else:
                print(f"👋 يمكنك تشغيل التطبيق لاحقاً بالأمر: python main.py")
                return 0
                
        except KeyboardInterrupt:
            print(f"\n👋 تم إلغاء التشغيل")
            return 0
    else:
        print(f"❌ يرجى حل المشاكل المذكورة أعلاه قبل تشغيل التطبيق")
        return 1

if __name__ == "__main__":
    sys.exit(main())
