"""
عميل Google Cloud Video Intelligence للذكاء الاصطناعي
Google Cloud Video Intelligence AI client
"""

import logging
import json
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path
import base64

from config.settings import AISettings

logger = logging.getLogger(__name__)

# محاولة استيراد مكتبات Google Cloud
try:
    from google.cloud import videointelligence
    from google.cloud import speech
    from google.cloud import translate_v2 as translate
    from google.oauth2 import service_account
    GOOGLE_CLOUD_AVAILABLE = True
except ImportError:
    GOOGLE_CLOUD_AVAILABLE = False
    logger.warning("مكتبات Google Cloud غير متوفرة. سيتم تعطيل الميزات المتعلقة بها.")

class GoogleCloudClient:
    """عميل للتفاعل مع Google Cloud APIs"""
    
    def __init__(self, credentials_path: Optional[str] = None, project_id: Optional[str] = None):
        self.project_id = project_id or AISettings.GOOGLE_CLOUD_PROJECT_ID
        self.credentials_path = credentials_path or AISettings.GOOGLE_CLOUD_CREDENTIALS_PATH
        
        self.video_client = None
        self.speech_client = None
        self.translate_client = None
        
        if not GOOGLE_CLOUD_AVAILABLE:
            logger.error("مكتبات Google Cloud غير متوفرة")
            return
        
        # تهيئة العملاء
        self._initialize_clients()

        # التحقق من صحة الاعتمادات عند بدء التشغيل
        self._validate_credentials_on_startup()
    
    def _initialize_clients(self):
        """تهيئة عملاء Google Cloud"""
        try:
            if self.credentials_path and Path(self.credentials_path).exists():
                # استخدام ملف الاعتمادات
                credentials = service_account.Credentials.from_service_account_file(
                    self.credentials_path
                )
                
                self.video_client = videointelligence.VideoIntelligenceServiceClient(
                    credentials=credentials
                )
                self.speech_client = speech.SpeechClient(credentials=credentials)
                self.translate_client = translate.Client(credentials=credentials)
                
            else:
                # استخدام الاعتمادات الافتراضية
                self.video_client = videointelligence.VideoIntelligenceServiceClient()
                self.speech_client = speech.SpeechClient()
                self.translate_client = translate.Client()
            
            logger.info("تم تهيئة عملاء Google Cloud بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة عملاء Google Cloud: {e}")
    
    def is_available(self) -> bool:
        """التحقق من توفر الخدمة"""
        return GOOGLE_CLOUD_AVAILABLE and self.video_client is not None

    def _validate_credentials_on_startup(self):
        """التحقق من صحة بيانات الاعتماد عند بدء التشغيل"""
        if not GOOGLE_CLOUD_AVAILABLE:
            logger.warning("⚠️ مكتبات Google Cloud غير مثبتة")
            return

        if not self.is_available():
            logger.error("❌ فشل في تهيئة عملاء Google Cloud")
            if self.credentials_path:
                if not Path(self.credentials_path).exists():
                    logger.error(f"❌ ملف الاعتمادات غير موجود: {self.credentials_path}")
                else:
                    logger.error("❌ ملف الاعتمادات غير صالح أو تالف")
            else:
                logger.error("❌ لم يتم تحديد ملف الاعتمادات أو الاعتمادات الافتراضية غير متوفرة")
            return

        try:
            # اختبار بسيط للتحقق من صحة الاعتمادات
            # محاولة الحصول على قائمة العمليات (لا يتطلب موارد)
            if self.video_client:
                # اختبار بسيط للتحقق من الاتصال
                logger.info("✅ تم التحقق من صحة اعتمادات Google Cloud Video Intelligence")

            if self.speech_client:
                logger.info("✅ تم التحقق من صحة اعتمادات Google Cloud Speech")

            if self.translate_client:
                logger.info("✅ تم التحقق من صحة اعتمادات Google Cloud Translate")

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من اعتمادات Google Cloud: {e}")
            # إعادة تعيين العملاء في حالة الخطأ
            self.video_client = None
            self.speech_client = None
            self.translate_client = None

    def get_credentials_status(self) -> Dict[str, Any]:
        """الحصول على حالة بيانات الاعتماد"""
        return {
            "google_cloud_available": GOOGLE_CLOUD_AVAILABLE,
            "credentials_path": self.credentials_path,
            "credentials_file_exists": Path(self.credentials_path).exists() if self.credentials_path else False,
            "project_id": self.project_id,
            "video_client_available": self.video_client is not None,
            "speech_client_available": self.speech_client is not None,
            "translate_client_available": self.translate_client is not None
        }
    
    def detect_faces_in_video(self, video_path: str) -> Optional[List[Dict]]:
        """كشف الوجوه في الفيديو"""
        if not self.is_available():
            logger.error("خدمة Google Cloud غير متوفرة")
            return None
        
        try:
            # قراءة الفيديو
            with open(video_path, "rb") as video_file:
                input_content = video_file.read()
            
            # إعداد الطلب
            features = [videointelligence.Feature.FACE_DETECTION]
            
            operation = self.video_client.annotate_video(
                request={
                    "features": features,
                    "input_content": input_content,
                }
            )
            
            logger.info("جاري تحليل الوجوه في الفيديو...")
            result = operation.result(timeout=300)  # انتظار 5 دقائق
            
            faces_data = []
            
            # معالجة النتائج
            for annotation_result in result.annotation_results:
                for face_detection in annotation_result.face_detection_annotations:
                    for track in face_detection.tracks:
                        face_info = {
                            "confidence": track.confidence,
                            "segments": []
                        }
                        
                        for segment in track.segment:
                            segment_info = {
                                "start_time": segment.start_time_offset.total_seconds(),
                                "end_time": segment.end_time_offset.total_seconds()
                            }
                            face_info["segments"].append(segment_info)
                        
                        # إضافة معلومات الإطارات
                        face_info["frames"] = []
                        for timestamped_object in track.timestamped_objects:
                            frame_info = {
                                "time": timestamped_object.time_offset.total_seconds(),
                                "bounding_box": {
                                    "left": timestamped_object.normalized_bounding_box.left,
                                    "top": timestamped_object.normalized_bounding_box.top,
                                    "right": timestamped_object.normalized_bounding_box.right,
                                    "bottom": timestamped_object.normalized_bounding_box.bottom
                                }
                            }
                            face_info["frames"].append(frame_info)
                        
                        faces_data.append(face_info)
            
            logger.info(f"تم كشف {len(faces_data)} وجه في الفيديو")
            return faces_data
            
        except Exception as e:
            logger.error(f"خطأ في كشف الوجوه: {e}")
            return None
    
    def detect_objects_in_video(self, video_path: str) -> Optional[List[Dict]]:
        """كشف الكائنات في الفيديو"""
        if not self.is_available():
            logger.error("خدمة Google Cloud غير متوفرة")
            return None
        
        try:
            with open(video_path, "rb") as video_file:
                input_content = video_file.read()
            
            features = [videointelligence.Feature.OBJECT_TRACKING]
            
            operation = self.video_client.annotate_video(
                request={
                    "features": features,
                    "input_content": input_content,
                }
            )
            
            logger.info("جاري تحليل الكائنات في الفيديو...")
            result = operation.result(timeout=300)
            
            objects_data = []
            
            for annotation_result in result.annotation_results:
                for object_annotation in annotation_result.object_annotations:
                    object_info = {
                        "entity": object_annotation.entity.description,
                        "confidence": object_annotation.confidence,
                        "segments": []
                    }
                    
                    for segment in object_annotation.segment:
                        segment_info = {
                            "start_time": segment.start_time_offset.total_seconds(),
                            "end_time": segment.end_time_offset.total_seconds()
                        }
                        object_info["segments"].append(segment_info)
                    
                    objects_data.append(object_info)
            
            logger.info(f"تم كشف {len(objects_data)} كائن في الفيديو")
            return objects_data
            
        except Exception as e:
            logger.error(f"خطأ في كشف الكائنات: {e}")
            return None
    
    def analyze_video_content(self, video_path: str) -> Optional[Dict]:
        """تحليل محتوى الفيديو الشامل"""
        if not self.is_available():
            logger.error("خدمة Google Cloud غير متوفرة")
            return None
        
        try:
            with open(video_path, "rb") as video_file:
                input_content = video_file.read()
            
            # تحليل شامل
            features = [
                videointelligence.Feature.LABEL_DETECTION,
                videointelligence.Feature.SHOT_CHANGE_DETECTION,
                videointelligence.Feature.EXPLICIT_CONTENT_DETECTION
            ]
            
            operation = self.video_client.annotate_video(
                request={
                    "features": features,
                    "input_content": input_content,
                }
            )
            
            logger.info("جاري التحليل الشامل للفيديو...")
            result = operation.result(timeout=300)
            
            analysis_data = {
                "labels": [],
                "shots": [],
                "explicit_content": []
            }
            
            for annotation_result in result.annotation_results:
                # التسميات
                for label in annotation_result.segment_label_annotations:
                    label_info = {
                        "description": label.entity.description,
                        "confidence": label.segments[0].confidence if label.segments else 0,
                        "segments": [
                            {
                                "start_time": segment.segment.start_time_offset.total_seconds(),
                                "end_time": segment.segment.end_time_offset.total_seconds()
                            }
                            for segment in label.segments
                        ]
                    }
                    analysis_data["labels"].append(label_info)
                
                # تغييرات المشاهد
                for shot in annotation_result.shot_annotations:
                    shot_info = {
                        "start_time": shot.start_time_offset.total_seconds(),
                        "end_time": shot.end_time_offset.total_seconds()
                    }
                    analysis_data["shots"].append(shot_info)
                
                # المحتوى الصريح
                for frame in annotation_result.explicit_annotation.frames:
                    explicit_info = {
                        "time": frame.time_offset.total_seconds(),
                        "pornography_likelihood": frame.pornography_likelihood.name,
                        "violence_likelihood": frame.violence_likelihood.name
                    }
                    analysis_data["explicit_content"].append(explicit_info)
            
            logger.info("تم التحليل الشامل للفيديو بنجاح")
            return analysis_data
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الشامل: {e}")
            return None
    
    def transcribe_audio(self, audio_path: str, language_code: str = "ar-SA") -> Optional[str]:
        """تحويل الصوت إلى نص"""
        if not self.is_available():
            logger.error("خدمة Google Cloud غير متوفرة")
            return None
        
        try:
            with open(audio_path, "rb") as audio_file:
                content = audio_file.read()
            
            audio = speech.RecognitionAudio(content=content)
            config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=16000,
                language_code=language_code,
                enable_automatic_punctuation=True,
                enable_word_time_offsets=True
            )
            
            response = self.speech_client.recognize(config=config, audio=audio)
            
            transcript = ""
            for result in response.results:
                transcript += result.alternatives[0].transcript + " "
            
            logger.info("تم تحويل الصوت إلى نص بنجاح")
            return transcript.strip()
            
        except Exception as e:
            logger.error(f"خطأ في تحويل الصوت إلى نص: {e}")
            return None
    
    def translate_text_google(self, text: str, target_language: str = "ar") -> Optional[str]:
        """ترجمة النص باستخدام Google Translate"""
        if not self.is_available():
            logger.error("خدمة Google Cloud غير متوفرة")
            return None
        
        try:
            if not text.strip():
                return ""
            
            result = self.translate_client.translate(
                text, target_language=target_language
            )
            
            translated_text = result["translatedText"]
            logger.info("تم ترجمة النص بنجاح")
            return translated_text
            
        except Exception as e:
            logger.error(f"خطأ في ترجمة النص: {e}")
            return None
    
    def get_supported_languages(self) -> Optional[List[Dict]]:
        """الحصول على قائمة اللغات المدعومة"""
        if not self.is_available():
            return None
        
        try:
            results = self.translate_client.get_languages()
            
            languages = []
            for language in results:
                languages.append({
                    "code": language["language"],
                    "name": language.get("name", language["language"])
                })
            
            return languages
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على اللغات المدعومة: {e}")
            return None
