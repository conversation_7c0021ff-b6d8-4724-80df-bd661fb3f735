"""
محلل السرد المتقدم لإنشاء مقاطع شورتس مترابطة
Advanced Narrative Analyzer for Creating Coherent Shorts
"""

import logging
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json
import re
from datetime import timedelta

from ai.gemini_client import GeminiClient
from ai.huggingface_client import HuggingFaceClient
from core.video_processor import VideoProcessor

logger = logging.getLogger(__name__)

class EventType(Enum):
    """أنواع الأحداث في السرد"""
    INTRODUCTION = "introduction"      # مقدمة
    SETUP = "setup"                   # إعداد
    BUILDUP = "buildup"               # تصاعد
    CLIMAX = "climax"                 # ذروة
    REACTION = "reaction"             # ردة فعل
    CONCLUSION = "conclusion"         # خاتمة
    TRANSITION = "transition"         # انتقال

@dataclass
class NarrativeEvent:
    """حدث سردي في الفيديو"""
    start_time: float
    end_time: float
    event_type: EventType
    confidence: float
    description: str
    keywords: List[str]
    emotional_intensity: float
    importance_score: float
    related_objects: List[str] = None
    speaker_emotions: Dict[str, float] = None
    
    def __post_init__(self):
        if self.related_objects is None:
            self.related_objects = []
        if self.speaker_emotions is None:
            self.speaker_emotions = {}

@dataclass
class NarrativeArc:
    """قوس سردي كامل"""
    events: List[NarrativeEvent]
    total_duration: float
    story_theme: str
    coherence_score: float
    viral_potential: float
    recommended_cuts: List[Tuple[float, float]]

class NarrativeAnalyzer:
    """محلل السرد المتقدم"""
    
    def __init__(self):
        self.gemini_client = GeminiClient()
        self.hf_client = HuggingFaceClient()
        self.video_processor = VideoProcessor()
        
        # أنماط السرد الشائعة للمشاهير
        self.narrative_patterns = {
            'gaming': {
                'typical_events': [EventType.INTRODUCTION, EventType.SETUP, EventType.CLIMAX, EventType.REACTION],
                'keywords': ['game', 'play', 'level', 'win', 'lose', 'boss', 'challenge'],
                'emotional_flow': ['neutral', 'excitement', 'tension', 'joy_or_frustration']
            },
            'challenge': {
                'typical_events': [EventType.INTRODUCTION, EventType.SETUP, EventType.BUILDUP, EventType.CLIMAX, EventType.REACTION],
                'keywords': ['challenge', 'try', 'attempt', 'test', 'experiment', 'dare'],
                'emotional_flow': ['curiosity', 'preparation', 'tension', 'peak_emotion', 'relief_or_disappointment']
            },
            'reaction': {
                'typical_events': [EventType.INTRODUCTION, EventType.BUILDUP, EventType.CLIMAX, EventType.REACTION],
                'keywords': ['watch', 'react', 'see', 'look', 'omg', 'wow', 'what'],
                'emotional_flow': ['neutral', 'building_anticipation', 'shock_or_surprise', 'processing']
            },
            'tutorial': {
                'typical_events': [EventType.INTRODUCTION, EventType.SETUP, EventType.BUILDUP, EventType.CONCLUSION],
                'keywords': ['how', 'tutorial', 'learn', 'teach', 'show', 'explain', 'step'],
                'emotional_flow': ['introduction', 'explanation', 'demonstration', 'summary']
            }
        }
        
        # كلمات مفتاحية للأحداث المختلفة
        self.event_keywords = {
            EventType.INTRODUCTION: [
                'hello', 'hi', 'welcome', 'today', 'going to', 'about to', 'let\'s',
                'مرحبا', 'أهلا', 'اليوم', 'سوف', 'سنقوم', 'دعونا', 'هيا'
            ],
            EventType.SETUP: [
                'first', 'before', 'need to', 'have to', 'prepare', 'get ready', 'setup',
                'أولا', 'قبل', 'يجب', 'نحتاج', 'تحضير', 'استعداد', 'إعداد'
            ],
            EventType.BUILDUP: [
                'now', 'here we go', 'getting', 'starting', 'beginning', 'building up',
                'الآن', 'هيا بنا', 'نبدأ', 'يبدأ', 'بداية', 'تصاعد'
            ],
            EventType.CLIMAX: [
                'wow', 'omg', 'insane', 'crazy', 'unbelievable', 'peak', 'maximum', 'ultimate',
                'واو', 'لا يصدق', 'مجنون', 'رهيب', 'ذروة', 'أقصى', 'نهائي'
            ],
            EventType.REACTION: [
                'what', 'how', 'that was', 'can\'t believe', 'amazing', 'incredible',
                'ماذا', 'كيف', 'كان ذلك', 'لا أصدق', 'مذهل', 'لا يصدق'
            ],
            EventType.CONCLUSION: [
                'so', 'finally', 'end', 'conclusion', 'that\'s it', 'done', 'finished',
                'إذن', 'أخيرا', 'نهاية', 'خلاصة', 'هذا كل شيء', 'انتهى', 'انتهينا'
            ]
        }
        
        logger.info("تم تهيئة محلل السرد المتقدم")

    def analyze_narrative_structure(self, video_path: str, transcript: str = None) -> NarrativeArc:
        """تحليل البنية السردية للفيديو"""
        try:
            logger.info(f"بدء تحليل البنية السردية: {video_path}")
            
            # الحصول على معلومات الفيديو
            video_info = self.video_processor.get_video_info(video_path)
            duration = video_info['duration']
            
            # تحليل النص إذا كان متوفراً
            if not transcript:
                transcript = self._extract_transcript(video_path)
            
            # تحديد نوع المحتوى
            content_type = self._identify_content_type(transcript)
            
            # استخراج الأحداث السردية
            events = self._extract_narrative_events(transcript, duration, content_type)
            
            # تحليل التماسك السردي
            coherence_score = self._calculate_coherence_score(events)
            
            # تقييم الإمكانية الفيروسية
            viral_potential = self._calculate_viral_potential(events, content_type)
            
            # تحديد أفضل القطع للشورتس
            recommended_cuts = self._recommend_shorts_cuts(events, duration)
            
            # تحديد موضوع القصة
            story_theme = self._identify_story_theme(transcript, events)
            
            narrative_arc = NarrativeArc(
                events=events,
                total_duration=duration,
                story_theme=story_theme,
                coherence_score=coherence_score,
                viral_potential=viral_potential,
                recommended_cuts=recommended_cuts
            )
            
            logger.info(f"تم تحليل السرد بنجاح - {len(events)} حدث، نقاط تماسك: {coherence_score:.2f}")
            return narrative_arc
            
        except Exception as e:
            logger.error(f"خطأ في تحليل البنية السردية: {e}")
            raise

    def _extract_transcript(self, video_path: str) -> str:
        """استخراج النص من الفيديو"""
        try:
            # محاولة استخدام Gemini لتحليل الفيديو
            try:
                if hasattr(self, 'gemini_client') and self.gemini_client:
                    # محاولة تحليل بسيط للفيديو
                    result = self.gemini_client.analyze_video_content(video_path)
                    if result and 'transcript' in result:
                        return result['transcript']
            except Exception as gemini_error:
                logger.warning(f"فشل في استخدام Gemini لاستخراج النص: {gemini_error}")

            # إذا فشل Gemini، استخدم نص افتراضي بناءً على اسم الملف
            import os
            filename = os.path.basename(video_path).lower()

            # تحليل اسم الملف لاستخراج معلومات
            default_transcript = self._generate_default_transcript(filename)

            if default_transcript:
                logger.info("تم استخدام نص افتراضي بناءً على اسم الملف")
                return default_transcript

            logger.warning("لم يتم العثور على نص، سيتم استخدام التحليل البصري فقط")
            return ""

        except Exception as e:
            logger.error(f"خطأ في استخراج النص: {e}")
            return ""

    def _generate_default_transcript(self, filename: str) -> str:
        """توليد نص افتراضي بناءً على اسم الملف"""
        try:
            # كلمات مفتاحية شائعة في أسماء الملفات
            keywords_map = {
                'speed': 'IShowSpeed is playing and reacting',
                'ishowspeed': 'IShowSpeed is streaming and having fun',
                'reaction': 'watching and reacting to content',
                'gaming': 'playing games and having reactions',
                'funny': 'funny moments and laughter',
                'challenge': 'taking on a challenge',
                'live': 'live streaming content',
                'stream': 'streaming and interacting with viewers',
                'highlight': 'best moments and highlights',
                'compilation': 'compilation of exciting moments'
            }

            # البحث عن كلمات مفتاحية في اسم الملف
            found_keywords = []
            for keyword, description in keywords_map.items():
                if keyword in filename:
                    found_keywords.append(description)

            if found_keywords:
                # إنشاء نص افتراضي
                transcript = "Hello everyone! Today we have some exciting content. "
                transcript += " ".join(found_keywords)
                transcript += " This is going to be amazing! Let's see what happens. "
                transcript += "Wow, this is incredible! What a moment! "
                transcript += "That was absolutely insane! Thanks for watching!"

                return transcript

            return ""

        except Exception as e:
            logger.error(f"خطأ في توليد النص الافتراضي: {e}")
            return ""

    def _identify_content_type(self, transcript: str) -> str:
        """تحديد نوع المحتوى بناءً على النص"""
        try:
            transcript_lower = transcript.lower()
            
            # حساب نقاط لكل نوع محتوى
            scores = {}
            for content_type, pattern in self.narrative_patterns.items():
                score = 0
                for keyword in pattern['keywords']:
                    score += transcript_lower.count(keyword.lower())
                scores[content_type] = score
            
            # اختيار النوع الأعلى نقاطاً
            if scores:
                best_type = max(scores, key=scores.get)
                if scores[best_type] > 0:
                    return best_type
            
            return 'general'  # نوع عام إذا لم يتم تحديد نوع محدد
            
        except Exception as e:
            logger.error(f"خطأ في تحديد نوع المحتوى: {e}")
            return 'general'

    def _extract_narrative_events(self, transcript: str, duration: float, content_type: str) -> List[NarrativeEvent]:
        """استخراج الأحداث السردية من النص"""
        try:
            events = []
            
            if not transcript:
                # إذا لم يكن هناك نص، نقوم بتقسيم الفيديو إلى أجزاء افتراضية
                return self._create_default_events(duration, content_type)
            
            # تقسيم النص إلى جمل
            sentences = re.split(r'[.!?]+', transcript)
            sentence_duration = duration / len(sentences) if sentences else duration
            
            current_time = 0
            for i, sentence in enumerate(sentences):
                if not sentence.strip():
                    continue
                
                # تحديد نوع الحدث بناءً على الكلمات المفتاحية
                event_type = self._classify_sentence_event_type(sentence)
                
                # حساب الكثافة العاطفية
                emotional_intensity = self._calculate_emotional_intensity(sentence)
                
                # حساب درجة الأهمية
                importance_score = self._calculate_importance_score(sentence, event_type)
                
                # استخراج الكلمات المفتاحية
                keywords = self._extract_keywords(sentence)
                
                event = NarrativeEvent(
                    start_time=current_time,
                    end_time=current_time + sentence_duration,
                    event_type=event_type,
                    confidence=0.7,  # ثقة افتراضية
                    description=sentence.strip(),
                    keywords=keywords,
                    emotional_intensity=emotional_intensity,
                    importance_score=importance_score
                )
                
                events.append(event)
                current_time += sentence_duration
            
            # تحسين الأحداث وتجميعها
            events = self._refine_events(events, content_type)
            
            return events
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الأحداث السردية: {e}")
            return []

    def _create_default_events(self, duration: float, content_type: str) -> List[NarrativeEvent]:
        """إنشاء أحداث افتراضية عند عدم وجود نص"""
        try:
            pattern = self.narrative_patterns.get(content_type, self.narrative_patterns['gaming'])
            typical_events = pattern['typical_events']
            
            events = []
            event_duration = duration / len(typical_events)
            
            for i, event_type in enumerate(typical_events):
                start_time = i * event_duration
                end_time = (i + 1) * event_duration
                
                event = NarrativeEvent(
                    start_time=start_time,
                    end_time=end_time,
                    event_type=event_type,
                    confidence=0.5,  # ثقة منخفضة للأحداث الافتراضية
                    description=f"Default {event_type.value} segment",
                    keywords=[],
                    emotional_intensity=0.5,
                    importance_score=0.6
                )
                
                events.append(event)
            
            return events
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الأحداث الافتراضية: {e}")
            return []

    def _classify_sentence_event_type(self, sentence: str) -> EventType:
        """تصنيف نوع الحدث بناءً على الجملة"""
        try:
            sentence_lower = sentence.lower()
            
            # حساب نقاط لكل نوع حدث
            scores = {}
            for event_type, keywords in self.event_keywords.items():
                score = 0
                for keyword in keywords:
                    if keyword.lower() in sentence_lower:
                        score += 1
                scores[event_type] = score
            
            # اختيار النوع الأعلى نقاطاً
            if scores:
                best_type = max(scores, key=scores.get)
                if scores[best_type] > 0:
                    return best_type
            
            # تصنيف افتراضي بناءً على الموقع في النص
            return EventType.BUILDUP
            
        except Exception as e:
            logger.error(f"خطأ في تصنيف نوع الحدث: {e}")
            return EventType.BUILDUP

    def _calculate_emotional_intensity(self, text: str) -> float:
        """حساب الكثافة العاطفية للنص"""
        try:
            # كلمات عالية الكثافة
            high_intensity_words = [
                'wow', 'omg', 'insane', 'crazy', 'unbelievable', 'amazing', 'incredible',
                'واو', 'لا يصدق', 'مجنون', 'رهيب', 'مذهل'
            ]
            
            # كلمات متوسطة الكثافة
            medium_intensity_words = [
                'good', 'nice', 'cool', 'interesting', 'fun',
                'جيد', 'لطيف', 'ممتع', 'مثير للاهتمام'
            ]
            
            text_lower = text.lower()
            intensity = 0.0
            
            for word in high_intensity_words:
                intensity += text_lower.count(word.lower()) * 0.8
            
            for word in medium_intensity_words:
                intensity += text_lower.count(word.lower()) * 0.4
            
            # تطبيع النتيجة
            return min(intensity / 3.0, 1.0)
            
        except Exception as e:
            logger.error(f"خطأ في حساب الكثافة العاطفية: {e}")
            return 0.5

    def _calculate_importance_score(self, text: str, event_type: EventType) -> float:
        """حساب درجة أهمية النص"""
        try:
            base_score = 0.5
            
            # زيادة النقاط للأحداث المهمة
            if event_type in [EventType.CLIMAX, EventType.REACTION]:
                base_score += 0.3
            elif event_type in [EventType.INTRODUCTION, EventType.CONCLUSION]:
                base_score += 0.2
            
            # زيادة النقاط للنصوص الطويلة (تحتوي على معلومات أكثر)
            word_count = len(text.split())
            if word_count > 10:
                base_score += 0.1
            
            return min(base_score, 1.0)
            
        except Exception as e:
            logger.error(f"خطأ في حساب درجة الأهمية: {e}")
            return 0.5

    def _extract_keywords(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية من النص"""
        try:
            # كلمات مفتاحية مهمة
            important_words = []
            words = text.lower().split()
            
            # كلمات يجب تجاهلها
            stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
            
            for word in words:
                # تنظيف الكلمة
                clean_word = re.sub(r'[^\w]', '', word)
                if len(clean_word) > 2 and clean_word not in stop_words:
                    important_words.append(clean_word)
            
            return important_words[:5]  # أهم 5 كلمات
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الكلمات المفتاحية: {e}")
            return []

    def _refine_events(self, events: List[NarrativeEvent], content_type: str) -> List[NarrativeEvent]:
        """تحسين وتجميع الأحداث المتشابهة"""
        try:
            if not events:
                return events

            refined_events = []
            current_event = events[0]

            for next_event in events[1:]:
                # دمج الأحداث المتشابهة والمتتالية
                if (current_event.event_type == next_event.event_type and
                    next_event.start_time - current_event.end_time < 5.0):  # أقل من 5 ثوان

                    # دمج الأحداث
                    current_event.end_time = next_event.end_time
                    current_event.description += " " + next_event.description
                    current_event.keywords.extend(next_event.keywords)
                    current_event.emotional_intensity = max(current_event.emotional_intensity,
                                                           next_event.emotional_intensity)
                    current_event.importance_score = max(current_event.importance_score,
                                                        next_event.importance_score)
                else:
                    refined_events.append(current_event)
                    current_event = next_event

            refined_events.append(current_event)
            return refined_events

        except Exception as e:
            logger.error(f"خطأ في تحسين الأحداث: {e}")
            return events

    def _calculate_coherence_score(self, events: List[NarrativeEvent]) -> float:
        """حساب درجة التماسك السردي"""
        try:
            if not events:
                return 0.0

            coherence_score = 0.0

            # فحص التسلسل المنطقي للأحداث
            expected_sequence = [EventType.INTRODUCTION, EventType.SETUP, EventType.BUILDUP,
                               EventType.CLIMAX, EventType.REACTION, EventType.CONCLUSION]

            event_types = [event.event_type for event in events]

            # حساب نقاط التسلسل
            sequence_score = 0.0
            for i, expected_type in enumerate(expected_sequence):
                if expected_type in event_types:
                    # موقع الحدث في التسلسل الفعلي
                    actual_position = event_types.index(expected_type)
                    expected_position = i

                    # كلما كان الموقع أقرب للمتوقع، كانت النقاط أعلى
                    position_score = 1.0 - abs(actual_position - expected_position) / len(events)
                    sequence_score += position_score

            sequence_score /= len(expected_sequence)

            # حساب نقاط التنوع (وجود أنواع مختلفة من الأحداث)
            unique_types = len(set(event_types))
            diversity_score = unique_types / len(EventType)

            # حساب نقاط التوازن (توزيع الأحداث بشكل متوازن)
            durations = [event.end_time - event.start_time for event in events]
            avg_duration = sum(durations) / len(durations)
            balance_score = 1.0 - (max(durations) - min(durations)) / avg_duration if avg_duration > 0 else 0.5
            balance_score = max(0.0, min(1.0, balance_score))

            # الدرجة النهائية
            coherence_score = (sequence_score * 0.5 + diversity_score * 0.3 + balance_score * 0.2)

            return coherence_score

        except Exception as e:
            logger.error(f"خطأ في حساب درجة التماسك: {e}")
            return 0.5

    def _calculate_viral_potential(self, events: List[NarrativeEvent], content_type: str) -> float:
        """حساب الإمكانية الفيروسية للمحتوى"""
        try:
            if not events:
                return 0.0

            viral_score = 0.0

            # عوامل الإمكانية الفيروسية

            # 1. وجود لحظات ذروة عالية الكثافة
            climax_events = [e for e in events if e.event_type == EventType.CLIMAX]
            if climax_events:
                max_intensity = max(e.emotional_intensity for e in climax_events)
                viral_score += max_intensity * 0.4

            # 2. وجود ردود فعل قوية
            reaction_events = [e for e in events if e.event_type == EventType.REACTION]
            if reaction_events:
                avg_reaction_intensity = sum(e.emotional_intensity for e in reaction_events) / len(reaction_events)
                viral_score += avg_reaction_intensity * 0.3

            # 3. التنوع في الأحداث
            event_types = set(e.event_type for e in events)
            diversity_bonus = len(event_types) / len(EventType) * 0.2
            viral_score += diversity_bonus

            # 4. مكافأة خاصة لأنواع المحتوى الفيروسي
            if content_type in ['challenge', 'reaction']:
                viral_score += 0.1

            return min(viral_score, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب الإمكانية الفيروسية: {e}")
            return 0.5

    def _recommend_shorts_cuts(self, events: List[NarrativeEvent], total_duration: float) -> List[Tuple[float, float]]:
        """اقتراح أفضل القطع لمقاطع الشورتس"""
        try:
            if not events:
                return []

            recommended_cuts = []
            max_shorts_duration = 60.0  # 60 ثانية كحد أقصى

            # البحث عن أفضل تسلسل للأحداث
            best_sequences = self._find_best_event_sequences(events, max_shorts_duration)

            for sequence in best_sequences:
                if sequence:
                    start_time = sequence[0].start_time
                    end_time = sequence[-1].end_time

                    # التأكد من أن المدة لا تتجاوز الحد الأقصى
                    if end_time - start_time <= max_shorts_duration:
                        recommended_cuts.append((start_time, end_time))
                    else:
                        # تقليم التسلسل ليناسب الحد الأقصى
                        trimmed_end = start_time + max_shorts_duration
                        recommended_cuts.append((start_time, trimmed_end))

            # ترتيب القطع حسب الجودة المتوقعة
            recommended_cuts.sort(key=lambda cut: self._evaluate_cut_quality(cut, events), reverse=True)

            return recommended_cuts[:5]  # أفضل 5 قطع

        except Exception as e:
            logger.error(f"خطأ في اقتراح قطع الشورتس: {e}")
            return []

    def _find_best_event_sequences(self, events: List[NarrativeEvent], max_duration: float) -> List[List[NarrativeEvent]]:
        """العثور على أفضل تسلسلات الأحداث"""
        try:
            sequences = []

            # البحث عن تسلسلات تحتوي على ذروة
            for i, event in enumerate(events):
                if event.event_type == EventType.CLIMAX:
                    # بناء تسلسل حول الذروة
                    sequence = self._build_sequence_around_climax(events, i, max_duration)
                    if sequence:
                        sequences.append(sequence)

            # البحث عن تسلسلات أخرى مثيرة للاهتمام
            for i, event in enumerate(events):
                if event.importance_score > 0.7:
                    sequence = self._build_sequence_around_event(events, i, max_duration)
                    if sequence and sequence not in sequences:
                        sequences.append(sequence)

            return sequences

        except Exception as e:
            logger.error(f"خطأ في العثور على تسلسلات الأحداث: {e}")
            return []

    def _build_sequence_around_climax(self, events: List[NarrativeEvent], climax_index: int, max_duration: float) -> List[NarrativeEvent]:
        """بناء تسلسل حول حدث الذروة"""
        try:
            climax_event = events[climax_index]
            sequence = [climax_event]
            current_duration = climax_event.end_time - climax_event.start_time

            # إضافة أحداث قبل الذروة (إعداد)
            for i in range(climax_index - 1, -1, -1):
                event = events[i]
                event_duration = event.end_time - event.start_time

                if current_duration + event_duration <= max_duration:
                    sequence.insert(0, event)
                    current_duration += event_duration
                else:
                    break

            # إضافة أحداث بعد الذروة (ردة فعل)
            for i in range(climax_index + 1, len(events)):
                event = events[i]
                event_duration = event.end_time - event.start_time

                if current_duration + event_duration <= max_duration:
                    sequence.append(event)
                    current_duration += event_duration
                else:
                    break

            return sequence

        except Exception as e:
            logger.error(f"خطأ في بناء تسلسل حول الذروة: {e}")
            return []

    def _build_sequence_around_event(self, events: List[NarrativeEvent], event_index: int, max_duration: float) -> List[NarrativeEvent]:
        """بناء تسلسل حول حدث مهم"""
        try:
            main_event = events[event_index]
            sequence = [main_event]
            current_duration = main_event.end_time - main_event.start_time

            # إضافة أحداث مجاورة
            left_index = event_index - 1
            right_index = event_index + 1

            while (left_index >= 0 or right_index < len(events)) and current_duration < max_duration:
                # اختيار الحدث الأكثر أهمية من الجانبين
                left_event = events[left_index] if left_index >= 0 else None
                right_event = events[right_index] if right_index < len(events) else None

                chosen_event = None
                chosen_side = None

                if left_event and right_event:
                    if left_event.importance_score >= right_event.importance_score:
                        chosen_event = left_event
                        chosen_side = 'left'
                    else:
                        chosen_event = right_event
                        chosen_side = 'right'
                elif left_event:
                    chosen_event = left_event
                    chosen_side = 'left'
                elif right_event:
                    chosen_event = right_event
                    chosen_side = 'right'
                else:
                    break

                event_duration = chosen_event.end_time - chosen_event.start_time
                if current_duration + event_duration <= max_duration:
                    if chosen_side == 'left':
                        sequence.insert(0, chosen_event)
                        left_index -= 1
                    else:
                        sequence.append(chosen_event)
                        right_index += 1
                    current_duration += event_duration
                else:
                    break

            return sequence

        except Exception as e:
            logger.error(f"خطأ في بناء تسلسل حول الحدث: {e}")
            return []

    def _evaluate_cut_quality(self, cut: Tuple[float, float], events: List[NarrativeEvent]) -> float:
        """تقييم جودة القطع المقترح"""
        try:
            start_time, end_time = cut

            # العثور على الأحداث في هذا القطع
            cut_events = [e for e in events if e.start_time >= start_time and e.end_time <= end_time]

            if not cut_events:
                return 0.0

            # حساب متوسط الأهمية
            avg_importance = sum(e.importance_score for e in cut_events) / len(cut_events)

            # حساب متوسط الكثافة العاطفية
            avg_intensity = sum(e.emotional_intensity for e in cut_events) / len(cut_events)

            # مكافأة للتنوع في أنواع الأحداث
            unique_types = len(set(e.event_type for e in cut_events))
            diversity_bonus = unique_types / len(EventType)

            # الدرجة النهائية
            quality_score = (avg_importance * 0.4 + avg_intensity * 0.4 + diversity_bonus * 0.2)

            return quality_score

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة القطع: {e}")
            return 0.0

    def _identify_story_theme(self, transcript: str, events: List[NarrativeEvent]) -> str:
        """تحديد موضوع القصة الرئيسي"""
        try:
            if not transcript and not events:
                return "Unknown"

            # تحليل الكلمات المفتاحية من الأحداث
            all_keywords = []
            for event in events:
                all_keywords.extend(event.keywords)

            # تحليل النص
            if transcript:
                words = transcript.lower().split()
                all_keywords.extend(words)

            # تحديد الموضوعات الشائعة
            keyword_counts = {}
            for keyword in all_keywords:
                keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1

            # اختيار أكثر الكلمات تكراراً
            if keyword_counts:
                most_common = max(keyword_counts, key=keyword_counts.get)
                return most_common.capitalize()

            return "General"

        except Exception as e:
            logger.error(f"خطأ في تحديد موضوع القصة: {e}")
            return "Unknown"
